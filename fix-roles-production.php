<?php

/**
 * Production Role Fix Script
 * Run this script directly to fix user role assignments
 * 
 * Usage: php fix-roles-production.php [--dry-run]
 */

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "Error: Please run this script from the Laravel root directory.\n";
    exit(1);
}

// Load <PERSON>
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use Spatie\Permission\Models\Role;

// Check for dry-run flag
$dryRun = in_array('--dry-run', $argv);

if ($dryRun) {
    echo "=== DRY RUN MODE - No changes will be made ===\n\n";
}

echo "Starting production user role fix...\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // Step 1: Ensure roles exist
    echo "1. Ensuring roles exist...\n";
    $roles = ['admin', 'manager', 'provider', 'patient'];
    
    foreach ($roles as $roleName) {
        $role = Role::where('name', $roleName)->where('guard_name', 'web')->first();
        if (!$role) {
            if ($dryRun) {
                echo "   Would create role: {$roleName}\n";
            } else {
                Role::create(['name' => $roleName, 'guard_name' => 'web']);
                echo "   ✓ Created role: {$roleName}\n";
            }
        } else {
            echo "   ✓ Role exists: {$roleName}\n";
        }
    }

    // Step 2: Fix users without Spatie roles
    echo "\n2. Checking users without Spatie roles...\n";
    $usersWithoutRoles = User::whereDoesntHave('roles')->get();
    echo "   Found {$usersWithoutRoles->count()} users without Spatie roles\n";

    if ($usersWithoutRoles->count() > 0) {
        foreach ($usersWithoutRoles as $user) {
            $roleToAssign = $user->role ?: 'patient';
            
            if ($dryRun) {
                echo "   Would assign '{$roleToAssign}' role to: {$user->email}\n";
            } else {
                try {
                    $user->assignRole($roleToAssign);
                    echo "   ✓ Assigned '{$roleToAssign}' role to: {$user->email}\n";
                } catch (\Exception $e) {
                    echo "   ✗ Failed to assign role to {$user->email}: {$e->getMessage()}\n";
                }
            }
        }
    }

    // Step 3: Fix patients without profiles
    echo "\n3. Checking patients without profiles...\n";
    $patientsWithoutProfiles = User::where('role', 'patient')
        ->whereDoesntHave('patient')
        ->get();
    echo "   Found {$patientsWithoutProfiles->count()} patients without profiles\n";

    if ($patientsWithoutProfiles->count() > 0) {
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first();
        }

        foreach ($patientsWithoutProfiles as $user) {
            if ($dryRun) {
                echo "   Would create patient profile for: {$user->email}\n";
            } else {
                try {
                    $user->patient()->create([
                        'user_id' => $user->id,
                        'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                        'phone' => null,
                        'address' => null,
                        'emergency_contact' => null,
                        'medical_history' => null,
                        'allergies' => null,
                        'current_medications' => null,
                    ]);

                    // Initialize user credits if not exists
                    if (!$user->credit) {
                        $user->credit()->create([
                            'balance' => 0.00,
                            'total_earned' => 0.00,
                            'total_spent' => 0.00,
                        ]);
                    }

                    echo "   ✓ Created patient profile for: {$user->email}\n";
                } catch (\Exception $e) {
                    echo "   ✗ Failed to create patient profile for {$user->email}: {$e->getMessage()}\n";
                }
            }
        }
    }

    // Step 4: Fix providers without profiles
    echo "\n4. Checking providers without profiles...\n";
    $providersWithoutProfiles = User::where('role', 'provider')
        ->whereDoesntHave('provider')
        ->get();
    echo "   Found {$providersWithoutProfiles->count()} providers without profiles\n";

    if ($providersWithoutProfiles->count() > 0) {
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first();
        }

        foreach ($providersWithoutProfiles as $user) {
            if ($dryRun) {
                echo "   Would create provider profile for: {$user->email}\n";
            } else {
                try {
                    $user->provider()->create([
                        'user_id' => $user->id,
                        'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
                        'specialization' => 'General Practice',
                        'license_number' => 'PENDING',
                        'verification_status' => 'pending',
                        'verified_at' => null,
                        'gender' => null,
                        'bio' => null,
                        'rating' => 0,
                        'weekly_availability' => [
                            ['day' => 'Monday', 'slots' => []],
                            ['day' => 'Tuesday', 'slots' => []],
                            ['day' => 'Wednesday', 'slots' => []],
                            ['day' => 'Thursday', 'slots' => []],
                            ['day' => 'Friday', 'slots' => []],
                            ['day' => 'Saturday', 'slots' => []],
                            ['day' => 'Sunday', 'slots' => []],
                        ],
                        'absences' => [],
                        'practice_locations' => [],
                    ]);

                    echo "   ✓ Created provider profile for: {$user->email}\n";
                } catch (\Exception $e) {
                    echo "   ✗ Failed to create provider profile for {$user->email}: {$e->getMessage()}\n";
                }
            }
        }
    }

    if (!$dryRun) {
        // Clear permission cache
        echo "\n5. Clearing permission cache...\n";
        \Artisan::call('permission:cache-reset');
        echo "   ✓ Permission cache cleared\n";
    }

    echo "\n=== Fix completed successfully! ===\n";
    echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";

    // Final verification
    echo "\n=== Verification ===\n";
    $usersWithoutRoles = User::whereDoesntHave('roles')->count();
    $patientsWithoutProfiles = User::where('role', 'patient')->whereDoesntHave('patient')->count();
    $providersWithoutProfiles = User::where('role', 'provider')->whereDoesntHave('provider')->count();
    
    echo "Users without Spatie roles: {$usersWithoutRoles}\n";
    echo "Patients without profiles: {$patientsWithoutProfiles}\n";
    echo "Providers without profiles: {$providersWithoutProfiles}\n";

    if ($usersWithoutRoles == 0 && $patientsWithoutProfiles == 0 && $providersWithoutProfiles == 0) {
        echo "\n✅ All issues have been resolved!\n";
    } else {
        echo "\n⚠️  Some issues remain. Please check the output above.\n";
    }

} catch (\Exception $e) {
    echo "\n❌ Error occurred: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
