<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExamModeHandler
{
    private $groqService;
    
    public function __construct(GroqService $groqService)
    {
        $this->groqService = $groqService;
    }
    
    /**
     * Process exam mode questions with specialized reasoning
     */
    public function processExamQuestion($question, $examType, $options = [])
    {
        try {
            $systemPrompt = $this->createExamSystemPrompt($examType);
            $analysisPrompt = $this->createExamAnalysisPrompt($question, $examType);
            
            $response = $this->callGroqForExam($systemPrompt, $analysisPrompt);
            
            return $this->formatExamResponse($response, $examType);
            
        } catch (\Exception $e) {
            Log::error('Exam mode processing error', [
                'exam_type' => $examType,
                'error' => $e->getMessage()
            ]);
            
            return [
                'answer' => 'Unable to process exam question',
                'reasoning' => 'System error occurred',
                'confidence' => 'low'
            ];
        }
    }
    
    private function createExamSystemPrompt($examType)
    {
        $basePrompt = "You are an expert medical educator in EXAM MODE. Your goal is to achieve the highest possible score through systematic clinical reasoning.\n\n";
        
        switch ($examType) {
            case 'usmle_step1':
                return $basePrompt . $this->getUSMLEStep1ExamPrompt();
            case 'usmle_step2_ck':
                return $basePrompt . $this->getUSMLEStep2CKExamPrompt();
            case 'usmle_step3':
                return $basePrompt . $this->getUSMLEStep3ExamPrompt();
            case 'mrcp_part1':
            case 'mrcp_part2':
                return $basePrompt . $this->getMRCPExamPrompt();
            default:
                return $basePrompt . "Focus on systematic medical reasoning.";
        }
    }
    
    private function getUSMLEStep1ExamPrompt()
    {
        return <<<EOT
**USMLE STEP 1 EXPERT MODE**

**Core Strategy:**
1. Identify the basic science mechanism first
2. Apply mechanism to pathophysiology  
3. Connect to clinical presentation
4. Eliminate based on scientific principles

**High-Yield Focus Areas:**
- Biochemistry pathways and enzyme defects
- Microbiology virulence factors and treatments
- Pathology cellular changes and mechanisms
- Pharmacology mechanisms of action and side effects
- Physiology normal function and regulation

**Answer Approach:**
- Start with "What is the fundamental mechanism?"
- Consider epidemiology and risk factors
- Apply First Aid high-yield facts
- Use Pathoma reasoning principles
- Eliminate obviously incorrect options first

**CRITICAL RESPONSE RULE:**
- NEVER mention "USMLE", "Step 1", or specific exam names in your response
- Use generic terms like "medical exam", "clinical reasoning", or "systematic analysis"
- Keep all exam-specific knowledge but use neutral terminology in responses

**Response Format Required:**
## Systematic Analysis
**Mechanism:** [Core biological process]
**Application:** [How it applies to this question]
**Elimination:** [Why wrong answers are incorrect]

## Final Answer: [Letter]
**Reasoning:** [Complete explanation]
**High-Yield Pearl:** [Key teaching point]
EOT;
    }
    
    private function getUSMLEStep2CKExamPrompt()
    {
        return <<<EOT
**USMLE STEP 2 CK EXPERT MODE**

**Clinical Reasoning Framework:**
1. **Pattern Recognition**: Identify the clinical syndrome
2. **Risk Stratification**: Assess urgency and severity
3. **Evidence-Based Approach**: Apply guidelines and protocols
4. **Safety First**: Choose safest and most appropriate option

**Question Type Strategies:**
- **"Most likely diagnosis"**: Common things are common
- **"Next best step"**: Safest and most informative
- **"Most appropriate treatment"**: Evidence-based, guideline-recommended
- **"Best initial test"**: Most sensitive and specific for diagnosis

**Clinical Priorities:**
1. Life-threatening conditions first
2. Treatable conditions next  
3. Most common diagnoses
4. Evidence-based management

**CRITICAL RESPONSE RULE:**
- NEVER mention "USMLE", "Step 2", "CK", or specific exam names in your response
- Use generic terms like "medical exam", "clinical reasoning", or "systematic analysis"
- Keep all exam-specific knowledge but use neutral terminology in responses

**Response Format Required:**
## Clinical Reasoning
**Syndrome Recognition:** [Key clinical pattern]
**Differential Ranking:** [Most to least likely]
**Next Step Analysis:** [Why this choice is optimal]

## Final Answer: [Letter]
**Clinical Justification:** [Evidence-based reasoning]
**Safety Consideration:** [Risk assessment]
EOT;
    }

    private function getUSMLEStep3ExamPrompt()
    {
        return <<<EOT
**USMLE STEP 3 EXPERT MODE - PATIENT MANAGEMENT FOCUS**

**Step 3 Unique Characteristics:**
- Focuses on PATIENT MANAGEMENT, not just diagnosis
- Tests real-world clinical decision-making
- Emphasizes follow-up care and monitoring
- Includes ambulatory and emergency settings
- Computer-based Case Simulations (CCS) component

**Core Strategy:**
1. **Patient Management**: Focus on what to DO, not just what it IS
2. **Real-World Application**: Consider practical constraints and resources
3. **Follow-up Planning**: Think about ongoing care and monitoring
4. **Safety Net**: Always include safety monitoring and red flags
5. **Cost-Effectiveness**: Consider appropriate resource utilization

**Clinical Management Framework:**
1. **Initial Management**: Most appropriate immediate actions
2. **Diagnostic Strategy**: Cost-effective workup prioritization
3. **Treatment Implementation**: Evidence-based therapeutic decisions
4. **Monitoring Plan**: How to follow up and assess response
5. **Safety Protocols**: What could go wrong and how to prevent it

**Question Type Strategies:**
- **"Most appropriate management"**: Best real-world action for this patient
- **"Next step in management"**: Logical progression of care
- **"Most important initial action"**: Safety and stabilization first
- **"Best follow-up plan"**: Appropriate monitoring and reassessment
- **"Most cost-effective approach"**: Balance quality and resource utilization

**Step 3 Priorities:**
1. **Patient Safety**: Always prioritize safety over cost or convenience
2. **Evidence-Based Care**: Use current guidelines and protocols
3. **Practical Implementation**: Consider real-world feasibility
4. **Comprehensive Management**: Think beyond the immediate problem
5. **Communication**: Include patient education and counseling

**Clinical Settings Emphasis:**
- **Ambulatory Care**: Outpatient management and follow-up
- **Emergency Department**: Acute care and disposition decisions
- **Hospital Medicine**: Inpatient management and discharge planning
- **Preventive Care**: Screening and health maintenance
- **Chronic Disease Management**: Long-term care strategies

**Computer-based Case Simulations (CCS) Approach:**
- **Time Management**: Prioritize interventions appropriately
- **Comprehensive Orders**: Include monitoring, follow-up, and safety
- **Patient Education**: Always include counseling and education
- **Disposition Planning**: Appropriate level of care decisions

**CRITICAL RESPONSE RULE:**
- NEVER mention "USMLE", "Step 3", or specific exam names in your response
- Use generic terms like "medical exam", "clinical reasoning", or "patient management"
- Keep all exam-specific knowledge but use neutral terminology in responses

**Response Format Required:**
## Patient Management Analysis
**Clinical Scenario:** [Key patient presentation and context]
**Management Priorities:** [Most important immediate actions]
**Diagnostic Strategy:** [Cost-effective and appropriate workup]
**Treatment Plan:** [Evidence-based interventions]
**Monitoring Plan:** [Follow-up and safety monitoring]

## Final Answer: [Letter]
**Management Justification:** [Why this is the best management approach]
**Safety Considerations:** [Potential complications and monitoring]
**Follow-up Strategy:** [Ongoing care and reassessment plan]

**High-Yield Step 3 Concepts:**
- Ambulatory management of chronic diseases
- Emergency department decision-making
- Cost-effective diagnostic strategies
- Patient safety and quality improvement
- Preventive care and screening guidelines
- Hospital medicine and discharge planning
EOT;
    }

    private function getMRCPExamPrompt()
    {
        return <<<EOT
**MRCP EXPERT MODE - UK CLINICAL PRACTICE**

**UK-Specific Approach:**
1. **NICE Guidelines**: Always consider current UK recommendations
2. **BNF Standards**: UK drug choices and dosing
3. **NHS Protocols**: Resource-appropriate management
4. **Royal College Guidance**: Specialty-specific UK standards

**Key UK Considerations:**
- QOF indicators for primary care quality
- DVLA medical fitness standards
- UK immunization schedules and protocols
- Mental Health Act implications
- NHS cost-effectiveness considerations

**Clinical Focus:**
- Evidence-based medicine with UK guidelines
- Multisystem complex cases
- Investigation interpretation
- Management decision-making
- Prognosis assessment

**CRITICAL RESPONSE RULE:**
- NEVER mention "MRCP", "Part 1", "Part 2", or specific exam names in your response
- Use generic terms like "UK medical exam", "clinical reasoning", or "systematic analysis"
- Keep all exam-specific knowledge but use neutral terminology in responses

**CRITICAL RESPONSE RULE:**
- NEVER mention "MRCP", "Part 1", "Part 2", or specific exam names in your response
- Use generic terms like "UK medical exam", "clinical reasoning", or "systematic analysis"
- Keep all exam-specific knowledge but use neutral terminology in responses

**Response Format Required:**
## UK Clinical Analysis
**Guideline Reference:** [Relevant NICE/Royal College guidance]
**UK Practice Standard:** [How this aligns with NHS practice]
**Investigation Strategy:** [Cost-effective UK approach]

## Final Answer: [Letter]
**UK Justification:** [Why this is best practice in UK]
**NICE/BNF Citation:** [Specific guideline reference]
EOT;
    }
    
    private function createExamAnalysisPrompt($question, $examType)
    {
        return <<<EOT
Analyze this $examType exam question using systematic clinical reasoning:

**Question:**
$question

**Instructions:**
1. Read the question carefully and identify the key clinical information
2. Determine what the question is really asking
3. Apply the appropriate reasoning framework for $examType
4. Use systematic elimination of incorrect options
5. Provide your final answer with complete reasoning

**CRITICAL RESPONSE REQUIREMENT:**
- DO NOT mention specific exam names (USMLE, MRCP, Step 1, Step 2, etc.) in your response
- Use generic terms like "medical exam", "clinical reasoning", "systematic analysis"
- Keep all exam-specific knowledge but use neutral terminology in your response

**Remember:**
- Show your step-by-step thinking process
- Eliminate wrong answers with explanations
- Choose the BEST answer among the options
- Focus on high-yield, testable concepts
- Consider the exam's typical reasoning patterns

Provide your complete analysis now:
EOT;
    }
    
    private function callGroqForExam($systemPrompt, $userPrompt)
    {
        $apiKey = config('services.groq.api_key');
        $apiUrl = config('services.groq.api_url');
        $model = config('services.groq.model');
        
        $timeout = config('services.medroid.timeouts.exam', 60);

        $response = Http::withHeaders([
            'Authorization' => "Bearer $apiKey",
            'Content-Type' => 'application/json',
        ])->timeout($timeout)
        ->post($apiUrl, [
            'model' => $model,
            'messages' => [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt]
            ],
            'temperature' => 0.1, // Low temperature for consistent exam performance
            'max_completion_tokens' => 2000,
            'top_p' => 1,
            'stream' => false,
        ]);
        
        if ($response->successful()) {
            $result = $response->json();
            return $result['choices'][0]['message']['content'] ?? '';
        }
        
        throw new \Exception('Failed to get exam response from Groq');
    }
    
    private function formatExamResponse($response, $examType)
    {
        // Extract structured information from the response
        $formatted = [
            'exam_type' => $examType,
            'full_response' => $response,
            'reasoning_steps' => $this->extractReasoningSteps($response),
            'final_answer' => $this->extractFinalAnswer($response),
            'confidence_level' => $this->extractConfidence($response),
            'high_yield_pearl' => $this->extractHighYieldPearl($response)
        ];
        
        return $formatted;
    }
    
    private function extractReasoningSteps($response)
    {
        // Parse the systematic reasoning from the response
        $steps = [];
        if (preg_match('/## Systematic Analysis(.*?)## Final Answer/s', $response, $matches)) {
            $steps[] = trim($matches[1]);
        }
        return $steps;
    }
    
    private function extractFinalAnswer($response)
    {
        // Extract the letter answer (A, B, C, D, etc.)
        if (preg_match('/## Final Answer:\s*([A-E])/i', $response, $matches)) {
            return $matches[1];
        }
        return 'Unable to determine';
    }
    
    private function extractConfidence($response)
    {
        if (stripos($response, 'high confidence') !== false) return 'high';
        if (stripos($response, 'medium confidence') !== false) return 'medium';
        if (stripos($response, 'low confidence') !== false) return 'low';
        return 'medium'; // default
    }
    
    private function extractHighYieldPearl($response)
    {
        if (preg_match('/High-Yield Pearl:\s*(.*?)$/m', $response, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }
}
