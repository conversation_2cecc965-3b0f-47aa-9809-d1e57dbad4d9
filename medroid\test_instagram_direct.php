<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\InstagramService;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔗 Direct Instagram OAuth URL Test\n\n";

try {
    $instagramService = new InstagramService();
    
    // Generate auth URL
    $state = base64_encode(json_encode([
        'user_id' => 1,
        'timestamp' => time(),
        'test' => true
    ]));
    
    $authUrl = $instagramService->getAuthorizationUrl($state);
    
    echo "✅ Generated Instagram OAuth URL:\n";
    echo $authUrl . "\n\n";
    
    echo "🌐 You can test this URL directly in your browser:\n";
    echo "1. Copy the URL above\n";
    echo "2. Paste it in your browser\n";
    echo "3. It should redirect to Instagram login\n\n";
    
    // Also test with a simpler URL without state
    $simpleUrl = $instagramService->getAuthorizationUrl();
    echo "🔗 Simple URL (without state):\n";
    echo $simpleUrl . "\n\n";
    
    echo "📋 If you get 'Invalid platform app' error, check:\n";
    echo "1. Meta Developer Console app settings\n";
    echo "2. Make sure app is in 'Live' mode\n";
    echo "3. Valid OAuth redirect URIs are configured\n";
    echo "4. App type is 'Instagram API with Instagram Login'\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
