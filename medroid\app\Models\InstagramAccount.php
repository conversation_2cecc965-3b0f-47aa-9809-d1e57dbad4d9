<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InstagramAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'instagram_user_id',
        'username',
        'access_token',
        'account_type',
        'media_count',
        'expires_at',
        'last_sync_at',
        'is_active',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'last_sync_at' => 'datetime',
        'is_active' => 'boolean',
        'media_count' => 'integer',
    ];

    protected $hidden = [
        'access_token',
    ];

    /**
     * Get the user that owns the Instagram account
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all social media posts for this Instagram account
     */
    public function socialMediaPosts(): HasMany
    {
        return $this->hasMany(SocialMediaPost::class);
    }

    /**
     * Get the social content imported from this Instagram account (for backward compatibility)
     */
    public function socialContent(): Has<PERSON>any
    {
        return $this->hasMany(SocialContent::class, 'user_id', 'user_id')
            ->where('source', 'instagram');
    }

    /**
     * Check if the access token is expired or about to expire
     */
    public function isTokenExpired(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        // Consider token expired if it expires within the next 7 days
        return $this->expires_at->isBefore(now()->addDays(7));
    }

    /**
     * Check if the account needs to be synced
     */
    public function needsSync(): bool
    {
        if (!$this->last_sync_at) {
            return true;
        }

        // Sync if last sync was more than 1 hour ago
        return $this->last_sync_at->isBefore(now()->subHour());
    }

    /**
     * Get the account type badge color
     */
    public function getAccountTypeBadgeAttribute(): string
    {
        return match ($this->account_type) {
            'BUSINESS' => 'bg-blue-100 text-blue-800',
            'CREATOR' => 'bg-purple-100 text-purple-800',
            'MEDIA_CREATOR' => 'bg-purple-100 text-purple-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the account type display name
     */
    public function getAccountTypeDisplayAttribute(): string
    {
        return match ($this->account_type) {
            'BUSINESS' => 'Business',
            'CREATOR' => 'Creator',
            'MEDIA_CREATOR' => 'Creator',
            default => 'Unknown',
        };
    }

    /**
     * Scope to get active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get accounts that need token refresh
     */
    public function scopeNeedsTokenRefresh($query)
    {
        return $query->where('expires_at', '<=', now()->addDays(7));
    }

    /**
     * Scope for accounts that need sync
     */
    public function scopeNeedsSync($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('last_sync_at')
              ->orWhere('last_sync_at', '<', now()->subHour());
        });
    }
}
