# 🌐 Ngrok Static Domain Setup for Medroid

## Current Status ✅
- ✅ Laravel server running on port 8000
- ✅ Ngrok tunnel active: `https://ae16-82-9-240-28.ngrok-free.app`
- ✅ Instagram API configured and working
- ✅ Environment variables updated

## 🎯 Setting Up Static Domain

### Step 1: Create Static Domain in ngrok Dashboard
1. Go to [ngrok Dashboard](https://dashboard.ngrok.com/cloud-edge/domains)
2. Click **"Create Domain"** or **"New Domain"**
3. Choose a domain name (suggestions):
   - `medroid-dev.ngrok-free.app`
   - `medroid-backend.ngrok-free.app`
   - `medroid-api.ngrok-free.app`
4. Save the domain

### Step 2: Update Configuration
Once you have your static domain, run:
```bash
# Update .env file with your static domain
./update-ngrok-env.sh your-static-domain.ngrok-free.app

# Start ngrok with static domain
./start-ngrok-static.sh your-static-domain.ngrok-free.app
```

### Step 3: Update Instagram App Settings
In [Facebook Developer Console](https://developers.facebook.com/apps/951526903656551/instagram-api-with-instagram-login/):

1. **Valid O<PERSON>uth Redirect URIs:**
   ```
   https://your-static-domain.ngrok-free.app/auth/instagram/callback
   ```

2. **Webhook URL:**
   ```
   https://your-static-domain.ngrok-free.app/webhooks/instagram
   ```

## 🚀 Quick Start Commands

### Start Development Environment
```bash
# Option 1: With static domain (recommended)
./start-ngrok-static.sh your-static-domain.ngrok-free.app

# Option 2: Without static domain (dynamic URL)
./start-ngrok-static.sh
```

### Update Environment for New Domain
```bash
./update-ngrok-env.sh your-new-domain.ngrok-free.app
```

## 📋 Current Configuration
- **App URL:** `https://ae16-82-9-240-28.ngrok-free.app`
- **Instagram App ID:** `951526903656551`
- **Callback URL:** `https://ae16-82-9-240-28.ngrok-free.app/auth/instagram/callback`
- **Webhook URL:** `https://ae16-82-9-240-28.ngrok-free.app/webhooks/instagram`

## 🔧 Troubleshooting

### If ngrok tunnel goes offline:
1. Restart Laravel server: `php artisan serve --port=8000`
2. Restart ngrok: `./start-ngrok-static.sh your-domain`
3. Update .env if needed: `./update-ngrok-env.sh your-domain`

### Test your setup:
```bash
# Test Laravel app
curl -I https://your-domain.ngrok-free.app

# Test Instagram integration
curl https://your-domain.ngrok-free.app/instagram-test
```

## 💡 Benefits of Static Domain
- ✅ URL never changes
- ✅ No need to update Instagram app settings
- ✅ Consistent development environment
- ✅ Easier testing and debugging
