#!/bin/bash

# Script to update .env file with new ngrok URL
# Usage: ./update-ngrok-env.sh your-ngrok-domain.ngrok-free.app

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

if [ -z "$1" ]; then
    echo -e "${RED}❌ Error: Please provide the ngrok domain${NC}"
    echo -e "${YELLOW}Usage: ./update-ngrok-env.sh your-domain.ngrok-free.app${NC}"
    exit 1
fi

NGROK_DOMAIN=$1
HTTPS_URL="https://$NGROK_DOMAIN"

echo -e "${GREEN}🔧 Updating .env file with ngrok domain: $NGROK_DOMAIN${NC}"

# Backup current .env
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
echo -e "${GREEN}✅ Backed up current .env file${NC}"

# Update APP_URL
sed -i '' "s|APP_URL=.*|APP_URL=$HTTPS_URL|g" .env
echo -e "${GREEN}✅ Updated APP_URL${NC}"

# Update SESSION_DOMAIN
sed -i '' "s|SESSION_DOMAIN=.*|SESSION_DOMAIN=$NGROK_DOMAIN|g" .env
echo -e "${GREEN}✅ Updated SESSION_DOMAIN${NC}"

# Update INSTAGRAM_REDIRECT_URI
sed -i '' "s|INSTAGRAM_REDIRECT_URI=.*|INSTAGRAM_REDIRECT_URI=$HTTPS_URL/auth/instagram/callback|g" .env
echo -e "${GREEN}✅ Updated INSTAGRAM_REDIRECT_URI${NC}"

echo -e "${GREEN}🎉 .env file updated successfully!${NC}"
echo -e "${YELLOW}📝 Don't forget to update your Instagram app settings in Facebook Developer Console:${NC}"
echo -e "${YELLOW}   Valid OAuth Redirect URIs: $HTTPS_URL/auth/instagram/callback${NC}"
echo -e "${YELLOW}   Webhook URL: $HTTPS_URL/webhooks/instagram${NC}"

# Show current configuration
echo -e "\n${GREEN}📋 Current Configuration:${NC}"
echo -e "APP_URL: $(grep APP_URL .env)"
echo -e "SESSION_DOMAIN: $(grep SESSION_DOMAIN .env)"
echo -e "INSTAGRAM_REDIRECT_URI: $(grep INSTAGRAM_REDIRECT_URI .env)"
