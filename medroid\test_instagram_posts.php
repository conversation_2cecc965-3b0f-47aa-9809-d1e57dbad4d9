<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\InstagramAccount;
use App\Services\InstagramService;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🎬 Fetching Instagram Posts for Display...\n\n";

try {
    // Get the Instagram account we just created
    $instagramAccount = InstagramAccount::where('username', 'spark_soul_shine')->first();
    
    if (!$instagramAccount) {
        echo "❌ No Instagram account found. Please run test_instagram.php first.\n";
        exit;
    }
    
    echo "📱 Account: @{$instagramAccount->username} ({$instagramAccount->account_type})\n";
    echo "📊 Total Media Count: {$instagramAccount->media_count}\n\n";
    
    // Initialize Instagram service
    $instagramService = new InstagramService();
    
    // Use reflection to access protected method
    $reflection = new ReflectionClass($instagramService);
    $method = $reflection->getMethod('getUserMedia');
    $method->setAccessible(true);
    
    // Fetch posts
    $media = $method->invoke(
        $instagramService, 
        $instagramAccount->access_token, 
        $instagramAccount->instagram_user_id, 
        10 // Get 10 posts
    );
    
    if (!$media || !isset($media['data'])) {
        echo "❌ Failed to fetch media\n";
        exit;
    }
    
    echo "🎉 Successfully fetched " . count($media['data']) . " posts!\n\n";
    echo str_repeat("=", 80) . "\n";
    
    foreach ($media['data'] as $index => $post) {
        $postNum = $index + 1;
        $mediaType = $post['media_type'] ?? 'Unknown';
        $caption = $post['caption'] ?? 'No caption';
        $timestamp = $post['timestamp'] ?? '';
        $permalink = $post['permalink'] ?? '';
        
        // Format timestamp
        $date = $timestamp ? date('M j, Y g:i A', strtotime($timestamp)) : 'Unknown date';
        
        echo "📸 POST #{$postNum}\n";
        echo "🎭 Type: {$mediaType}\n";
        echo "📅 Date: {$date}\n";
        echo "🔗 Link: {$permalink}\n";
        echo "📝 Caption: " . (strlen($caption) > 100 ? substr($caption, 0, 100) . "..." : $caption) . "\n";
        
        if ($mediaType === 'VIDEO') {
            echo "🎥 Video URL: " . ($post['media_url'] ?? 'N/A') . "\n";
            echo "🖼️ Thumbnail: " . ($post['thumbnail_url'] ?? 'N/A') . "\n";
        } else {
            echo "🖼️ Image URL: " . ($post['media_url'] ?? 'N/A') . "\n";
        }
        
        echo str_repeat("-", 80) . "\n";
    }
    
    // Show pagination info
    if (isset($media['paging']['next'])) {
        echo "\n📄 More posts available! Next page URL:\n";
        echo $media['paging']['next'] . "\n";
    }
    
    echo "\n🎯 Ready to integrate these posts into your feed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
