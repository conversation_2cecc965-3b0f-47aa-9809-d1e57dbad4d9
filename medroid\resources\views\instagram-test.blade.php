<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #E4405F;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #C13584;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .account-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .posts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .post-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .post-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .post-caption {
            padding: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Instagram Integration Test</h1>
        
        @if(session('success'))
            <div class="status success">
                ✅ {{ session('success') }}
            </div>
        @endif
        
        @if(session('error'))
            <div class="status error">
                ❌ {{ session('error') }}
            </div>
        @endif
        
        <div class="status info">
            <strong>📋 Test Instructions:</strong><br>
            1. Click "Connect Instagram" to authenticate<br>
            2. Authorize the app in Instagram<br>
            3. You'll be redirected back here<br>
            4. Your posts will be synced automatically<br>
            5. Use "Sync Content" to manually refresh
        </div>
        
        @auth
            @php
                $instagramAccount = \App\Models\InstagramAccount::where('user_id', auth()->id())
                    ->where('is_active', true)
                    ->first();
                $posts = $instagramAccount ? 
                    \App\Models\SocialMediaPost::where('instagram_account_id', $instagramAccount->id)
                        ->where('is_visible', true)
                        ->orderBy('posted_at', 'desc')
                        ->limit(12)
                        ->get() : collect();
            @endphp
            
            <div class="account-info">
                <strong>👤 Logged in as:</strong> {{ auth()->user()->name }} ({{ auth()->user()->email }})
            </div>
            
            @if($instagramAccount)
                <div class="account-info">
                    <strong>📱 Instagram Account Connected:</strong><br>
                    <strong>Username:</strong> {{ $instagramAccount->username }}<br>
                    <strong>Account Type:</strong> {{ $instagramAccount->account_type }}<br>
                    <strong>Media Count:</strong> {{ $instagramAccount->media_count }}<br>
                    <strong>Last Sync:</strong> {{ $instagramAccount->last_sync_at ? $instagramAccount->last_sync_at->diffForHumans() : 'Never' }}<br>
                    <strong>Token Expires:</strong> {{ $instagramAccount->expires_at ? $instagramAccount->expires_at->diffForHumans() : 'Unknown' }}
                </div>
                
                <form method="POST" action="{{ route('instagram.sync') }}" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn">🔄 Sync Content</button>
                </form>
                
                <form method="POST" action="{{ route('instagram.disconnect') }}" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-secondary">❌ Disconnect</button>
                </form>
                
                @if($posts->count() > 0)
                    <h3>📸 Recent Instagram Posts ({{ $posts->count() }})</h3>
                    <div class="posts-grid">
                        @foreach($posts as $post)
                            <div class="post-item">
                                @if($post->display_media_url)
                                    <img src="{{ $post->display_media_url }}" alt="Instagram Post">
                                @endif
                                <div class="post-caption">
                                    <strong>{{ $post->media_type }}</strong><br>
                                    {{ Str::limit($post->caption, 100) }}<br>
                                    <small>{{ $post->posted_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="status info">
                        📭 No posts found. Try syncing your content or post something new on Instagram!
                    </div>
                @endif
            @else
                <a href="{{ route('instagram.auth') }}" class="btn">📱 Connect Instagram</a>
            @endif
        @else
            <div class="status error">
                ⚠️ You need to be logged in to test Instagram integration.
            </div>
            <a href="{{ route('login') }}" class="btn">🔐 Login</a>
        @endauth
        
        <hr style="margin: 30px 0;">
        
        <h3>🔧 Technical Details</h3>
        <div class="account-info">
            <strong>Webhook URL:</strong> {{ url('/webhooks/instagram') }}<br>
            <strong>Callback URL:</strong> {{ url('/auth/instagram/callback') }}<br>
            <strong>App ID:</strong> {{ env('INSTAGRAM_APP_ID', 'Not configured') }}<br>
            <strong>Environment:</strong> {{ app()->environment() }}
        </div>
        
        <h3>📊 Test Webhook</h3>
        <button onclick="testWebhook()" class="btn btn-secondary">🧪 Send Test Webhook</button>
        <div id="webhook-result"></div>
        
        <script>
            async function testWebhook() {
                const result = document.getElementById('webhook-result');
                result.innerHTML = '<div class="status info">⏳ Sending test webhook...</div>';
                
                try {
                    const response = await fetch('/webhooks/instagram', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            object: 'instagram',
                            entry: [{
                                id: '123456789',
                                changes: [{
                                    field: 'media',
                                    value: {
                                        media_id: 'test_media_123'
                                    }
                                }]
                            }]
                        })
                    });
                    
                    if (response.ok) {
                        result.innerHTML = '<div class="status success">✅ Webhook test successful!</div>';
                    } else {
                        result.innerHTML = '<div class="status error">❌ Webhook test failed: ' + response.status + '</div>';
                    }
                } catch (error) {
                    result.innerHTML = '<div class="status error">❌ Webhook test error: ' + error.message + '</div>';
                }
            }
        </script>
    </div>
</body>
</html>
