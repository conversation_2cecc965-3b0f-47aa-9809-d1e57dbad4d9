<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SocialContent;
use App\Models\Story;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SocialMediaManagerController extends Controller
{
    /**
     * Display the social media management dashboard
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            // Get overview statistics
            $stats = [
                'total_posts' => SocialContent::count(),
                'pending_posts' => SocialContent::where('filtered_status', 'pending')->count(),
                'approved_posts' => SocialContent::where('filtered_status', 'approved')->count(),
                'rejected_posts' => SocialContent::where('filtered_status', 'rejected')->count(),
                'total_stories' => Story::count(),
                'active_stories' => Story::active()->count(),
                'total_users' => User::count(),
                'posts_today' => SocialContent::whereDate('created_at', today())->count(),
                'stories_today' => Story::whereDate('created_at', today())->count(),
            ];

            // Get recent activity
            $recent_posts = SocialContent::with(['user'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $recent_stories = Story::with(['user'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Get engagement metrics
            $engagement_stats = SocialContent::select(
                DB::raw('AVG(JSON_EXTRACT(engagement_metrics, "$.likes")) as avg_likes'),
                DB::raw('AVG(JSON_EXTRACT(engagement_metrics, "$.comments")) as avg_comments'),
                DB::raw('AVG(JSON_EXTRACT(engagement_metrics, "$.shares")) as avg_shares'),
                DB::raw('SUM(JSON_EXTRACT(engagement_metrics, "$.likes")) as total_likes'),
                DB::raw('SUM(JSON_EXTRACT(engagement_metrics, "$.comments")) as total_comments'),
                DB::raw('SUM(JSON_EXTRACT(engagement_metrics, "$.shares")) as total_shares')
            )->first();

            return response()->json([
                'stats' => $stats,
                'recent_posts' => $recent_posts,
                'recent_stories' => $recent_stories,
                'engagement_stats' => $engagement_stats
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load social media dashboard', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to load dashboard data'
            ], 500);
        }
    }

    /**
     * Get all posts with filtering and pagination
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getPosts(Request $request)
    {
        try {
            $query = SocialContent::with(['user']);

            // Apply filters
            if ($request->has('status') && $request->status !== 'all') {
                $query->where('filtered_status', $request->status);
            }

            if ($request->has('source') && $request->source !== 'all') {
                $query->where('source', $request->source);
            }

            if ($request->has('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('caption', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                      });
                });
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            
            if ($sortBy === 'engagement') {
                $query->orderByRaw('JSON_EXTRACT(engagement_metrics, "$.likes") + JSON_EXTRACT(engagement_metrics, "$.comments") + JSON_EXTRACT(engagement_metrics, "$.shares") ' . $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Paginate results
            $perPage = $request->get('per_page', 20);
            $posts = $query->paginate($perPage);

            return response()->json($posts);

        } catch (\Exception $e) {
            Log::error('Failed to get posts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to load posts'
            ], 500);
        }
    }

    /**
     * Get all stories with filtering and pagination
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getStories(Request $request)
    {
        try {
            $query = Story::with(['user']);

            // Apply filters
            if ($request->has('status')) {
                if ($request->status === 'active') {
                    $query->active();
                } elseif ($request->status === 'expired') {
                    $query->expired();
                }
            }

            if ($request->has('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('caption', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                      });
                });
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Paginate results
            $perPage = $request->get('per_page', 20);
            $stories = $query->paginate($perPage);

            return response()->json($stories);

        } catch (\Exception $e) {
            Log::error('Failed to get stories', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to load stories'
            ], 500);
        }
    }

    /**
     * Update post status (approve/reject/pending)
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $postId
     * @return \Illuminate\Http\Response
     */
    public function updatePostStatus(Request $request, $postId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:approved,rejected,pending',
                'reason' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $post = SocialContent::findOrFail($postId);
            $post->update([
                'filtered_status' => $request->status,
                'moderation_reason' => $request->reason
            ]);

            Log::info('Post status updated', [
                'post_id' => $postId,
                'status' => $request->status,
                'reason' => $request->reason,
                'admin_id' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Post status updated successfully',
                'post' => $post
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update post status', [
                'post_id' => $postId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to update post status'
            ], 500);
        }
    }

    /**
     * Delete post
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $postId
     * @return \Illuminate\Http\Response
     */
    public function deletePost(Request $request, $postId)
    {
        try {
            $post = SocialContent::findOrFail($postId);
            
            // Delete associated media file if exists
            if ($post->media_url && \Storage::disk('public')->exists($post->media_url)) {
                \Storage::disk('public')->delete($post->media_url);
            }

            $post->delete();

            Log::info('Post deleted by admin', [
                'post_id' => $postId,
                'admin_id' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Post deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete post', [
                'post_id' => $postId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to delete post'
            ], 500);
        }
    }

    /**
     * Delete story
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $storyId
     * @return \Illuminate\Http\Response
     */
    public function deleteStory(Request $request, $storyId)
    {
        try {
            $story = Story::findOrFail($storyId);
            
            // Delete associated media file if exists
            if ($story->media_url && \Storage::disk('public')->exists($story->media_url)) {
                \Storage::disk('public')->delete($story->media_url);
            }

            $story->delete();

            Log::info('Story deleted by admin', [
                'story_id' => $storyId,
                'admin_id' => $request->user()->id
            ]);

            return response()->json([
                'message' => 'Story deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete story', [
                'story_id' => $storyId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to delete story'
            ], 500);
        }
    }
}
