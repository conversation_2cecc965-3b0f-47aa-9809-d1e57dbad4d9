<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ProductManagementController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        // Allow providers to access their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('view products')) {
            abort(403, 'Unauthorized');
        }

        $query = Product::with(['category', 'images', 'user'])
            ->orderBy('created_at', 'desc');

        // Admin role check first - admins get full access
        if (!$user->hasRole('admin')) {
            // Non-admin users (including providers) only see their own products
            $query->where('user_id', $user->id);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $products = $query->paginate(20);
        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'products' => $products,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Index', [
            'products' => $products,
            'categories' => $categories,
            'filters' => $request->only(['category', 'type', 'search']),
        ]);
    }

    public function create(Request $request)
    {
        $user = Auth::user();

        // Allow providers to create products without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            abort(403, 'Unauthorized');
        }

        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Create', [
            'categories' => $categories,
        ]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        // Allow providers to create products without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'required|string|max:255|unique:products,sku',
            'stock_quantity' => 'required_if:type,physical|integer|min:0',
            'manage_stock' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'digital_files' => 'required_if:type,digital|array',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240',
        ]);

        try {
            // Handle featured image upload
            $featuredImagePath = null;
            if ($request->hasFile('featured_image')) {
                $featuredImage = $request->file('featured_image');
                $featuredImagePath = $featuredImage->store('products', 'public');
            }

            $product = Product::create([
                'user_id' => Auth::id(),
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'category_id' => $request->category_id,
                'price' => $request->price,
                'sale_price' => $request->sale_price,
                'sku' => $request->sku,
                'stock_quantity' => $request->type === 'physical' ? $request->stock_quantity : 0,
                'manage_stock' => $request->type === 'physical' ? ($request->manage_stock ?? true) : false,
                'in_stock' => $request->type === 'digital' ? true : ($request->stock_quantity > 0),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'featured_image' => $featuredImagePath,
                'is_featured' => $request->is_featured ?? false,
                'is_active' => $request->is_active ?? true,
                'digital_files' => $request->type === 'digital' ? $request->digital_files : null,
                'download_limit' => $request->download_limit,
                'download_expiry_days' => $request->download_expiry_days,
            ]);

            // Handle gallery images
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $index => $galleryImage) {
                    $galleryImagePath = $galleryImage->store('products', 'public');

                    $product->images()->create([
                        'image_path' => $galleryImagePath,
                        'alt_text' => $product->name . ' - Image ' . ($index + 1),
                        'sort_order' => $index,
                        'is_primary' => false,
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'product' => $product->load(['category', 'images']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function show(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to view their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('view products')) {
            abort(403, 'Unauthorized');
        }

        $product = Product::with(['category', 'images'])->findOrFail($id);

        // Check if user can access this product
        if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
            abort(403, 'Unauthorized - You can only view your own products');
        }

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
            ]);
        }

        return Inertia::render('Admin/Products/Show', [
            'product' => $product,
        ]);
    }

    public function edit(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to edit their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('edit products')) {
            abort(403, 'Unauthorized');
        }

        $product = Product::with(['category', 'images'])->findOrFail($id);

        // Admin role check first - admins get full access
        if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
            abort(403, 'Unauthorized - You can only edit your own products');
        }

        $categories = ProductCategory::active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'product' => $product,
                'categories' => $categories,
            ]);
        }

        return Inertia::render('Admin/Products/Edit', [
            'product' => $product,
            'categories' => $categories,
        ]);
    }

    public function update(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to edit their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('edit products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $product = Product::findOrFail($id);

        // Admin role check first - admins get full access
        if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized - You can only edit your own products'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'required|string|max:255|unique:products,sku,' . $id,
            'stock_quantity' => 'required_if:type,physical|integer|min:0',
            'manage_stock' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'digital_files' => 'required_if:type,digital|array',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
        ]);

        try {
            $product->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'category_id' => $request->category_id,
                'price' => $request->price,
                'sale_price' => $request->sale_price,
                'sku' => $request->sku,
                'stock_quantity' => $request->type === 'physical' ? $request->stock_quantity : 0,
                'manage_stock' => $request->type === 'physical' ? ($request->manage_stock ?? true) : false,
                'in_stock' => $request->type === 'digital' ? true : ($request->stock_quantity > 0),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'is_featured' => $request->is_featured ?? false,
                'is_active' => $request->is_active ?? true,
                'digital_files' => $request->type === 'digital' ? $request->digital_files : null,
                'download_limit' => $request->download_limit,
                'download_expiry_days' => $request->download_expiry_days,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'product' => $product->load(['category', 'images']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to delete their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('delete products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = Auth::user();

        try {
            $product = Product::findOrFail($id);

            // Admin role check first - admins get full access
            if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
                return response()->json(['message' => 'Unauthorized - You can only delete your own products'], 403);
            }

            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function toggleStatus(Request $request, $id)
    {
        $user = Auth::user();

        // Allow providers to toggle status of their own products without permission check
        if (!$user->hasRole('provider') && !$user->can('edit products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = Auth::user();

        try {
            $product = Product::findOrFail($id);

            // Admin role check first - admins get full access
            if (!$user->hasRole('admin') && $product->user_id !== $user->id) {
                return response()->json(['message' => 'Unauthorized - You can only edit your own products'], 403);
            }

            $product->update(['is_active' => !$product->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Product status updated successfully',
                'product' => $product,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product status: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function downloadImportTemplate()
    {
        $user = Auth::user();

        // Allow providers to download import template without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            abort(403, 'Unauthorized');
        }

        $headers = [
            'name',
            'description',
            'short_description',
            'type',
            'category_id',
            'price',
            'sale_price',
            'sku',
            'stock_quantity',
            'weight',
            'dimensions',
            'is_featured',
            'is_active',
            'download_limit',
            'download_expiry_days'
        ];

        $sampleData = [
            [
                'Sample Product 1',
                'This is a detailed description of the product',
                'Short description for display',
                'physical',
                '1',
                '29.99',
                '24.99',
                'SAMPLE-001',
                '100',
                '0.5',
                '10x5x2',
                '0',
                '1',
                '',
                ''
            ],
            [
                'Digital Product Sample',
                'This is a digital product description',
                'Digital product short description',
                'digital',
                '2',
                '19.99',
                '',
                'DIGITAL-001',
                '0',
                '',
                '',
                '1',
                '1',
                '5',
                '30'
            ]
        ];

        $csvContent = implode(',', $headers) . "\n";
        foreach ($sampleData as $row) {
            $csvContent .= '"' . implode('","', $row) . '"' . "\n";
        }

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="product_import_template.csv"');
    }

    public function validateImport(Request $request)
    {
        $user = Auth::user();

        // Allow providers to validate import without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240'
        ]);

        try {
            $file = $request->file('file');
            $path = $file->getRealPath();

            // Read CSV file
            $data = array_map('str_getcsv', file($path));
            $headers = array_shift($data);

            $errors = [];
            $validRows = 0;
            $totalRows = count($data);

            $categories = ProductCategory::pluck('id')->toArray();
            $existingSkus = Product::pluck('sku')->toArray();

            foreach ($data as $index => $row) {
                $rowNumber = $index + 2; // +2 because we removed headers and arrays are 0-indexed

                if (count($row) < count($headers)) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Missing required columns'
                    ];
                    continue;
                }

                $rowData = array_combine($headers, $row);

                // Validate required fields
                if (empty($rowData['name'])) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Product name is required'
                    ];
                    continue;
                }

                if (empty($rowData['sku'])) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'SKU is required'
                    ];
                    continue;
                }

                if (in_array($rowData['sku'], $existingSkus)) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'SKU already exists: ' . $rowData['sku']
                    ];
                    continue;
                }

                if (!in_array($rowData['type'], ['physical', 'digital'])) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Type must be either "physical" or "digital"'
                    ];
                    continue;
                }

                if (!in_array($rowData['category_id'], $categories)) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Invalid category ID: ' . $rowData['category_id']
                    ];
                    continue;
                }

                if (!is_numeric($rowData['price']) || $rowData['price'] < 0) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Price must be a valid positive number'
                    ];
                    continue;
                }

                $validRows++;
            }

            return response()->json([
                'total_rows' => $totalRows,
                'valid_rows' => $validRows,
                'invalid_rows' => $totalRows - $validRows,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error validating file: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkImport(Request $request)
    {
        $user = Auth::user();

        // Allow providers to bulk import without permission check
        if (!$user->hasRole('provider') && !$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:10240'
        ]);

        try {
            $file = $request->file('file');
            $path = $file->getRealPath();

            // Read CSV file
            $data = array_map('str_getcsv', file($path));
            $headers = array_shift($data);

            $importedCount = 0;
            $errors = [];
            $userId = Auth::id();

            foreach ($data as $index => $row) {
                $rowNumber = $index + 2;

                if (count($row) < count($headers)) {
                    continue;
                }

                $rowData = array_combine($headers, $row);

                // Skip invalid rows
                if (empty($rowData['name']) || empty($rowData['sku'])) {
                    continue;
                }

                try {
                    Product::create([
                        'user_id' => $userId,
                        'name' => $rowData['name'],
                        'slug' => Str::slug($rowData['name']),
                        'description' => $rowData['description'] ?? '',
                        'short_description' => $rowData['short_description'] ?? '',
                        'type' => $rowData['type'],
                        'category_id' => $rowData['category_id'],
                        'price' => (float) $rowData['price'],
                        'sale_price' => !empty($rowData['sale_price']) ? (float) $rowData['sale_price'] : null,
                        'sku' => $rowData['sku'],
                        'stock_quantity' => $rowData['type'] === 'physical' ? (int) ($rowData['stock_quantity'] ?? 0) : 0,
                        'manage_stock' => $rowData['type'] === 'physical',
                        'in_stock' => $rowData['type'] === 'digital' ? true : ((int) ($rowData['stock_quantity'] ?? 0) > 0),
                        'weight' => !empty($rowData['weight']) ? (float) $rowData['weight'] : null,
                        'dimensions' => $rowData['dimensions'] ?? null,
                        'is_featured' => (bool) ($rowData['is_featured'] ?? false),
                        'is_active' => (bool) ($rowData['is_active'] ?? true),
                        'download_limit' => !empty($rowData['download_limit']) ? (int) $rowData['download_limit'] : null,
                        'download_expiry_days' => !empty($rowData['download_expiry_days']) ? (int) $rowData['download_expiry_days'] : null,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = [
                        'row' => $rowNumber,
                        'message' => 'Failed to create product: ' . $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$importedCount} products",
                'imported_count' => $importedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error importing products: ' . $e->getMessage()
            ], 500);
        }
    }
}
