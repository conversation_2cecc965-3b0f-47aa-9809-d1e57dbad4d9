import{c as w,X as s,Z as i,B as u,u as v,_ as m,$ as l,a0 as y,a1 as g,a2 as O,Y as h,a3 as S,a4 as b,V as A,w as _,G as c}from"./vendor-BK2qhpQJ.js";function E(e,n){var r;const t=h();return S(()=>{t.value=e()},{...n,flush:(r=void 0)!=null?r:"sync"}),b(t)}function f(e){return g()?(O(e),!0):!1}function G(e){let n=!1,r;const t=l(!0);return(...o)=>(n||(r=t.run(()=>e(...o)),n=!0),r)}function B(e){let n=0,r,t;const o=()=>{n-=1,t&&n<=0&&(t.stop(),r=void 0,t=void 0)};return(...a)=>(n+=1,t||(t=l(!0),r=t.run(()=>e(...a))),f(o),r)}function P(e){if(!i(e))return u(e);const n=new Proxy({},{get(r,t,o){return v(Reflect.get(e.value,t,o))},set(r,t,o){return i(e.value[t])&&!i(o)?e.value[t].value=o:e.value[t]=o,!0},deleteProperty(r,t){return Reflect.deleteProperty(e.value,t)},has(r,t){return Reflect.has(e.value,t)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return u(n)}function C(e){return P(w(e))}function K(e,...n){const r=n.flat(),t=r[0];return C(()=>Object.fromEntries(typeof t=="function"?Object.entries(c(e)).filter(([o,a])=>!t(s(a),o)):Object.entries(c(e)).filter(o=>!r.includes(o[0]))))}const T=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const U=e=>typeof e<"u",I=Object.prototype.toString,V=e=>I.call(e)==="[object Object]",W=k();function k(){var e,n;return T&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((n=window==null?void 0:window.navigator)==null?void 0:n.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function x(e){return A()}function $(e){return Array.isArray(e)?e:[e]}function j(e,n=1e4){return m((r,t)=>{let o=s(e),a;const d=()=>setTimeout(()=>{o=s(e),t()},s(n));return f(()=>{clearTimeout(a)}),{get(){return r(),o},set(p){o=p,t(),clearTimeout(a),a=d()}}})}const z=s;function L(e,n){x()&&y(e,n)}function M(e,n,r){return _(e,n,{...r,immediate:!0})}export{T as a,$ as b,V as c,z as d,j as e,E as f,G as g,B as h,U as i,L as j,W as k,K as r,f as t,M as w};
