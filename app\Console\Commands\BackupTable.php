<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class BackupTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:table-backup {table} {file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup a specific database table to SQL file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $table = $this->argument('table');
        $file = $this->argument('file');
        
        try {
            // Check if table exists
            if (!DB::getSchemaBuilder()->hasTable($table)) {
                $this->error("Table '{$table}' does not exist.");
                return 1;
            }
            
            // Get table structure
            $createTable = DB::select("SHOW CREATE TABLE `{$table}`")[0];
            $createTableSql = $createTable->{'Create Table'};
            
            // Get table data
            $rows = DB::table($table)->get();
            
            // Create SQL content
            $sql = "-- Backup of table '{$table}' created at " . date('Y-m-d H:i:s') . "\n\n";
            $sql .= "DROP TABLE IF EXISTS `{$table}_backup`;\n";
            $sql .= str_replace("CREATE TABLE `{$table}`", "CREATE TABLE `{$table}_backup`", $createTableSql) . ";\n\n";
            
            if ($rows->count() > 0) {
                $sql .= "INSERT INTO `{$table}_backup` VALUES\n";
                $values = [];
                
                foreach ($rows as $row) {
                    $rowData = [];
                    foreach ((array)$row as $value) {
                        if (is_null($value)) {
                            $rowData[] = 'NULL';
                        } else {
                            $rowData[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(', ', $rowData) . ')';
                }
                
                $sql .= implode(",\n", $values) . ";\n";
            }
            
            // Ensure directory exists
            $directory = dirname($file);
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }
            
            // Write to file
            file_put_contents($file, $sql);
            
            $this->info("Table '{$table}' backed up to '{$file}' successfully.");
            $this->info("Backed up {$rows->count()} rows.");
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("Failed to backup table '{$table}': " . $e->getMessage());
            return 1;
        }
    }
}
