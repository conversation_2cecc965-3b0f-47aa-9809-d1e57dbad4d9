<?php

namespace App\Http\Controllers;

use App\Services\InstagramService;
use App\Models\InstagramAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class InstagramController extends Controller
{
    private $instagramService;

    public function __construct(InstagramService $instagramService)
    {
        $this->instagramService = $instagramService;
    }

    /**
     * Get Instagram authorization URL
     */
    public function getAuthUrl(Request $request)
    {
        try {
            $state = base64_encode(json_encode([
                'user_id' => Auth::id(),
                'timestamp' => time(),
            ]));

            $authUrl = $this->instagramService->getAuthorizationUrl($state);

            return response()->json([
                'success' => true,
                'auth_url' => $authUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram auth URL generation failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate authorization URL',
            ], 500);
        }
    }

    /**
     * Handle Instagram OAuth callback
     */
    public function handleCallback(Request $request)
    {
        try {
            $code = $request->get('code');
            $state = $request->get('state');
            $error = $request->get('error');

            if ($error) {
                return redirect('/discover?instagram_error=' . urlencode($error));
            }

            if (!$code || !$state) {
                return redirect('/discover?instagram_error=missing_parameters');
            }

            // Decode and validate state
            $stateData = json_decode(base64_decode($state), true);
            if (!$stateData || !isset($stateData['user_id'])) {
                return redirect('/discover?instagram_error=invalid_state');
            }

            // Find the user
            $user = \App\Models\User::find($stateData['user_id']);
            if (!$user) {
                return redirect('/discover?instagram_error=user_not_found');
            }

            // Connect the account
            $result = $this->instagramService->connectAccount($user, $code);

            if ($result['success']) {
                return redirect('/discover?instagram_success=1');
            } else {
                return redirect('/discover?instagram_error=' . urlencode($result['message']));
            }
        } catch (\Exception $e) {
            Log::error('Instagram callback failed', ['error' => $e->getMessage()]);
            return redirect('/discover?instagram_error=callback_failed');
        }
    }

    /**
     * Get user's Instagram account status
     */
    public function getAccountStatus(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => true,
                    'connected' => false,
                    'message' => 'No Instagram account connected',
                ]);
            }

            return response()->json([
                'success' => true,
                'connected' => true,
                'account' => [
                    'username' => $account->username,
                    'account_type' => $account->account_type,
                    'account_type_display' => $account->account_type_display,
                    'media_count' => $account->media_count,
                    'last_synced_at' => $account->last_sync_at?->diffForHumans(),
                    'needs_sync' => $account->needsSync(),
                    'token_expired' => $account->isTokenExpired(),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram account status check failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to check account status',
            ], 500);
        }
    }

    /**
     * Disconnect Instagram account
     */
    public function disconnect(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No Instagram account found',
                ], 404);
            }

            Log::info('Disconnecting Instagram account and cleaning up content', [
                'user_id' => $user->id,
                'account_id' => $account->id,
                'username' => $account->username
            ]);

            // Remove all SocialContent entries from this account (from discover feed)
            $socialContentDeleted = \App\Models\SocialContent::where('user_id', $user->id)
                ->where('source', 'instagram')
                ->delete();

            // Remove all SocialMediaPost entries from this account
            $socialMediaPostsDeleted = \App\Models\SocialMediaPost::where('user_id', $user->id)
                ->where('platform', 'instagram')
                ->delete();

            // Deactivate the account
            $account->update(['is_active' => false]);

            Log::info('Instagram account disconnected and content cleaned up', [
                'user_id' => $user->id,
                'social_content_deleted' => $socialContentDeleted,
                'social_media_posts_deleted' => $socialMediaPostsDeleted
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Instagram account disconnected and all content removed successfully',
                'removed_posts' => $socialMediaPostsDeleted,
                'removed_feed_content' => $socialContentDeleted
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram disconnect failed', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to disconnect Instagram account',
            ], 500);
        }
    }

    /**
     * Get Instagram connection progress
     */
    public function getConnectionProgress(Request $request)
    {
        try {
            $user = Auth::user();
            $cacheKey = "instagram_progress_{$user->id}";

            $progress = Cache::get($cacheKey, [
                'status' => 'idle',
                'step' => '',
                'progress' => 0,
                'message' => '',
                'imported_count' => 0
            ]);

            return response()->json([
                'success' => true,
                'progress' => $progress
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get Instagram connection progress', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get progress',
            ], 500);
        }
    }

    /**
     * Manually sync Instagram content
     */
    public function syncContent(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active Instagram account found',
                ], 404);
            }

            if ($account->isTokenExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Instagram access token has expired. Please reconnect your account.',
                ], 401);
            }

            // Set initial progress
            $cacheKey = "instagram_progress_{$user->id}";
            Cache::put($cacheKey, [
                'status' => 'syncing',
                'step' => 'fetching_posts',
                'progress' => 10,
                'message' => 'Fetching your Instagram posts...',
                'imported_count' => 0
            ], 300); // 5 minutes

            $importedCount = $this->instagramService->syncAccountContent($account);

            // Update final progress
            Cache::put($cacheKey, [
                'status' => 'completed',
                'step' => 'completed',
                'progress' => 100,
                'message' => "Successfully imported {$importedCount} health-related posts",
                'imported_count' => $importedCount
            ], 60); // Keep for 1 minute

            return response()->json([
                'success' => true,
                'message' => "Successfully imported {$importedCount} health-related posts",
                'imported_count' => $importedCount,
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram sync failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync Instagram content',
            ], 500);
        }
    }

    /**
     * Get Instagram content for the feed
     */
    public function getFeedContent(Request $request)
    {
        try {
            $limit = $request->get('limit', 10);
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $limit;

            // Get Instagram content from all connected accounts
            $content = \App\Models\SocialContent::with(['user'])
                ->where('source', 'instagram')
                ->where('filtered_status', 'approved')
                ->orderBy('published_at', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();

            $total = \App\Models\SocialContent::where('source', 'instagram')
                ->where('filtered_status', 'approved')
                ->count();

            return response()->json([
                'success' => true,
                'data' => $content,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'last_page' => ceil($total / $limit),
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram feed content fetch failed', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch Instagram content',
                'data' => [],
            ], 500);
        }
    }

    /**
     * Refresh access token for an account
     */
    public function refreshToken(Request $request)
    {
        try {
            $user = Auth::user();
            $account = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active Instagram account found',
                ], 404);
            }

            $tokenData = $this->instagramService->refreshAccessToken($account->access_token);

            if (!$tokenData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to refresh access token',
                ], 400);
            }

            $account->update([
                'access_token' => $tokenData['access_token'],
                'expires_at' => now()->addSeconds($tokenData['expires_in']),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Access token refreshed successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Instagram token refresh failed', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh access token',
            ], 500);
        }
    }

    /**
     * Get Instagram Stories for connected accounts
     */
    public function getStories(Request $request)
    {
        try {
            $user = Auth::user();

            // Get all connected Instagram accounts for this user
            $accounts = InstagramAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->get();

            if ($accounts->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'stories' => []
                ]);
            }

            $stories = [];

            foreach ($accounts as $account) {
                try {
                    // Create a story entry for each connected Instagram account
                    $stories[] = [
                        'user_id' => 'instagram_' . $account->id,
                        'username' => $account->username,
                        'profile_image' => $account->profile_picture_url,
                        'source' => 'instagram',
                        'story_count' => 1, // Instagram stories are typically grouped
                        'has_unviewed' => true, // Assume new content is available
                        'latest_story_time' => $account->updated_at,
                        'created_at' => $account->created_at,
                        'account_type' => $account->account_type,
                        'account_id' => $account->id
                    ];
                } catch (\Exception $e) {
                    Log::error('Error processing Instagram story for account ' . $account->username, [
                        'error' => $e->getMessage(),
                        'account_id' => $account->id
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'stories' => $stories
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram stories error', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to load Instagram stories: ' . $e->getMessage(),
                'stories' => []
            ], 500);
        }
    }
}
