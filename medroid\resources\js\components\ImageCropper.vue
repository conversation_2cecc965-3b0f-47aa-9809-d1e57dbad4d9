<template>
    <div v-if="show" class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
        <div class="bg-white rounded-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Crop Image</h3>
                <div class="flex items-center space-x-3">
                    <button 
                        @click="$emit('cancel')"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        Cancel
                    </button>
                    <button 
                        @click="cropImage"
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Done
                    </button>
                </div>
            </div>

            <!-- Aspect Ratio Selector -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-700">Aspect Ratio:</span>
                    <div class="flex space-x-2">
                        <button 
                            v-for="(ratio, index) in aspectRatios" 
                            :key="index"
                            @click="setAspectRatio(index)"
                            :class="[
                                'px-3 py-1 text-sm rounded-lg transition-colors',
                                selectedAspectRatio === index 
                                    ? 'bg-blue-600 text-white' 
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            ]"
                        >
                            {{ ratio.name }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Crop Area -->
            <div class="p-4">
                <div class="relative bg-gray-100 rounded-lg overflow-hidden" style="height: 400px;">
                    <canvas 
                        ref="canvas"
                        @mousedown="startDrag"
                        @mousemove="drag"
                        @mouseup="endDrag"
                        @wheel="zoom"
                        class="cursor-move"
                        style="width: 100%; height: 100%;"
                    ></canvas>
                </div>
            </div>

            <!-- Zoom Controls -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center justify-center space-x-4">
                    <button 
                        @click="zoomOut"
                        class="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                        </svg>
                    </button>
                    <input 
                        type="range" 
                        v-model="zoomLevel"
                        @input="updateZoom"
                        min="0.5" 
                        max="3" 
                        step="0.1"
                        class="w-32"
                    />
                    <button 
                        @click="zoomIn"
                        class="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'

const props = defineProps({
    show: Boolean,
    imageFile: File,
    aspectRatio: {
        type: Number,
        default: 4/3
    }
})

const emit = defineEmits(['cropped', 'cancel'])

// Aspect ratio options
const aspectRatios = [
    { name: '1:1', ratio: 1 },
    { name: '4:3', ratio: 4/3 },
    { name: '16:9', ratio: 16/9 },
    { name: '3:4', ratio: 3/4 },
    { name: '9:16', ratio: 9/16 },
    { name: 'Free', ratio: 0 }
]

// Reactive state
const canvas = ref(null)
const selectedAspectRatio = ref(1) // Default to 4:3
const zoomLevel = ref(1)

// Image and crop state
let image = null
let cropX = 0
let cropY = 0
let cropWidth = 0
let cropHeight = 0
let isDragging = false
let dragStartX = 0
let dragStartY = 0
let canvasWidth = 0
let canvasHeight = 0
let imageScale = 1

// Initialize when component mounts
onMounted(() => {
    if (props.show && props.imageFile) {
        loadImage()
    }
})

// Watch for prop changes
watch(() => props.show, (newVal) => {
    if (newVal && props.imageFile) {
        nextTick(() => {
            loadImage()
        })
    }
})

function loadImage() {
    if (!canvas.value || !props.imageFile) return

    const ctx = canvas.value.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
        image = img
        setupCanvas()
        calculateInitialCrop()
        drawCanvas()
    }
    
    img.src = URL.createObjectURL(props.imageFile)
}

function setupCanvas() {
    const container = canvas.value.parentElement
    canvasWidth = container.clientWidth
    canvasHeight = container.clientHeight
    
    canvas.value.width = canvasWidth
    canvas.value.height = canvasHeight
    
    // Calculate scale to fit image in canvas
    const scaleX = canvasWidth / image.width
    const scaleY = canvasHeight / image.height
    imageScale = Math.min(scaleX, scaleY) * zoomLevel.value
}

function calculateInitialCrop() {
    const ratio = aspectRatios[selectedAspectRatio.value].ratio
    
    if (ratio === 0) {
        // Free form - use full image
        cropWidth = image.width
        cropHeight = image.height
        cropX = 0
        cropY = 0
    } else {
        // Calculate crop area for aspect ratio
        const imageRatio = image.width / image.height
        
        if (imageRatio > ratio) {
            // Image is wider than target ratio
            cropHeight = image.height
            cropWidth = cropHeight * ratio
            cropX = (image.width - cropWidth) / 2
            cropY = 0
        } else {
            // Image is taller than target ratio
            cropWidth = image.width
            cropHeight = cropWidth / ratio
            cropX = 0
            cropY = (image.height - cropHeight) / 2
        }
    }
}

function drawCanvas() {
    if (!canvas.value || !image) return
    
    const ctx = canvas.value.getContext('2d')
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    
    // Calculate display dimensions
    const displayWidth = image.width * imageScale
    const displayHeight = image.height * imageScale
    const offsetX = (canvasWidth - displayWidth) / 2
    const offsetY = (canvasHeight - displayHeight) / 2
    
    // Draw image
    ctx.drawImage(image, offsetX, offsetY, displayWidth, displayHeight)
    
    // Draw crop overlay
    const cropDisplayX = offsetX + (cropX * imageScale)
    const cropDisplayY = offsetY + (cropY * imageScale)
    const cropDisplayWidth = cropWidth * imageScale
    const cropDisplayHeight = cropHeight * imageScale
    
    // Darken areas outside crop
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)
    ctx.clearRect(cropDisplayX, cropDisplayY, cropDisplayWidth, cropDisplayHeight)
    
    // Draw crop border
    ctx.strokeStyle = '#3B82F6'
    ctx.lineWidth = 2
    ctx.strokeRect(cropDisplayX, cropDisplayY, cropDisplayWidth, cropDisplayHeight)
    
    // Draw corner handles
    const handleSize = 8
    ctx.fillStyle = '#3B82F6'
    
    // Top-left
    ctx.fillRect(cropDisplayX - handleSize/2, cropDisplayY - handleSize/2, handleSize, handleSize)
    // Top-right
    ctx.fillRect(cropDisplayX + cropDisplayWidth - handleSize/2, cropDisplayY - handleSize/2, handleSize, handleSize)
    // Bottom-left
    ctx.fillRect(cropDisplayX - handleSize/2, cropDisplayY + cropDisplayHeight - handleSize/2, handleSize, handleSize)
    // Bottom-right
    ctx.fillRect(cropDisplayX + cropDisplayWidth - handleSize/2, cropDisplayY + cropDisplayHeight - handleSize/2, handleSize, handleSize)
}

function setAspectRatio(index) {
    selectedAspectRatio.value = index
    calculateInitialCrop()
    drawCanvas()
}

function updateZoom() {
    imageScale = Math.min(canvasWidth / image.width, canvasHeight / image.height) * zoomLevel.value
    drawCanvas()
}

function zoomIn() {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.1)
    updateZoom()
}

function zoomOut() {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
    updateZoom()
}

function startDrag(event) {
    isDragging = true
    dragStartX = event.offsetX
    dragStartY = event.offsetY
}

function drag(event) {
    if (!isDragging) return
    
    const deltaX = event.offsetX - dragStartX
    const deltaY = event.offsetY - dragStartY
    
    // Update crop position (simplified - in a real implementation you'd handle edge cases)
    cropX = Math.max(0, Math.min(image.width - cropWidth, cropX + deltaX / imageScale))
    cropY = Math.max(0, Math.min(image.height - cropHeight, cropY + deltaY / imageScale))
    
    dragStartX = event.offsetX
    dragStartY = event.offsetY
    
    drawCanvas()
}

function endDrag() {
    isDragging = false
}

function zoom(event) {
    event.preventDefault()
    const delta = event.deltaY > 0 ? -0.1 : 0.1
    zoomLevel.value = Math.max(0.5, Math.min(3, zoomLevel.value + delta))
    updateZoom()
}

function cropImage() {
    if (!canvas.value || !image) return
    
    // Create a new canvas for the cropped image
    const cropCanvas = document.createElement('canvas')
    const cropCtx = cropCanvas.getContext('2d')
    
    cropCanvas.width = cropWidth
    cropCanvas.height = cropHeight
    
    // Draw the cropped portion
    cropCtx.drawImage(
        image,
        cropX, cropY, cropWidth, cropHeight,
        0, 0, cropWidth, cropHeight
    )
    
    // Convert to blob and emit
    cropCanvas.toBlob((blob) => {
        emit('cropped', blob)
    }, 'image/jpeg', 0.9)
}
</script>
