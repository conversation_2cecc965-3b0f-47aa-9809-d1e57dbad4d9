<?php

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== Instagram Configuration Debug ===\n\n";

// Check environment variables
echo "1. Environment Variables:\n";
echo "   INSTAGRAM_APP_ID: " . ($_ENV['INSTAGRAM_APP_ID'] ?? 'NOT SET') . "\n";
echo "   INSTAGRAM_APP_SECRET: " . (isset($_ENV['INSTAGRAM_APP_SECRET']) ? '[SET]' : 'NOT SET') . "\n";
echo "   INSTAGRAM_REDIRECT_URI: " . ($_ENV['INSTAGRAM_REDIRECT_URI'] ?? 'NOT SET') . "\n\n";

// Generate the exact URL that would be created
$appId = $_ENV['INSTAGRAM_APP_ID'] ?? '';
$redirectUri = $_ENV['INSTAGRAM_REDIRECT_URI'] ?? '';
$scope = 'user_profile,user_media';
$responseType = 'code';
$state = base64_encode(json_encode(['user_id' => 18, 'timestamp' => time()]));

$params = [
    'client_id' => $appId,
    'redirect_uri' => $redirectUri,
    'scope' => $scope,
    'response_type' => $responseType,
    'state' => $state
];

$authUrl = 'https://api.instagram.com/oauth/authorize?' . http_build_query($params);

echo "2. Generated Auth URL:\n";
echo "   " . $authUrl . "\n\n";

echo "3. URL Parameters Breakdown:\n";
foreach ($params as $key => $value) {
    echo "   {$key}: {$value}\n";
}

echo "\n4. Potential Issues to Check:\n";
echo "   - Is your Meta app in 'Live' mode? (Development mode has limitations)\n";
echo "   - Is the redirect URI exactly matching in Meta Developer Console?\n";
echo "   - Is the Instagram API with Instagram Login product added to your app?\n";
echo "   - Are the required permissions configured?\n\n";

// Test if ngrok URL is accessible
echo "5. Testing ngrok URL accessibility:\n";
$ngrokUrl = $_ENV['INSTAGRAM_REDIRECT_URI'] ?? '';
if ($ngrokUrl) {
    $testUrl = str_replace('/auth/instagram/callback', '', $ngrokUrl);
    echo "   Testing: {$testUrl}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ Error: {$error}\n";
    } else {
        echo "   ✅ HTTP Status: {$httpCode}\n";
    }
} else {
    echo "   ❌ No redirect URI configured\n";
}

echo "\n6. Meta Developer Console Checklist:\n";
echo "   □ App Status: Live (not Development)\n";
echo "   □ Instagram API with Instagram Login product added\n";
echo "   □ Valid OAuth Redirect URIs includes: {$redirectUri}\n";
echo "   □ App Review completed (if required)\n";
echo "   □ Business verification completed (if required)\n\n";

echo "=== Debug Complete ===\n";
