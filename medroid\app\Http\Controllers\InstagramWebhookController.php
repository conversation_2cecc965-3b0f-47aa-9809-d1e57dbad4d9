<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\InstagramAccount;
use App\Services\InstagramService;

class InstagramWebhookController extends Controller
{
    protected $instagramService;

    public function __construct(InstagramService $instagramService)
    {
        $this->instagramService = $instagramService;
    }

    /**
     * Handle Instagram webhook verification (GET request)
     */
    public function verify(Request $request)
    {
        $mode = $request->get('hub_mode');
        $token = $request->get('hub_verify_token');
        $challenge = $request->get('hub_challenge');

        Log::info('Instagram webhook verification attempt', [
            'mode' => $mode,
            'token' => $token,
            'challenge' => $challenge
        ]);

        // Verify the webhook
        if ($mode === 'subscribe' && $token === env('INSTAGRAM_WEBHOOK_VERIFY_TOKEN')) {
            Log::info('Instagram webhook verified successfully');
            return response($challenge, 200);
        }

        Log::warning('Instagram webhook verification failed');
        return response('Forbidden', 403);
    }

    /**
     * Handle Instagram webhook events (POST request)
     */
    public function handle(Request $request)
    {
        $payload = $request->all();
        
        Log::info('Instagram webhook received', [
            'payload' => $payload
        ]);

        // Verify the request signature (optional but recommended for production)
        if (!$this->verifySignature($request)) {
            Log::warning('Instagram webhook signature verification failed');
            return response('Unauthorized', 401);
        }

        // Process webhook events
        if (isset($payload['entry'])) {
            foreach ($payload['entry'] as $entry) {
                $this->processEntry($entry);
            }
        }

        return response('OK', 200);
    }

    /**
     * Process a single webhook entry
     */
    protected function processEntry($entry)
    {
        Log::info('Processing Instagram webhook entry', ['entry' => $entry]);

        // Handle different types of changes
        if (isset($entry['changes'])) {
            foreach ($entry['changes'] as $change) {
                $this->processChange($entry['id'], $change);
            }
        }
    }

    /**
     * Process a single change event
     */
    protected function processChange($instagramUserId, $change)
    {
        $field = $change['field'] ?? null;
        $value = $change['value'] ?? null;

        Log::info('Processing Instagram change', [
            'user_id' => $instagramUserId,
            'field' => $field,
            'value' => $value
        ]);

        switch ($field) {
            case 'media':
                $this->handleMediaChange($instagramUserId, $value);
                break;
            
            case 'comments':
                $this->handleCommentsChange($instagramUserId, $value);
                break;
            
            default:
                Log::info('Unhandled Instagram webhook field: ' . $field);
        }
    }

    /**
     * Handle media changes (new posts, updates, etc.)
     */
    protected function handleMediaChange($instagramUserId, $value)
    {
        Log::info('Handling Instagram media change', [
            'user_id' => $instagramUserId,
            'value' => $value
        ]);

        // Find the Instagram account
        $instagramAccount = InstagramAccount::where('instagram_user_id', $instagramUserId)->first();
        
        if (!$instagramAccount) {
            Log::warning('Instagram account not found for user ID: ' . $instagramUserId);
            return;
        }

        // Sync the latest content for this account
        try {
            $this->instagramService->syncAccountContent($instagramAccount);
            Log::info('Successfully synced Instagram content for user: ' . $instagramUserId);
        } catch (\Exception $e) {
            Log::error('Failed to sync Instagram content', [
                'user_id' => $instagramUserId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle comments changes
     */
    protected function handleCommentsChange($instagramUserId, $value)
    {
        Log::info('Handling Instagram comments change', [
            'user_id' => $instagramUserId,
            'value' => $value
        ]);

        // For now, just log the event
        // You can implement comment processing logic here if needed
    }

    /**
     * Verify webhook signature (for production security)
     */
    protected function verifySignature(Request $request)
    {
        // For development, skip signature verification
        if (env('APP_ENV') === 'local') {
            return true;
        }

        $signature = $request->header('X-Hub-Signature-256');
        $payload = $request->getContent();
        $secret = env('INSTAGRAM_APP_SECRET');

        if (!$signature || !$secret) {
            return false;
        }

        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
        
        return hash_equals($expectedSignature, $signature);
    }
}
