<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InstagramAccount;
use App\Services\InstagramService;

class SyncInstagramContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instagram:sync {--force : Force sync even if recently synced}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Instagram content from connected accounts';

    private $instagramService;

    public function __construct(InstagramService $instagramService)
    {
        parent::__construct();
        $this->instagramService = $instagramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting Instagram content sync...');

        $force = $this->option('force');

        // Get accounts that need syncing
        $query = InstagramAccount::active();

        if (!$force) {
            $query->needsSync();
        }

        $accounts = $query->get();

        if ($accounts->isEmpty()) {
            $this->info('No accounts need syncing.');
            return 0;
        }

        $this->info("Found {$accounts->count()} accounts to sync.");

        $totalImported = 0;
        $successCount = 0;
        $errorCount = 0;

        foreach ($accounts as $account) {
            $this->line("Syncing @{$account->username} ({$account->account_type})...");

            try {
                // Check if token needs refresh
                if ($account->isTokenExpired()) {
                    $this->warn("Token expired for @{$account->username}, attempting refresh...");

                    $tokenData = $this->instagramService->refreshAccessToken($account->access_token);

                    if ($tokenData) {
                        $account->update([
                            'access_token' => $tokenData['access_token'],
                            'expires_at' => now()->addSeconds($tokenData['expires_in']),
                        ]);
                        $this->info("Token refreshed for @{$account->username}");
                    } else {
                        $this->error("Failed to refresh token for @{$account->username}");
                        $errorCount++;
                        continue;
                    }
                }

                // Import media
                $imported = $this->instagramService->syncAccountContent($account);
                $totalImported += $imported;
                $successCount++;

                $this->info("Imported {$imported} posts from @{$account->username}");

            } catch (\Exception $e) {
                $this->error("Error syncing @{$account->username}: " . $e->getMessage());
                $errorCount++;
            }
        }

        $this->info("Sync completed!");
        $this->info("Accounts synced: {$successCount}");
        $this->info("Errors: {$errorCount}");
        $this->info("Total posts imported: {$totalImported}");

        return 0;
    }
}
