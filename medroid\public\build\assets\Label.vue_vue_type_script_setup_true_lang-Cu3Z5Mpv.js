import{V as $,r as R,w as E,C as I,c as g,X as v,Y as J,o as T,u as b,M as P,y as A,e as M,g as B,N as L,Q as z,l as W,v as K,d as U,A as Q,Z as X}from"./vendor-BK2qhpQJ.js";import{P as Y,c as D}from"./Primitive-BOKwGa1V.js";import{i as Z,a as q,t as k,b as V,w as G,c as H}from"./index-vK5yNL7A.js";const S=q?window:void 0;function _(e){var t;const s=v(e);return(t=s==null?void 0:s.$el)!=null?t:s}function F(...e){const t=[],s=()=>{t.forEach(o=>o()),t.length=0},r=(o,i,c,a)=>(o.addEventListener(i,c,a),()=>o.removeEventListener(i,c,a)),n=g(()=>{const o=V(v(e[0])).filter(i=>i!=null);return o.every(i=>typeof i!="string")?o:void 0}),u=G(()=>{var o,i;return[(i=(o=n.value)==null?void 0:o.map(c=>_(c)))!=null?i:[S].filter(c=>c!=null),V(v(n.value?e[1]:e[0])),V(b(n.value?e[2]:e[1])),v(n.value?e[3]:e[2])]},([o,i,c,a])=>{if(s(),!(o!=null&&o.length)||!(i!=null&&i.length)||!(c!=null&&c.length))return;const p=H(a)?{...a}:a;t.push(...o.flatMap(m=>i.flatMap(d=>c.map(x=>r(m,d,x,p)))))},{flush:"post"}),l=()=>{u(),s()};return k(s),l}function ee(){const e=J(!1),t=$();return t&&T(()=>{e.value=!0},t),e}function te(e){const t=ee();return g(()=>(t.value,!!e()))}function ne(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ce(...e){let t,s,r={};e.length===3?(t=e[0],s=e[1],r=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,s=e[0],r=e[1]):(t=e[0],s=e[1]):(t=!0,s=e[0]);const{target:n=S,eventName:u="keydown",passive:l=!1,dedupe:o=!1}=r,i=ne(t);return F(n,u,a=>{a.repeat&&v(o)||i(a)&&s(a)},l)}function oe(e){return JSON.parse(JSON.stringify(e))}function de(e,t,s={}){const{window:r=S,...n}=s;let u;const l=te(()=>r&&"ResizeObserver"in r),o=()=>{u&&(u.disconnect(),u=void 0)},i=g(()=>{const p=v(e);return Array.isArray(p)?p.map(m=>_(m)):[_(p)]}),c=E(i,p=>{if(o(),l.value&&r){u=new ResizeObserver(t);for(const m of p)m&&u.observe(m,n)}},{immediate:!0,flush:"post"}),a=()=>{o(),c()};return k(a),{isSupported:l,stop:a}}function se(e,t,s,r={}){var n,u,l;const{clone:o=!1,passive:i=!1,eventName:c,deep:a=!1,defaultValue:p,shouldEmit:m}=r,d=$(),x=s||(d==null?void 0:d.emit)||((n=d==null?void 0:d.$emit)==null?void 0:n.bind(d))||((l=(u=d==null?void 0:d.proxy)==null?void 0:u.$emit)==null?void 0:l.bind(d==null?void 0:d.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const C=f=>o?typeof o=="function"?o(f):oe(f):f,j=()=>Z(e[t])?C(e[t]):p,N=f=>{m?m(f)&&x(y,f):x(y,f)};if(i){const f=j(),O=R(f);let h=!1;return E(()=>e[t],w=>{h||(h=!0,O.value=C(w),I(()=>h=!1))}),E(O,w=>{!h&&(w!==e[t]||a)&&N(w)},{deep:a}),O}else return g({get(){return j()},set(f){N(f)}})}function le(){const e=$(),t=R(),s=g(()=>{var l,o;return["#text","#comment"].includes((l=t.value)==null?void 0:l.$el.nodeName)?(o=t.value)==null?void 0:o.$el.nextElementSibling:_(t)}),r=Object.assign({},e.exposed),n={};for(const l in e.props)Object.defineProperty(n,l,{enumerable:!0,configurable:!0,get:()=>e.props[l]});if(Object.keys(r).length>0)for(const l in r)Object.defineProperty(n,l,{enumerable:!0,configurable:!0,get:()=>r[l]});Object.defineProperty(n,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=n;function u(l){t.value=l,l&&(Object.defineProperty(n,"$el",{enumerable:!0,configurable:!0,get:()=>l instanceof Element?l:l.$el}),e.exposed=n)}return{forwardRef:u,currentRef:t,currentElement:s}}const re=P({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(e){const t=e;return le(),(s,r)=>(M(),A(b(Y),z(t,{onMousedown:r[0]||(r[0]=n=>{!n.defaultPrevented&&n.detail>1&&n.preventDefault()})}),{default:B(()=>[L(s.$slots,"default")]),_:3},16))}}),fe=P({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(e,{emit:t}){const s=e,n=se(s,"modelValue",t,{passive:!0,defaultValue:s.defaultValue});return(u,l)=>W((M(),U("input",{"onUpdate:modelValue":l[0]||(l[0]=o=>X(n)?n.value=o:null),"data-slot":"input",class:Q(b(D)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s.class))},null,2)),[[K,b(n)]])}}),pe=P({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,s=g(()=>{const{class:r,...n}=t;return n});return(r,n)=>(M(),A(b(re),z({"data-slot":"label"},s.value,{class:b(D)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t.class)}),{default:B(()=>[L(r.$slots,"default")]),_:3},16,["class"]))}});export{pe as _,fe as a,le as b,se as c,de as d,S as e,F as f,ee as g,ce as o,_ as u};
