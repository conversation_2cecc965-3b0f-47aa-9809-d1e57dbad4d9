<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('social_media_posts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('instagram_account_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('platform'); // 'instagram', 'tiktok', etc.
            $table->string('platform_post_id')->unique();
            $table->string('media_type'); // 'IMAGE', 'VIDEO', 'CAROUSEL_ALBUM', 'REELS'
            $table->text('media_url')->nullable();
            $table->text('thumbnail_url')->nullable();
            $table->text('caption')->nullable();
            $table->text('permalink')->nullable();
            $table->timestamp('posted_at');
            $table->integer('like_count')->default(0);
            $table->integer('comment_count')->default(0);
            $table->boolean('is_visible')->default(true);
            $table->timestamps();

            $table->index(['user_id', 'platform', 'is_visible']);
            $table->index(['platform', 'posted_at']);
            $table->index('platform_post_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_media_posts');
    }
};
