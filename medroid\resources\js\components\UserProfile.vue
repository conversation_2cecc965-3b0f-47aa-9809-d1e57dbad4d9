<script setup>
import { ref, reactive, onMounted, computed, watch, toRefs } from 'vue'
import axios from 'axios'

// Props
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    userId: {
        type: [String, Number],
        default: null
    }
})

// Emits
const emit = defineEmits(['close', 'open-post-detail'])

// State
const loading = ref(false)
const uploadingImage = ref(false)
const editingBio = ref(false)
const followLoading = ref(false)
const bioText = ref('')
const showFollowersList = ref(false)
const showFollowingList = ref(false)
const followersList = ref([])
const followingList = ref([])
const profileData = reactive({
    user: null,
    stats: {
        posts: 0,
        followers: 0,
        following: 0,
        stories: 0
    },
    posts: [],
    stories: []
})

// Computed
const displayName = computed(() => profileData.user?.name || 'User')
const username = computed(() => `@${profileData.user?.email?.split('@')[0] || 'username'}`)
const profileImage = computed(() => {
    if (profileData.user?.profile_image) {
        return profileData.user.profile_image.startsWith('http')
            ? profileData.user.profile_image
            : `/storage/${profileData.user.profile_image}`
    }
    return '/images/default-avatar.svg'
})

const isFounderMember = computed(() => profileData.user?.is_founder_member || false)
const isOwnProfile = computed(() => !props.userId || props.userId === profileData.user?.id)
const isFollowing = ref(false)

// Methods
const loadProfile = async () => {
    if (!props.show) return

    loading.value = true
    try {
        // Load user profile data - if userId is provided, load that user's profile, otherwise load current user's profile
        const endpoint = props.userId
            ? `/web-api/user/${props.userId}/profile-stats`
            : '/web-api/user/profile-stats'

        const response = await axios.get(endpoint)

        profileData.user = response.data.user
        profileData.stats = response.data.stats
        profileData.posts = response.data.posts || []
        profileData.stories = response.data.stories || []
        isFollowing.value = response.data.is_following || false
        bioText.value = response.data.user?.bio || ''

        console.log('Profile loaded:', {
            userId: props.userId,
            isOwnProfile: isOwnProfile.value,
            isFollowing: isFollowing.value,
            user: response.data.user,
            stats: response.data.stats
        })
    } catch (error) {
        console.error('Error loading profile:', error)
    } finally {
        loading.value = false
    }
}

const closeProfile = () => {
    emit('close')
}

const toggleFollow = async () => {
    if (!props.userId || isOwnProfile.value || followLoading.value) return

    followLoading.value = true

    try {
        const endpoint = isFollowing.value
            ? `/web-api/user/${props.userId}/unfollow`
            : `/web-api/user/${props.userId}/follow`

        console.log('Toggling follow:', {
            userId: props.userId,
            currentlyFollowing: isFollowing.value,
            endpoint
        })

        const response = await axios.post(endpoint)

        console.log('Follow response:', response.data)

        isFollowing.value = !isFollowing.value

        // Update follower count
        if (isFollowing.value) {
            profileData.stats.followers++
        } else {
            profileData.stats.followers--
        }

        // Show success message
        const message = isFollowing.value ? 'Now following this user!' : 'Unfollowed user'
        alert(message)

    } catch (error) {
        console.error('Error toggling follow:', error)
        alert('Failed to update follow status. Please try again.')
    } finally {
        followLoading.value = false
    }
}

const openPostDetail = (post) => {
    emit('open-post-detail', post)
}

// Bio management
const startEditingBio = () => {
    editingBio.value = true
}

const cancelEditingBio = () => {
    editingBio.value = false
    bioText.value = profileData.user?.bio || ''
}

const saveBio = async () => {
    try {
        const response = await axios.post('/web-api/user/bio', {
            bio: bioText.value
        })

        profileData.user.bio = bioText.value
        editingBio.value = false
        alert('Bio updated successfully!')
    } catch (error) {
        console.error('Error updating bio:', error)
        alert('Failed to update bio. Please try again.')
    }
}

// Followers/Following management
const showFollowers = async () => {
    try {
        const userId = props.userId || profileData.user?.id
        const response = await axios.get(`/web-api/user/${userId}/followers`)
        followersList.value = response.data.followers
        showFollowersList.value = true
    } catch (error) {
        console.error('Error loading followers:', error)
    }
}

const showFollowing = async () => {
    try {
        const userId = props.userId || profileData.user?.id
        const response = await axios.get(`/web-api/user/${userId}/following`)
        followingList.value = response.data.following
        showFollowingList.value = true
    } catch (error) {
        console.error('Error loading following:', error)
    }
}

const closeFollowersList = () => {
    showFollowersList.value = false
    followersList.value = []
}

const closeFollowingList = () => {
    showFollowingList.value = false
    followingList.value = []
}

const unfollowUser = async (userId) => {
    try {
        await axios.post(`/web-api/user/${userId}/unfollow`)

        // Remove from following list
        followingList.value = followingList.value.filter(user => user.id !== userId)

        // Update stats
        profileData.stats.following--

        alert('User unfollowed successfully!')
    } catch (error) {
        console.error('Error unfollowing user:', error)
        alert('Failed to unfollow user. Please try again.')
    }
}

// Image compression utility for profile pictures
const compressProfileImage = (file, callback, quality = 0.8, maxWidth = 512) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
        // Calculate new dimensions (square crop for profile pictures)
        const size = Math.min(img.width, img.height)
        const x = (img.width - size) / 2
        const y = (img.height - size) / 2

        canvas.width = maxWidth
        canvas.height = maxWidth

        // Draw cropped and resized image
        ctx.drawImage(img, x, y, size, size, 0, 0, maxWidth, maxWidth)
        canvas.toBlob(callback, 'image/jpeg', quality)
    }

    img.src = URL.createObjectURL(file)
}

// Profile picture upload
const handleProfileImageUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    console.log('Selected file:', file.name, file.type, file.size)

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
        alert('Please select a valid image file (JPEG, PNG, or GIF)')
        return
    }

    // Compress image if it's larger than 1MB or always for profile pictures
    if (file.size > 1024 * 1024 || true) {
        compressProfileImage(file, async (compressedBlob) => {
            if (compressedBlob) {
                const compressedFile = new File([compressedBlob], 'profile-image.jpg', { type: 'image/jpeg' })
                console.log('Image compressed:', file.size, '->', compressedFile.size)
                await uploadProfileImage(compressedFile)
            }
        })
    } else {
        await uploadProfileImage(file)
    }
}

const uploadProfileImage = async (file) => {

    uploadingImage.value = true
    try {
        const formData = new FormData()
        formData.append('profile_image', file)



        const response = await axios.post('/web-api/user/profile-image', formData, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })

        console.log('Upload response:', response.data)

        // Update the user data with new profile image
        // The backend returns just the path, so we need to construct the full URL
        const imagePath = response.data.profile_image
        const imageUrl = imagePath.startsWith('http') ? imagePath : `/storage/${imagePath}`
        profileData.user.profile_image = imageUrl

        // Show success message
        alert('Profile picture updated successfully!')
    } catch (error) {
        console.error('Error uploading profile image:', error)

        if (error.response?.status === 422) {
            const errors = error.response.data.errors
            if (errors) {
                const errorMessages = Object.values(errors).flat().join(', ')
                alert('Validation error: ' + errorMessages)
            } else {
                alert('Validation failed. Please check your file and try again.')
            }
        } else if (error.response?.status === 403) {
            alert('Permission denied. Please check your authentication.')
        } else {
            alert('Failed to upload profile picture. Please try again.')
        }
    } finally {
        uploadingImage.value = false
    }
}

// Watch for show prop changes
onMounted(() => {
    if (props.show) {
        loadProfile()
    }
})

// Watch for prop changes
const { show } = toRefs(props)
watch(show, (newValue) => {
    if (newValue) {
        loadProfile()
    }
})
</script>

<template>
    <!-- Profile Modal -->
    <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div 
                class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
                @click="closeProfile"
            ></div>

            <!-- Modal panel -->
            <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Profile</h3>
                    <button
                        @click="closeProfile"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex items-center justify-center py-12">
                    <div class="w-8 h-8 border-4 border-teal-200 border-t-teal-500 rounded-full animate-spin"></div>
                </div>

                <!-- Profile Content -->
                <div v-else class="space-y-6">
                    <!-- Profile Header -->
                    <div class="text-center">
                        <!-- Profile Image -->
                        <div class="relative inline-block mb-4">
                            <div class="relative">
                                <img
                                    :src="profileImage"
                                    :alt="displayName"
                                    class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                                />
                                <!-- Upload overlay - only for own profile -->
                                <div v-if="isOwnProfile" class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
                                    <input
                                        type="file"
                                        accept="image/*"
                                        @change="handleProfileImageUpload"
                                        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                        :disabled="uploadingImage"
                                    />
                                    <div v-if="uploadingImage" class="text-white">
                                        <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                    <div v-else class="text-white text-center">
                                        <svg class="w-6 h-6 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <span class="text-xs">Change</span>
                                    </div>
                                </div>
                            </div>
                            <!-- Founder Badge -->
                            <div v-if="isFounderMember" class="absolute -bottom-1 -right-1 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center border-2 border-white">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                            </div>
                        </div>

                        <!-- Name and Username -->
                        <h2 class="text-xl font-bold text-gray-900 mb-1">{{ displayName }}</h2>
                        <p class="text-gray-500 text-sm mb-4">{{ username }}</p>

                        <!-- Bio Section -->
                        <div class="mb-4">
                            <!-- Bio Display/Edit -->
                            <div v-if="!editingBio" class="text-center">
                                <p v-if="profileData.user?.bio" class="text-gray-700 text-sm leading-relaxed mb-2">
                                    {{ profileData.user.bio }}
                                </p>
                                <p v-else-if="isOwnProfile" class="text-gray-400 text-sm italic mb-2">
                                    Add a bio to tell others about yourself
                                </p>
                                <p v-else-if="!profileData.user?.bio" class="text-gray-400 text-sm italic mb-2">
                                    No bio added yet
                                </p>
                                <button
                                    v-if="isOwnProfile"
                                    @click="startEditingBio"
                                    class="text-blue-600 hover:text-blue-700 text-sm font-medium"
                                >
                                    {{ profileData.user?.bio ? 'Edit Bio' : 'Add Bio' }}
                                </button>
                            </div>

                            <!-- Bio Edit Form - Only for own profile -->
                            <div v-else-if="isOwnProfile" class="space-y-3">
                                <textarea
                                    v-model="bioText"
                                    placeholder="Tell others about yourself..."
                                    class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                    rows="3"
                                    maxlength="500"
                                ></textarea>
                                <div class="flex justify-center space-x-3">
                                    <button
                                        @click="saveBio"
                                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                                    >
                                        Save
                                    </button>
                                    <button
                                        @click="cancelEditingBio"
                                        class="px-4 py-2 bg-gray-200 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-300 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Founder Member Badge -->
                        <div v-if="isFounderMember" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 mb-4">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            Founders' Club Member
                        </div>

                        <!-- Stats -->
                        <div class="flex justify-center space-x-8 py-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">{{ profileData.stats.posts }}</div>
                                <div class="text-sm text-gray-500">Posts</div>
                            </div>
                            <button
                                @click="showFollowers"
                                class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors"
                            >
                                <div class="text-2xl font-bold text-gray-900">{{ profileData.stats.followers }}</div>
                                <div class="text-sm text-gray-500">Followers</div>
                            </button>
                            <button
                                @click="showFollowing"
                                class="text-center hover:bg-gray-50 rounded-lg p-2 transition-colors"
                            >
                                <div class="text-2xl font-bold text-gray-900">{{ profileData.stats.following }}</div>
                                <div class="text-sm text-gray-500">Following</div>
                            </button>
                        </div>

                        <!-- Follow Button - only for other users -->
                        <div v-if="!isOwnProfile" class="flex justify-center mb-4">
                            <button
                                @click="toggleFollow"
                                :disabled="followLoading"
                                :class="[
                                    'px-6 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2',
                                    followLoading
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : isFollowing
                                        ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                        : 'bg-blue-600 text-white hover:bg-blue-700'
                                ]"
                            >
                                <svg v-if="followLoading" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>
                                    {{ followLoading ? 'Loading...' : (isFollowing ? 'Following' : 'Follow') }}
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Stories Section -->
                    <div v-if="profileData.stories.length > 0" class="border-t border-gray-100 pt-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Stories</h4>
                        <div class="flex space-x-3 overflow-x-auto pb-2">
                            <div
                                v-for="story in profileData.stories.slice(0, 6)"
                                :key="story.id"
                                class="flex-shrink-0"
                            >
                                <div class="w-16 h-16 rounded-xl bg-gradient-to-br from-teal-400 to-cyan-500 p-0.5">
                                    <img
                                        :src="story.media_url || '/images/default-avatar.png'"
                                        :alt="'Story'"
                                        class="w-full h-full rounded-xl object-cover"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Posts Grid -->
                    <div v-if="profileData.posts.length > 0" class="border-t border-gray-100 pt-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-sm font-medium text-gray-900">Posts</h4>
                            <span class="text-xs text-gray-500">{{ profileData.posts.length }} posts</span>
                        </div>
                        <div class="grid grid-cols-3 gap-2">
                            <div
                                v-for="post in profileData.posts.slice(0, 9)"
                                :key="post.id"
                                class="aspect-square rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
                                @click="openPostDetail(post)"
                            >
                                <!-- Image Post -->
                                <img
                                    v-if="post.media_url"
                                    :src="post.media_url"
                                    :alt="post.caption"
                                    class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                                />
                                <!-- Text Post -->
                                <div v-else class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 text-white p-3">
                                    <div class="text-center">
                                        <div class="text-xs font-medium leading-tight">
                                            {{ post.caption && post.caption.length > 60 ? post.caption.substring(0, 60) + '...' : post.caption }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-if="!loading && profileData.posts.length === 0 && profileData.stories.length === 0" class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <p class="text-gray-500 text-sm">No posts or stories yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Followers Modal -->
    <div v-if="showFollowersList" class="fixed inset-0 z-60 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div
                class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
                @click="closeFollowersList"
            ></div>

            <!-- Modal panel -->
            <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Followers</h3>
                    <button
                        @click="closeFollowersList"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Followers List -->
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    <div
                        v-for="follower in followersList"
                        :key="follower.id"
                        class="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                        <img
                            :src="follower.profile_image ?
                                (follower.profile_image.startsWith('http') ?
                                    follower.profile_image :
                                    `/storage/${follower.profile_image}`) :
                                '/images/default-avatar.svg'"
                            :alt="follower.name"
                            class="w-10 h-10 rounded-full object-cover"
                        />
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">{{ follower.name }}</p>
                            <div v-if="follower.is_founder_member" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 mt-1">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                                Founder
                            </div>
                        </div>
                    </div>
                    <div v-if="followersList.length === 0" class="text-center py-8">
                        <p class="text-gray-500">No followers yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Following Modal -->
    <div v-if="showFollowingList" class="fixed inset-0 z-60 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div
                class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
                @click="closeFollowingList"
            ></div>

            <!-- Modal panel -->
            <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Following</h3>
                    <button
                        @click="closeFollowingList"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Following List -->
                <div class="space-y-3 max-h-96 overflow-y-auto">
                    <div
                        v-for="following in followingList"
                        :key="following.id"
                        class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                        <div class="flex items-center space-x-3">
                            <img
                                :src="following.profile_image ?
                                    (following.profile_image.startsWith('http') ?
                                        following.profile_image :
                                        `/storage/${following.profile_image}`) :
                                    '/images/default-avatar.svg'"
                                :alt="following.name"
                                class="w-10 h-10 rounded-full object-cover"
                            />
                            <div>
                                <p class="font-medium text-gray-900">{{ following.name }}</p>
                                <div v-if="following.is_founder_member" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 mt-1">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    Founder
                                </div>
                            </div>
                        </div>
                        <!-- Unfollow button for own profile -->
                        <button
                            v-if="isOwnProfile"
                            @click="unfollowUser(following.id)"
                            class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                        >
                            Unfollow
                        </button>
                    </div>
                    <div v-if="followingList.length === 0" class="text-center py-8">
                        <p class="text-gray-500">Not following anyone yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Custom scrollbar for stories */
.overflow-x-auto::-webkit-scrollbar {
    height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>
