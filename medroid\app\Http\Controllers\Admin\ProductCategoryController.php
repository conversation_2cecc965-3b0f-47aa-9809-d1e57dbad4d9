<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProductCategoryController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user->can('view products')) {
            abort(403, 'Unauthorized');
        }

        $query = ProductCategory::with(['parent', 'children'])
            ->withCount('products')
            ->orderBy('sort_order')
            ->orderBy('name');

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by parent
        if ($request->filled('parent_id')) {
            if ($request->parent_id === 'root') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->parent_id);
            }
        }

        $categories = $query->paginate(20);
        $parentCategories = ProductCategory::whereNull('parent_id')->active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'categories' => $categories,
                'parent_categories' => $parentCategories,
            ]);
        }

        return Inertia::render('Admin/Categories/Index', [
            'categories' => $categories,
            'parentCategories' => $parentCategories,
            'filters' => $request->only(['search', 'parent_id']),
        ]);
    }

    public function create(Request $request)
    {
        $user = Auth::user();

        if (!$user->can('create products')) {
            abort(403, 'Unauthorized');
        }

        $parentCategories = ProductCategory::whereNull('parent_id')->active()->ordered()->get();

        if ($request->expectsJson()) {
            return response()->json([
                'parent_categories' => $parentCategories,
            ]);
        }

        return Inertia::render('Admin/Categories/Create', [
            'parentCategories' => $parentCategories,
        ]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        if (!$user->can('create products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:product_categories,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category = ProductCategory::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->is_active ?? true,
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Category created successfully',
                'category' => $category->load(['parent', 'children']),
            ]);
        }

        return redirect()->route('admin.categories')->with('success', 'Category created successfully');
    }

    public function show(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->can('view products')) {
            abort(403, 'Unauthorized');
        }

        $category = ProductCategory::with(['parent', 'children', 'products'])
            ->withCount('products')
            ->findOrFail($id);

        if ($request->expectsJson()) {
            return response()->json([
                'category' => $category,
            ]);
        }

        return Inertia::render('Admin/Categories/Show', [
            'category' => $category,
        ]);
    }

    public function edit(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->can('edit products')) {
            abort(403, 'Unauthorized');
        }

        $category = ProductCategory::with(['parent', 'children'])->findOrFail($id);
        $parentCategories = ProductCategory::whereNull('parent_id')
            ->where('id', '!=', $id) // Exclude self to prevent circular reference
            ->active()
            ->ordered()
            ->get();

        if ($request->expectsJson()) {
            return response()->json([
                'category' => $category,
                'parent_categories' => $parentCategories,
            ]);
        }

        return Inertia::render('Admin/Categories/Edit', [
            'category' => $category,
            'parentCategories' => $parentCategories,
        ]);
    }

    public function update(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->can('edit products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $category = ProductCategory::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:product_categories,id|not_in:' . $id, // Prevent self-reference
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? $category->sort_order,
            'is_active' => $request->is_active ?? $category->is_active,
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Category updated successfully',
                'category' => $category->load(['parent', 'children']),
            ]);
        }

        return redirect()->route('admin.categories')->with('success', 'Category updated successfully');
    }

    public function destroy(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->can('delete products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $category = ProductCategory::findOrFail($id);

            // Check if category has products
            if ($category->products()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category with existing products',
                ], 400);
            }

            // Check if category has child categories
            if ($category->children()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category with subcategories',
                ], 400);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'Category deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting category: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function toggleStatus(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->can('edit products')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            $category = ProductCategory::findOrFail($id);
            $category->update(['is_active' => !$category->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Category status updated successfully',
                'category' => $category,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating category status: ' . $e->getMessage(),
            ], 500);
        }
    }
}
