import{r as g,o as E,d as l,e as o,f as y,u as f,m as D,g as m,i as t,j as M,l as c,n as p,v,t as n,F as k,p as C,q as N,s as P,P as _,x,y as U,W as F}from"./vendor-BK2qhpQJ.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const j={class:"flex items-center justify-between"},L={class:"flex mt-2","aria-label":"Breadcrumb"},T={class:"inline-flex items-center space-x-1 md:space-x-3"},q={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},W={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},z={class:"py-12"},A={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},I={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},O={class:"p-6 text-gray-900 dark:text-gray-100"},R={key:0,class:"mt-1 text-sm text-red-600"},$={key:0,class:"mt-1 text-sm text-red-600"},G=["value"],H={key:0,class:"mt-1 text-sm text-red-600"},J={key:0,class:"mt-1 text-sm text-red-600"},K={class:"flex items-center"},Q={class:"flex items-center justify-end space-x-3"},X=["disabled"],Y={key:0,class:"fas fa-spinner fa-spin mr-2"},Z={key:1,class:"fas fa-save mr-2"},le={__name:"Create",setup(ee){const b=[{title:"Dashboard",href:"/dashboard"},{title:"Categories",href:"/admin/categories"},{title:"Create Category",href:"/admin/categories/create"}],u=g(!1),w=g([]),a=g({name:"",description:"",parent_id:"",sort_order:0,is_active:!0}),d=g({}),V=async()=>{try{const i=await window.axios.get("/admin/categories/create");w.value=i.data.parent_categories||[]}catch(i){console.error("Error fetching parent categories:",i)}},B=async()=>{var i,e,r;u.value=!0,d.value={};try{const s=new FormData;s.append("name",a.value.name),s.append("description",a.value.description),a.value.parent_id&&s.append("parent_id",a.value.parent_id),s.append("sort_order",a.value.sort_order),s.append("is_active",a.value.is_active?"1":"0");const h=await window.axios.post("/admin/save-category",s,{headers:{"Content-Type":"multipart/form-data"}});h.data.success?(alert("Category created successfully!"),F.visit("/admin/categories")):alert(h.data.message||"Error creating category")}catch(s){console.error("Error creating category:",s),((i=s.response)==null?void 0:i.status)===422?d.value=s.response.data.errors||{}:alert(((r=(e=s.response)==null?void 0:e.data)==null?void 0:r.message)||"Error creating category")}finally{u.value=!1}};return E(()=>{V()}),(i,e)=>(o(),l(k,null,[y(f(D),{title:"Create Category"}),y(S,null,{header:m(()=>[t("div",j,[t("div",null,[e[6]||(e[6]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Create Category ",-1)),t("nav",L,[t("ol",T,[(o(),l(k,null,C(b,(r,s)=>t("li",{key:s,class:"inline-flex items-center"},[s<b.length-1?(o(),U(f(_),{key:0,href:r.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:m(()=>[x(n(r.title),1)]),_:2},1032,["href"])):(o(),l("span",q,n(r.title),1)),s<b.length-1?(o(),l("svg",W,e[5]||(e[5]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):p("",!0)])),64))])])]),t("div",null,[y(f(_),{href:"/admin/categories",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2"},{default:m(()=>e[7]||(e[7]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),x(" Back to Categories ")])),_:1})])])]),default:m(()=>[t("div",z,[t("div",A,[t("div",I,[t("div",O,[t("form",{onSubmit:M(B,["prevent"]),class:"space-y-6"},[t("div",null,[e[8]||(e[8]=t("label",{for:"name",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Category Name * ",-1)),c(t("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=r=>a.value.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Enter category name"},null,512),[[v,a.value.name]]),d.value.name?(o(),l("p",R,n(d.value.name[0]),1)):p("",!0)]),t("div",null,[e[9]||(e[9]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Description ",-1)),c(t("textarea",{id:"description","onUpdate:modelValue":e[1]||(e[1]=r=>a.value.description=r),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Enter category description"},null,512),[[v,a.value.description]]),d.value.description?(o(),l("p",$,n(d.value.description[0]),1)):p("",!0)]),t("div",null,[e[11]||(e[11]=t("label",{for:"parent_id",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Parent Category ",-1)),c(t("select",{id:"parent_id","onUpdate:modelValue":e[2]||(e[2]=r=>a.value.parent_id=r),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[10]||(e[10]=t("option",{value:""},"Root Category (No Parent)",-1)),(o(!0),l(k,null,C(w.value,r=>(o(),l("option",{key:r.id,value:r.id},n(r.name),9,G))),128))],512),[[N,a.value.parent_id]]),d.value.parent_id?(o(),l("p",H,n(d.value.parent_id[0]),1)):p("",!0)]),t("div",null,[e[12]||(e[12]=t("label",{for:"sort_order",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Sort Order ",-1)),c(t("input",{id:"sort_order","onUpdate:modelValue":e[3]||(e[3]=r=>a.value.sort_order=r),type:"number",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0"},null,512),[[v,a.value.sort_order,void 0,{number:!0}]]),e[13]||(e[13]=t("p",{class:"mt-1 text-sm text-gray-500"},"Lower numbers appear first",-1)),d.value.sort_order?(o(),l("p",J,n(d.value.sort_order[0]),1)):p("",!0)]),t("div",null,[t("label",K,[c(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>a.value.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[P,a.value.is_active]]),e[14]||(e[14]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))]),e[15]||(e[15]=t("p",{class:"mt-1 text-sm text-gray-500"},"Inactive categories won't be visible to users",-1))]),t("div",Q,[y(f(_),{href:"/admin/categories",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:m(()=>e[16]||(e[16]=[x(" Cancel ")])),_:1}),t("button",{type:"submit",disabled:u.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},[u.value?(o(),l("i",Y)):(o(),l("i",Z)),x(" "+n(u.value?"Creating...":"Create Category"),1)],8,X)])],32)])])])])]),_:1})],64))}};export{le as default};
