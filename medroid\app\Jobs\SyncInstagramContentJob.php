<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\InstagramAccount;
use App\Services\InstagramService;
use Illuminate\Support\Facades\Log;

class SyncInstagramContentJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting Instagram content sync job');

        try {
            $instagramService = new InstagramService();

            // Get all active Instagram accounts
            $accounts = InstagramAccount::where('is_active', true)
                ->where('expires_at', '>', now())
                ->get();

            Log::info('Found ' . $accounts->count() . ' active Instagram accounts to sync');

            foreach ($accounts as $account) {
                try {
                    Log::info('Syncing content for Instagram account: ' . $account->username);

                    // Sync content for this account
                    $instagramService->syncAccountContent($account);

                    // Update last sync time
                    $account->update(['last_sync_at' => now()]);

                    Log::info('Successfully synced content for account: ' . $account->username);

                } catch (\Exception $e) {
                    Log::error('Failed to sync Instagram account: ' . $account->username, [
                        'error' => $e->getMessage(),
                        'account_id' => $account->id
                    ]);

                    // Continue with other accounts even if one fails
                    continue;
                }
            }

            Log::info('Instagram content sync job completed successfully');

        } catch (\Exception $e) {
            Log::error('Instagram content sync job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Instagram content sync job failed permanently', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
