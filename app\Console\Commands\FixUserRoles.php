<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use Spatie\Permission\Models\Role;

class FixUserRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:fix-roles {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix user role assignments and create missing profiles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
        }

        $this->info('Starting user role fix...');

        // Ensure roles exist
        $this->ensureRolesExist($dryRun);

        // Fix users without Spatie roles
        $this->fixUsersWithoutSpatieRoles($dryRun);

        // Fix users without profiles
        $this->fixUsersWithoutProfiles($dryRun);

        $this->info('User role fix completed!');
    }

    private function ensureRolesExist($dryRun)
    {
        $roles = ['admin', 'manager', 'provider', 'patient'];
        
        foreach ($roles as $roleName) {
            $role = Role::where('name', $roleName)->where('guard_name', 'web')->first();
            if (!$role) {
                if ($dryRun) {
                    $this->warn("Would create role: {$roleName}");
                } else {
                    Role::create(['name' => $roleName, 'guard_name' => 'web']);
                    $this->info("Created role: {$roleName}");
                }
            }
        }
    }

    private function fixUsersWithoutSpatieRoles($dryRun)
    {
        $this->info('Checking users without Spatie roles...');
        
        $usersWithoutRoles = User::whereDoesntHave('roles')->get();
        
        $this->info("Found {$usersWithoutRoles->count()} users without Spatie roles");

        foreach ($usersWithoutRoles as $user) {
            $roleToAssign = $user->role ?: 'patient';
            
            if ($dryRun) {
                $this->line("Would assign '{$roleToAssign}' role to user: {$user->email}");
            } else {
                try {
                    $user->assignRole($roleToAssign);
                    $this->info("✓ Assigned '{$roleToAssign}' role to: {$user->email}");
                } catch (\Exception $e) {
                    $this->error("✗ Failed to assign role to {$user->email}: {$e->getMessage()}");
                }
            }
        }
    }

    private function fixUsersWithoutProfiles($dryRun)
    {
        $this->info('Checking users without profiles...');
        
        // Fix patients without profiles
        $patientsWithoutProfiles = User::where('role', 'patient')
            ->whereDoesntHave('patient')
            ->get();
            
        $this->info("Found {$patientsWithoutProfiles->count()} patients without profiles");

        foreach ($patientsWithoutProfiles as $user) {
            if ($dryRun) {
                $this->line("Would create patient profile for: {$user->email}");
            } else {
                try {
                    $this->createPatientProfile($user);
                    $this->info("✓ Created patient profile for: {$user->email}");
                } catch (\Exception $e) {
                    $this->error("✗ Failed to create patient profile for {$user->email}: {$e->getMessage()}");
                }
            }
        }

        // Fix providers without profiles
        $providersWithoutProfiles = User::where('role', 'provider')
            ->whereDoesntHave('provider')
            ->get();
            
        $this->info("Found {$providersWithoutProfiles->count()} providers without profiles");

        foreach ($providersWithoutProfiles as $user) {
            if ($dryRun) {
                $this->line("Would create provider profile for: {$user->email}");
            } else {
                try {
                    $this->createProviderProfile($user);
                    $this->info("✓ Created provider profile for: {$user->email}");
                } catch (\Exception $e) {
                    $this->error("✗ Failed to create provider profile for {$user->email}: {$e->getMessage()}");
                }
            }
        }
    }

    private function createPatientProfile($user)
    {
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first();
        }

        $user->patient()->create([
            'user_id' => $user->id,
            'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
            'phone' => null,
            'address' => null,
            'emergency_contact' => null,
            'medical_history' => null,
            'allergies' => null,
            'current_medications' => null,
        ]);

        // Initialize user credits if not exists
        if (!$user->credit) {
            $user->credit()->create([
                'balance' => 0.00,
                'total_earned' => 0.00,
                'total_spent' => 0.00,
            ]);
        }
    }

    private function createProviderProfile($user)
    {
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first();
        }

        $user->provider()->create([
            'user_id' => $user->id,
            'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
            'specialization' => 'General Practice',
            'license_number' => 'PENDING',
            'verification_status' => 'pending',
            'verified_at' => null,
            'gender' => null,
            'bio' => null,
            'rating' => 0,
            'weekly_availability' => [
                ['day' => 'Monday', 'slots' => []],
                ['day' => 'Tuesday', 'slots' => []],
                ['day' => 'Wednesday', 'slots' => []],
                ['day' => 'Thursday', 'slots' => []],
                ['day' => 'Friday', 'slots' => []],
                ['day' => 'Saturday', 'slots' => []],
                ['day' => 'Sunday', 'slots' => []],
            ],
            'absences' => [],
            'practice_locations' => [],
        ]);
    }
}
