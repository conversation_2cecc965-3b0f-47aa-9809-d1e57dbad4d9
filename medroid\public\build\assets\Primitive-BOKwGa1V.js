import{F as Te,M as we,O as Ee,Q as Ne,R as Le,h as ee}from"./vendor-BK2qhpQJ.js";function ke(e){var r,t,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(r=0;r<a;r++)e[r]&&(t=ke(e[r]))&&(o&&(o+=" "),o+=t)}else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function Ve(){for(var e,r,t=0,o="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=ke(e))&&(o&&(o+=" "),o+=r);return o}const ae="-",je=e=>{const r=Oe(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const u=i.split(ae);return u[0]===""&&u.length!==1&&u.shift(),ye(u,r)||Fe(i)},getConflictingClassGroupIds:(i,u)=>{const p=t[i]||[];return u&&o[i]?[...p,...o[i]]:p}}},ye=(e,r)=>{var i;if(e.length===0)return r.classGroupId;const t=e[0],o=r.nextPart.get(t),a=o?ye(e.slice(1),o):void 0;if(a)return a;if(r.validators.length===0)return;const c=e.join(ae);return(i=r.validators.find(({validator:u})=>u(c)))==null?void 0:i.classGroupId},ge=/^\[(.+)\]$/,Fe=e=>{if(ge.test(e)){const r=ge.exec(e)[1],t=r==null?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},Oe=e=>{const{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(const a in t)te(t[a],o,a,r);return o},te=(e,r,t,o)=>{e.forEach(a=>{if(typeof a=="string"){const c=a===""?r:be(r,a);c.classGroupId=t;return}if(typeof a=="function"){if(_e(a)){te(a(o),r,t,o);return}r.validators.push({validator:a,classGroupId:t});return}Object.entries(a).forEach(([c,i])=>{te(i,be(r,c),t,o)})})},be=(e,r)=>{let t=e;return r.split(ae).forEach(o=>{t.nextPart.has(o)||t.nextPart.set(o,{nextPart:new Map,validators:[]}),t=t.nextPart.get(o)}),t},_e=e=>e.isThemeGetter,Be=e=>{if(e<1)return{get:()=>{},set:()=>{}};let r=0,t=new Map,o=new Map;const a=(c,i)=>{t.set(c,i),r++,r>e&&(r=0,o=t,t=new Map)};return{get(c){let i=t.get(c);if(i!==void 0)return i;if((i=o.get(c))!==void 0)return a(c,i),i},set(c,i){t.has(c)?t.set(c,i):a(c,i)}}},se="!",ne=":",We=ne.length,$e=e=>{const{prefix:r,experimentalParseClassName:t}=e;let o=a=>{const c=[];let i=0,u=0,p=0,b;for(let k=0;k<a.length;k++){let y=a[k];if(i===0&&u===0){if(y===ne){c.push(a.slice(p,k)),p=k+We;continue}if(y==="/"){b=k;continue}}y==="["?i++:y==="]"?i--:y==="("?u++:y===")"&&u--}const f=c.length===0?a:a.substring(p),S=Ue(f),F=S!==f,O=b&&b>p?b-p:void 0;return{modifiers:c,hasImportantModifier:F,baseClassName:S,maybePostfixModifierPosition:O}};if(r){const a=r+ne,c=o;o=i=>i.startsWith(a)?c(i.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(t){const a=o;o=c=>t({className:c,parseClassName:a})}return o},Ue=e=>e.endsWith(se)?e.substring(0,e.length-1):e.startsWith(se)?e.substring(1):e,qe=e=>{const r=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const a=[];let c=[];return o.forEach(i=>{i[0]==="["||r[i]?(a.push(...c.sort(),i),c=[]):c.push(i)}),a.push(...c.sort()),a}},He=e=>({cache:Be(e.cacheSize),parseClassName:$e(e),sortModifiers:qe(e),...je(e)}),Je=/\s+/,Qe=(e,r)=>{const{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:c}=r,i=[],u=e.trim().split(Je);let p="";for(let b=u.length-1;b>=0;b-=1){const f=u[b],{isExternal:S,modifiers:F,hasImportantModifier:O,baseClassName:k,maybePostfixModifierPosition:y}=t(f);if(S){p=f+(p.length>0?" "+p:p);continue}let G=!!y,A=o(G?k.substring(0,y):k);if(!A){if(!G){p=f+(p.length>0?" "+p:p);continue}if(A=o(k),!A){p=f+(p.length>0?" "+p:p);continue}G=!1}const $=c(F).join(":"),_=O?$+se:$,T=_+A;if(i.includes(T))continue;i.push(T);const E=a(A,G);for(let P=0;P<E.length;++P){const B=E[P];i.push(_+B)}p=f+(p.length>0?" "+p:p)}return p};function Xe(){let e=0,r,t,o="";for(;e<arguments.length;)(r=arguments[e++])&&(t=ve(r))&&(o&&(o+=" "),o+=t);return o}const ve=e=>{if(typeof e=="string")return e;let r,t="";for(let o=0;o<e.length;o++)e[o]&&(r=ve(e[o]))&&(t&&(t+=" "),t+=r);return t};function De(e,...r){let t,o,a,c=i;function i(p){const b=r.reduce((f,S)=>S(f),e());return t=He(b),o=t.cache.get,a=t.cache.set,c=u,u(p)}function u(p){const b=o(p);if(b)return b;const f=Qe(p,t);return a(p,f),f}return function(){return c(Xe.apply(null,arguments))}}const g=e=>{const r=t=>t[e]||[];return r.isThemeGetter=!0,r},ze=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ce=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ke=/^\d+\/\d+$/,Ye=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ze=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,eo=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,oo=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ro=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=e=>Ke.test(e),m=e=>!!e&&!Number.isNaN(Number(e)),M=e=>!!e&&Number.isInteger(Number(e)),oe=e=>e.endsWith("%")&&m(e.slice(0,-1)),C=e=>Ye.test(e),to=()=>!0,so=e=>Ze.test(e)&&!eo.test(e),Se=()=>!1,no=e=>oo.test(e),ao=e=>ro.test(e),io=e=>!s(e)&&!n(e),lo=e=>V(e,Pe,Se),s=e=>ze.test(e),R=e=>V(e,Ie,so),re=e=>V(e,fo,m),he=e=>V(e,Me,Se),co=e=>V(e,Ae,ao),X=e=>V(e,Re,no),n=e=>Ce.test(e),W=e=>j(e,Ie),mo=e=>j(e,go),xe=e=>j(e,Me),po=e=>j(e,Pe),uo=e=>j(e,Ae),D=e=>j(e,Re,!0),V=(e,r,t)=>{const o=ze.exec(e);return o?o[1]?r(o[1]):t(o[2]):!1},j=(e,r,t=!1)=>{const o=Ce.exec(e);return o?o[1]?r(o[1]):t:!1},Me=e=>e==="position"||e==="percentage",Ae=e=>e==="image"||e==="url",Pe=e=>e==="length"||e==="size"||e==="bg-size",Ie=e=>e==="length",fo=e=>e==="number",go=e=>e==="family-name",Re=e=>e==="shadow",bo=()=>{const e=g("color"),r=g("font"),t=g("text"),o=g("font-weight"),a=g("tracking"),c=g("leading"),i=g("breakpoint"),u=g("container"),p=g("spacing"),b=g("radius"),f=g("shadow"),S=g("inset-shadow"),F=g("text-shadow"),O=g("drop-shadow"),k=g("blur"),y=g("perspective"),G=g("aspect"),A=g("ease"),$=g("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],T=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...T(),n,s],P=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],d=()=>[n,s,p],v=()=>[L,"full","auto",...d()],ie=()=>[M,"none","subgrid",n,s],le=()=>["auto",{span:["full",M,n,s]},M,n,s],U=()=>[M,"auto",n,s],ce=()=>["auto","min","max","fr",n,s],K=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...d()],I=()=>[L,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...d()],l=()=>[e,n,s],de=()=>[...T(),xe,he,{position:[n,s]}],me=()=>["no-repeat",{repeat:["","x","y","space","round"]}],pe=()=>["auto","cover","contain",po,lo,{size:[n,s]}],Y=()=>[oe,W,R],x=()=>["","none","full",b,n,s],w=()=>["",m,W,R],q=()=>["solid","dashed","dotted","double"],ue=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],h=()=>[m,oe,xe,he],fe=()=>["","none",k,n,s],H=()=>["none",m,n,s],J=()=>["none",m,n,s],Z=()=>[m,n,s],Q=()=>[L,"full",...d()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[to],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[io],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",m],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",L,s,n,G]}],container:["container"],columns:[{columns:[m,s,n,u]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:v()}],"inset-x":[{"inset-x":v()}],"inset-y":[{"inset-y":v()}],start:[{start:v()}],end:[{end:v()}],top:[{top:v()}],right:[{right:v()}],bottom:[{bottom:v()}],left:[{left:v()}],visibility:["visible","invisible","collapse"],z:[{z:[M,"auto",n,s]}],basis:[{basis:[L,"full","auto",u,...d()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[m,L,"auto","initial","none",s]}],grow:[{grow:["",m,n,s]}],shrink:[{shrink:["",m,n,s]}],order:[{order:[M,"first","last","none",n,s]}],"grid-cols":[{"grid-cols":ie()}],"col-start-end":[{col:le()}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":ie()}],"row-start-end":[{row:le()}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ce()}],"auto-rows":[{"auto-rows":ce()}],gap:[{gap:d()}],"gap-x":[{"gap-x":d()}],"gap-y":[{"gap-y":d()}],"justify-content":[{justify:[...K(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...K()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":K()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:d()}],px:[{px:d()}],py:[{py:d()}],ps:[{ps:d()}],pe:[{pe:d()}],pt:[{pt:d()}],pr:[{pr:d()}],pb:[{pb:d()}],pl:[{pl:d()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":d()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":d()}],"space-y-reverse":["space-y-reverse"],size:[{size:I()}],w:[{w:[u,"screen",...I()]}],"min-w":[{"min-w":[u,"screen","none",...I()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[i]},...I()]}],h:[{h:["screen",...I()]}],"min-h":[{"min-h":["screen","none",...I()]}],"max-h":[{"max-h":["screen",...I()]}],"font-size":[{text:["base",t,W,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,n,re]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",oe,s]}],"font-family":[{font:[mo,s,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,n,s]}],"line-clamp":[{"line-clamp":[m,"none",n,re]}],leading:[{leading:[c,...d()]}],"list-image":[{"list-image":["none",n,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",n,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:l()}],"text-color":[{text:l()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[m,"from-font","auto",n,R]}],"text-decoration-color":[{decoration:l()}],"underline-offset":[{"underline-offset":[m,"auto",n,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:d()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",n,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",n,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:de()}],"bg-repeat":[{bg:me()}],"bg-size":[{bg:pe()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},M,n,s],radial:["",n,s],conic:[M,n,s]},uo,co]}],"bg-color":[{bg:l()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:l()}],"gradient-via":[{via:l()}],"gradient-to":[{to:l()}],rounded:[{rounded:x()}],"rounded-s":[{"rounded-s":x()}],"rounded-e":[{"rounded-e":x()}],"rounded-t":[{"rounded-t":x()}],"rounded-r":[{"rounded-r":x()}],"rounded-b":[{"rounded-b":x()}],"rounded-l":[{"rounded-l":x()}],"rounded-ss":[{"rounded-ss":x()}],"rounded-se":[{"rounded-se":x()}],"rounded-ee":[{"rounded-ee":x()}],"rounded-es":[{"rounded-es":x()}],"rounded-tl":[{"rounded-tl":x()}],"rounded-tr":[{"rounded-tr":x()}],"rounded-br":[{"rounded-br":x()}],"rounded-bl":[{"rounded-bl":x()}],"border-w":[{border:w()}],"border-w-x":[{"border-x":w()}],"border-w-y":[{"border-y":w()}],"border-w-s":[{"border-s":w()}],"border-w-e":[{"border-e":w()}],"border-w-t":[{"border-t":w()}],"border-w-r":[{"border-r":w()}],"border-w-b":[{"border-b":w()}],"border-w-l":[{"border-l":w()}],"divide-x":[{"divide-x":w()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":w()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:l()}],"border-color-x":[{"border-x":l()}],"border-color-y":[{"border-y":l()}],"border-color-s":[{"border-s":l()}],"border-color-e":[{"border-e":l()}],"border-color-t":[{"border-t":l()}],"border-color-r":[{"border-r":l()}],"border-color-b":[{"border-b":l()}],"border-color-l":[{"border-l":l()}],"divide-color":[{divide:l()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[m,n,s]}],"outline-w":[{outline:["",m,W,R]}],"outline-color":[{outline:l()}],shadow:[{shadow:["","none",f,D,X]}],"shadow-color":[{shadow:l()}],"inset-shadow":[{"inset-shadow":["none",S,D,X]}],"inset-shadow-color":[{"inset-shadow":l()}],"ring-w":[{ring:w()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:l()}],"ring-offset-w":[{"ring-offset":[m,R]}],"ring-offset-color":[{"ring-offset":l()}],"inset-ring-w":[{"inset-ring":w()}],"inset-ring-color":[{"inset-ring":l()}],"text-shadow":[{"text-shadow":["none",F,D,X]}],"text-shadow-color":[{"text-shadow":l()}],opacity:[{opacity:[m,n,s]}],"mix-blend":[{"mix-blend":[...ue(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ue()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[m]}],"mask-image-linear-from-pos":[{"mask-linear-from":h()}],"mask-image-linear-to-pos":[{"mask-linear-to":h()}],"mask-image-linear-from-color":[{"mask-linear-from":l()}],"mask-image-linear-to-color":[{"mask-linear-to":l()}],"mask-image-t-from-pos":[{"mask-t-from":h()}],"mask-image-t-to-pos":[{"mask-t-to":h()}],"mask-image-t-from-color":[{"mask-t-from":l()}],"mask-image-t-to-color":[{"mask-t-to":l()}],"mask-image-r-from-pos":[{"mask-r-from":h()}],"mask-image-r-to-pos":[{"mask-r-to":h()}],"mask-image-r-from-color":[{"mask-r-from":l()}],"mask-image-r-to-color":[{"mask-r-to":l()}],"mask-image-b-from-pos":[{"mask-b-from":h()}],"mask-image-b-to-pos":[{"mask-b-to":h()}],"mask-image-b-from-color":[{"mask-b-from":l()}],"mask-image-b-to-color":[{"mask-b-to":l()}],"mask-image-l-from-pos":[{"mask-l-from":h()}],"mask-image-l-to-pos":[{"mask-l-to":h()}],"mask-image-l-from-color":[{"mask-l-from":l()}],"mask-image-l-to-color":[{"mask-l-to":l()}],"mask-image-x-from-pos":[{"mask-x-from":h()}],"mask-image-x-to-pos":[{"mask-x-to":h()}],"mask-image-x-from-color":[{"mask-x-from":l()}],"mask-image-x-to-color":[{"mask-x-to":l()}],"mask-image-y-from-pos":[{"mask-y-from":h()}],"mask-image-y-to-pos":[{"mask-y-to":h()}],"mask-image-y-from-color":[{"mask-y-from":l()}],"mask-image-y-to-color":[{"mask-y-to":l()}],"mask-image-radial":[{"mask-radial":[n,s]}],"mask-image-radial-from-pos":[{"mask-radial-from":h()}],"mask-image-radial-to-pos":[{"mask-radial-to":h()}],"mask-image-radial-from-color":[{"mask-radial-from":l()}],"mask-image-radial-to-color":[{"mask-radial-to":l()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":T()}],"mask-image-conic-pos":[{"mask-conic":[m]}],"mask-image-conic-from-pos":[{"mask-conic-from":h()}],"mask-image-conic-to-pos":[{"mask-conic-to":h()}],"mask-image-conic-from-color":[{"mask-conic-from":l()}],"mask-image-conic-to-color":[{"mask-conic-to":l()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:de()}],"mask-repeat":[{mask:me()}],"mask-size":[{mask:pe()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",n,s]}],filter:[{filter:["","none",n,s]}],blur:[{blur:fe()}],brightness:[{brightness:[m,n,s]}],contrast:[{contrast:[m,n,s]}],"drop-shadow":[{"drop-shadow":["","none",O,D,X]}],"drop-shadow-color":[{"drop-shadow":l()}],grayscale:[{grayscale:["",m,n,s]}],"hue-rotate":[{"hue-rotate":[m,n,s]}],invert:[{invert:["",m,n,s]}],saturate:[{saturate:[m,n,s]}],sepia:[{sepia:["",m,n,s]}],"backdrop-filter":[{"backdrop-filter":["","none",n,s]}],"backdrop-blur":[{"backdrop-blur":fe()}],"backdrop-brightness":[{"backdrop-brightness":[m,n,s]}],"backdrop-contrast":[{"backdrop-contrast":[m,n,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",m,n,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m,n,s]}],"backdrop-invert":[{"backdrop-invert":["",m,n,s]}],"backdrop-opacity":[{"backdrop-opacity":[m,n,s]}],"backdrop-saturate":[{"backdrop-saturate":[m,n,s]}],"backdrop-sepia":[{"backdrop-sepia":["",m,n,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":d()}],"border-spacing-x":[{"border-spacing-x":d()}],"border-spacing-y":[{"border-spacing-y":d()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",n,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[m,"initial",n,s]}],ease:[{ease:["linear","initial",A,n,s]}],delay:[{delay:[m,n,s]}],animate:[{animate:["none",$,n,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,n,s]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:H()}],"rotate-x":[{"rotate-x":H()}],"rotate-y":[{"rotate-y":H()}],"rotate-z":[{"rotate-z":H()}],scale:[{scale:J()}],"scale-x":[{"scale-x":J()}],"scale-y":[{"scale-y":J()}],"scale-z":[{"scale-z":J()}],"scale-3d":["scale-3d"],skew:[{skew:Z()}],"skew-x":[{"skew-x":Z()}],"skew-y":[{"skew-y":Z()}],transform:[{transform:[n,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Q()}],"translate-x":[{"translate-x":Q()}],"translate-y":[{"translate-y":Q()}],"translate-z":[{"translate-z":Q()}],"translate-none":["translate-none"],accent:[{accent:l()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:l()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",n,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":d()}],"scroll-mx":[{"scroll-mx":d()}],"scroll-my":[{"scroll-my":d()}],"scroll-ms":[{"scroll-ms":d()}],"scroll-me":[{"scroll-me":d()}],"scroll-mt":[{"scroll-mt":d()}],"scroll-mr":[{"scroll-mr":d()}],"scroll-mb":[{"scroll-mb":d()}],"scroll-ml":[{"scroll-ml":d()}],"scroll-p":[{"scroll-p":d()}],"scroll-px":[{"scroll-px":d()}],"scroll-py":[{"scroll-py":d()}],"scroll-ps":[{"scroll-ps":d()}],"scroll-pe":[{"scroll-pe":d()}],"scroll-pt":[{"scroll-pt":d()}],"scroll-pr":[{"scroll-pr":d()}],"scroll-pb":[{"scroll-pb":d()}],"scroll-pl":[{"scroll-pl":d()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",n,s]}],fill:[{fill:["none",...l()]}],"stroke-w":[{stroke:[m,W,R,re]}],stroke:[{stroke:["none",...l()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},ho=De(bo);function yo(...e){return ho(Ve(e))}function Ge(e){return e?e.flatMap(r=>r.type===Te?Ge(r.children):[r]):[]}const xo=we({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:r,slots:t}){return()=>{var p,b;if(!t.default)return null;const o=Ge(t.default()),a=o.findIndex(f=>f.type!==Ee);if(a===-1)return o;const c=o[a];(p=c.props)==null||delete p.ref;const i=c.props?Ne(r,c.props):r;r.class&&((b=c.props)!=null&&b.class)&&delete c.props.class;const u=Le(c,i);for(const f in i)f.startsWith("on")&&(u.props||(u.props={}),u.props[f]=i[f]);return o.length===1?u:(o[a]=u,o)}}}),wo=["area","img","input"],vo=we({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:r,slots:t}){const o=e.asChild?"template":e.as;return typeof o=="string"&&wo.includes(o)?()=>ee(o,r):o!=="template"?()=>ee(e.as,r,{default:t.default}):()=>ee(xo,r,{default:t.default})}});export{vo as P,xo as S,Ve as a,yo as c,Ge as r};
