<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'

interface Props {
  content: string
  isThinking?: boolean
  autoCollapse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isThinking: false,
  autoCollapse: true
})

const isOpen = ref(false)
const showPreview = ref(false)

// Parse the thinking content to extract title and body
const parseThinkingContent = (content: string) => {
  const lines = content.split('\n').filter(line => line.trim() !== '')
  const title = lines[0] || '🧠 Clinical Reasoning'
  const body = lines.slice(1).join('\n').trim()
  return { title, body }
}

const { title, body } = parseThinkingContent(props.content)

// Clean and format content
const cleanContent = (content: string): string => {
  return content
    .replace(/#{1,6}\s*(.*?)(\n|$)/g, '<div class="mb-2"><strong>$1</strong></div>$2') // Convert all headers (# ## ### etc) to bold divs
    .replace(/Step\s+(\d+):\s*(.*?)(\n|$)/g, '<div class="mb-2"><strong>Step $1: $2</strong></div>$3') // Format steps with bold, less margin
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Convert **text** to <strong>text</strong>
    .replace(/\n\n/g, '</p><p class="mb-2">') // Convert double newlines to paragraph breaks, less margin
    .replace(/\n/g, '<br>') // Convert single newlines to line breaks
    .replace(/^/, '<p class="mb-2">') // Add opening paragraph tag, less margin
    .replace(/$/, '</p>') // Add closing paragraph tag
}

// Get preview lines (last 4-5 lines of content) with dynamic updates
const previewLines = computed(() => {
  const lines = body.split('\n').filter(line => line.trim() !== '')
  if (lines.length <= 1) return ''

  // Get last 4 lines (excluding the latest line which is shown separately)
  const previewLines = lines.slice(-5, -1)
  return previewLines.join('\n')
})

// Get the latest single line for dynamic display
const latestLine = computed(() => {
  const lines = body.split('\n').filter(line => line.trim() !== '')
  const latest = lines[lines.length - 1] || ''

  // Clean the latest line for display
  let cleaned = latest.trim()
  // Remove markdown headers but keep the content
  cleaned = cleaned.replace(/^#{1,6}\s*/, '')
  // Convert **text** to readable text (remove markdown)
  cleaned = cleaned.replace(/\*\*(.*?)\*\*/g, '$1')

  return cleaned || 'Analyzing medical information...'
})

// Get formatted preview content
const formattedPreviewLines = computed(() => {
  if (!previewLines.value) return ''
  return cleanContent(previewLines.value)
})

// Watch for thinking state changes
watch(() => props.isThinking, (newVal) => {
  if (newVal) {
    // When thinking starts, show preview but keep collapsed
    showPreview.value = true
    isOpen.value = true // Open to show the preview
  } else if (props.autoCollapse) {
    // When thinking ends, auto-collapse after a delay
    setTimeout(() => {
      isOpen.value = false
      showPreview.value = false
    }, 3000) // 3 second delay before auto-collapse
  }
})

// Watch for content changes to update preview
watch(() => props.content, (newContent) => {
  console.log('ThinkingSection content updated:', newContent)
  if (props.isThinking) {
    showPreview.value = true
  }
})

// Auto-open when thinking starts
onMounted(() => {
  if (props.isThinking) {
    showPreview.value = true
    isOpen.value = true
  }
})
</script>

<template>
  <div class="my-4">
    <Collapsible v-model:open="isOpen" class="w-full">
      <CollapsibleTrigger class="flex w-full items-center justify-between rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 text-left hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 group shadow-sm">
        <div class="flex items-center space-x-3">
          <!-- Thinking icon with animation -->
          <div class="flex items-center justify-center w-7 h-7 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 group-hover:from-blue-600 group-hover:to-indigo-600 transition-all duration-200 shadow-sm">
            <div class="flex space-x-0.5">
              <div class="w-1 h-1 bg-white rounded-full animate-bounce" style="animation-delay: 0ms; animation-duration: 1.4s;"></div>
              <div class="w-1 h-1 bg-white rounded-full animate-bounce" style="animation-delay: 200ms; animation-duration: 1.4s;"></div>
              <div class="w-1 h-1 bg-white rounded-full animate-bounce" style="animation-delay: 400ms; animation-duration: 1.4s;"></div>
            </div>
          </div>
          <div class="flex flex-col">
            <span class="font-semibold text-gray-800 text-sm">{{ title }}</span>
            <span class="text-xs text-gray-600 opacity-75">
              {{ isThinking ? 'Thinking in progress...' : 'Clinical reasoning process' }}
            </span>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200 font-medium">
            {{ isOpen ? 'Hide reasoning' : 'Show reasoning' }}
          </span>
          <svg
            class="w-4 h-4 text-gray-600 transition-transform duration-300 ease-in-out"
            :class="{ 'rotate-180': isOpen }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </CollapsibleTrigger>
      
      <CollapsibleContent class="overflow-hidden">
        <div class="border-l-2 border-r-2 border-b-2 border-gray-200 rounded-b-lg bg-gradient-to-b from-gray-50 to-white">
          <div class="px-5 py-4">
            <!-- Remove duplicate header - already shown in trigger -->

            <!-- Content with better formatting -->
            <div class="prose prose-sm max-w-none text-gray-700 leading-relaxed">
              <!-- Show preview during thinking -->
              <div v-if="isThinking || showPreview" class="thinking-content space-y-3">
                <!-- Latest line with typing effect -->
                <div class="latest-line bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                  <div class="text-xs text-blue-600 font-medium mb-1">Currently analyzing:</div>
                  <div class="text-sm font-medium text-gray-800">
                    <span v-if="latestLine && latestLine !== 'Analyzing medical information...'" v-html="latestLine"></span>
                    <span v-else class="text-gray-500 italic">Processing medical information...</span>
                    <span v-if="isThinking" class="inline-block w-2 h-4 bg-blue-400 ml-1 animate-pulse"></span>
                  </div>
                </div>

                <!-- Preview of recent lines -->
                <div v-if="formattedPreviewLines && previewLines !== latestLine" class="preview-scroll">
                  <div class="text-xs text-gray-500 mb-2">Recent analysis:</div>
                  <div
                    class="preview-content bg-gray-50 p-3 rounded text-sm"
                    v-html="formattedPreviewLines"
                  ></div>
                </div>

                <!-- Thinking indicator -->
                <div v-if="isThinking" class="flex items-center space-x-2 text-xs text-gray-500">
                  <div class="flex space-x-1">
                    <div class="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
                    <div class="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 200ms"></div>
                    <div class="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 400ms"></div>
                  </div>
                  <span>Continuing analysis...</span>
                </div>
              </div>

              <!-- Show full content when expanded and not in preview mode -->
              <div v-else-if="isOpen" class="thinking-content space-y-3">
                <div v-html="cleanContent(body)"></div>
              </div>
            </div>

            <!-- Footer -->
            <div v-if="isOpen" class="mt-4 pt-3 border-t border-gray-200 animate-fade-in">
              <div class="flex items-center justify-between">
                <span class="text-xs text-gray-500 italic">This reasoning helps explain the medical decision-making process</span>
                <div class="flex items-center space-x-1">
                  <div
                    class="w-1.5 h-1.5 rounded-full"
                    :class="isThinking ? 'bg-blue-400 animate-pulse' : 'bg-green-400'"
                  ></div>
                  <span class="text-xs text-gray-500">
                    {{ isThinking ? 'Analyzing...' : 'Analysis complete' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  </div>
</template>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.4s ease-out;
}

/* Custom bounce animation for thinking dots */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out both;
}

/* Thinking content styling */
.thinking-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.thinking-content p {
  margin-bottom: 0.5rem;
}

.thinking-content div {
  margin-bottom: 0.25rem;
}

.thinking-content strong {
  color: #374151;
  font-weight: 600;
}

/* Hover effects */
.group:hover .thinking-content {
  color: #1f2937;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for content */
.thinking-content::-webkit-scrollbar {
  width: 4px;
}

.thinking-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.thinking-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.thinking-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Preview content styling */
.preview-content {
  max-height: 100px;
  overflow-y: auto;
  position: relative;
  transition: all 0.3s ease;
}

.preview-content::-webkit-scrollbar {
  width: 3px;
}

.preview-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.preview-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.preview-scroll {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Latest line styling */
.latest-line {
  animation: fadeInScale 0.4s ease-out;
  border-left-width: 4px;
  border-left-color: #3b82f6;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
