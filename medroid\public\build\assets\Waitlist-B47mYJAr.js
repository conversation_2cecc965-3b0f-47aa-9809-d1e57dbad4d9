import{M as le,r,o as oe,d as l,e as o,f as $,u as ne,m as re,g as ie,i as e,n as c,A as b,t as n,F as M,p as T,l as A,q as H,v as de,a as p}from"./vendor-BK2qhpQJ.js";import{_ as ue}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const ce={class:"py-6"},me={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ge={class:"mb-8"},ve={class:"flex justify-between items-center"},xe={class:"flex items-center space-x-4"},pe=["disabled"],fe={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},ye={class:"bg-white rounded-lg shadow p-6"},be={class:"flex items-center"},_e={class:"ml-5 w-0 flex-1"},he={class:"text-lg font-medium text-gray-900"},we={class:"bg-white rounded-lg shadow p-6"},ke={class:"flex items-center"},Ce={class:"ml-5 w-0 flex-1"},Me={class:"text-lg font-medium text-gray-900"},je={class:"bg-white rounded-lg shadow p-6"},Ie={class:"flex items-center"},Se={class:"ml-5 w-0 flex-1"},Be={class:"text-lg font-medium text-gray-900"},Re={class:"bg-white rounded-lg shadow p-6"},Fe={class:"flex items-center"},Ee={class:"ml-5 w-0 flex-1"},ze={class:"text-lg font-medium text-gray-900"},Le={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},De={class:"bg-white shadow rounded-lg"},Te={class:"px-4 py-5 sm:p-6"},Ae={key:0,class:"space-y-3"},Pe={class:"flex-1"},Ve={class:"text-sm font-medium text-gray-900"},We={class:"text-sm text-gray-500"},qe={class:"text-xs text-gray-400"},Ne={class:"flex items-center space-x-2"},Ue=["onClick"],$e={class:"text-center pt-2 space-y-2"},He={key:0},Ge={key:1},Je={key:1,class:"text-center py-6 text-gray-500"},Ke={class:"bg-white shadow rounded-lg"},Oe={class:"px-4 py-5 sm:p-6"},Qe={key:0,class:"space-y-3"},Xe={class:"flex-1"},Ye={class:"text-sm font-medium text-gray-900"},Ze={class:"text-sm text-gray-500"},et={class:"text-xs text-gray-400"},tt={key:1,class:"text-center py-6 text-gray-500"},st={key:2,class:"bg-white shadow rounded-lg mb-8"},at={class:"px-4 py-5 sm:p-6"},lt={class:"overflow-hidden"},ot={class:"min-w-full divide-y divide-gray-200"},nt={class:"bg-white divide-y divide-gray-200"},rt={class:"px-6 py-4 whitespace-nowrap"},it={class:"text-sm font-medium text-gray-900"},dt={class:"text-sm text-gray-500"},ut={class:"px-6 py-4 whitespace-nowrap"},ct={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},gt={key:3,class:"flex justify-center items-center py-12"},vt={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},xt={class:"bg-white rounded-lg p-6 w-full max-w-md mx-4"},pt={key:0,class:"mb-4"},ft={class:"font-medium"},yt={class:"text-sm text-gray-500"},bt={class:"mb-4"},_t={key:1,class:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded"},ht={key:2,class:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"},wt={class:"flex justify-end space-x-3"},kt=["disabled"],Ct={key:0},Mt={key:1},jt={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},It={class:"bg-white rounded-lg p-6 w-full max-w-2xl mx-4"},St={class:"mb-4"},Bt={class:"mb-4"},Rt={key:0,class:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded"},Ft={key:1,class:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"},Et={class:"flex justify-end space-x-3"},zt=["disabled"],Lt={key:0},Dt={key:1},Ut=le({__name:"Waitlist",setup(Tt){const u=r({}),G=r({}),f=r(!0),j=r(!1),J=r(30),v=r([]),I=r([]),w=r(!1),S=r(!1),B=r(!1),x=r(null),R=r({club_type:"regular"}),m=r({emails:"",club_type:"regular"}),k=r(!1),C=r(!1),g=r(!1),i=r(""),K=r(10),F=r(1),P=async()=>{try{f.value=!0;const a=await p.get("/waitlist-stats",{params:{date_range:J.value}});u.value=a.data}catch(a){console.error("Error fetching waitlist stats:",a)}finally{f.value=!1}},O=async()=>{try{const a=await p.get("/waitlist-analytics");G.value=a.data}catch(a){console.error("Error fetching analytics:",a)}},Q=async()=>{var a;try{j.value=!0;const t=await p.post("/waitlist-toggle",{enabled:!((a=u.value.stats)!=null&&a.waitlist_enabled)});t.data.success&&(await P(),alert(t.data.message))}catch(t){console.error("Error toggling waitlist mode:",t),alert("Failed to toggle waitlist mode")}finally{j.value=!1}},E=a=>new Date(a).toLocaleDateString(),X=a=>{switch(a){case"founder":return"bg-purple-100 text-purple-800";case"user_referral":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},_=async(a=!1)=>{try{const t=await p.get("/waitlist-requests",{params:{per_page:K.value,page:a?F.value:1}});a?v.value=[...v.value,...t.data.data||t.data]:(v.value=t.data.data||t.data,F.value=1)}catch(t){console.error("Error fetching waitlist requests:",t)}},z=async()=>{try{const a=await p.get("/waitlist-invitation-stats");I.value=a.data.recent_invitations||[]}catch(a){console.error("Error fetching invitation stats:",a)}},Y=a=>{x.value=a,S.value=!0,g.value=!1,i.value=""},L=()=>{S.value=!1,x.value=null,g.value=!1,i.value=""},Z=async()=>{var a,t;if(x.value)try{k.value=!0,i.value="";const d=await p.post(`/waitlist-requests/${x.value.id}/invite`,R.value);d.data.success?(g.value=!0,setTimeout(()=>{L()},2e3),await _(),await z()):i.value=d.data.message||"Failed to send invitation"}catch(d){console.error("Error sending invitation:",d),i.value=((t=(a=d.response)==null?void 0:a.data)==null?void 0:t.message)||"Failed to send invitation"}finally{k.value=!1}},ee=()=>{B.value=!0,m.value.emails="",m.value.club_type="regular",g.value=!1,i.value=""},D=()=>{B.value=!1,m.value.emails="",g.value=!1,i.value=""},te=async()=>{var a,t;if(!m.value.emails.trim()){i.value="Please enter at least one email address";return}try{C.value=!0,i.value="";const d=m.value.emails.split(",").map(y=>y.trim()).filter(y=>y.length>0);if(d.length===0){i.value="Please enter valid email addresses";return}const h=await p.post("/send-bulk-invitations",{emails:d,club_type:m.value.club_type});h.data.success?(g.value=!0,setTimeout(()=>{D()},3e3),await _(),await z()):i.value=h.data.message||"Failed to send bulk invitations"}catch(d){console.error("Error sending bulk invitations:",d),i.value=((t=(a=d.response)==null?void 0:a.data)==null?void 0:t.message)||"Failed to send bulk invitations"}finally{C.value=!1}},se=()=>{F.value++,_(!0)},ae=()=>w.value?v.value:v.value.slice(0,5);return oe(()=>{P(),O(),_(),z()}),(a,t)=>(o(),l(M,null,[$(ne(re),{title:"Waitlist Management"}),$(ue,null,{default:ie(()=>{var d,h,y,V,W,q,N,U;return[e("div",ce,[e("div",me,[e("div",ge,[e("div",ve,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-3xl font-bold text-gray-900"},"Waitlist Management"),e("p",{class:"mt-2 text-gray-600"},"Monitor and control invitation-only signup system")],-1)),e("div",xe,[t[5]||(t[5]=e("span",{class:"text-sm font-medium text-gray-700"},"Waitlist Mode:",-1)),e("button",{onClick:Q,disabled:j.value,class:b(["relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:ring-offset-2",(d=u.value.stats)!=null&&d.waitlist_enabled?"bg-medroid-orange":"bg-gray-200"])},[e("span",{class:b(["pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",(h=u.value.stats)!=null&&h.waitlist_enabled?"translate-x-5":"translate-x-0"])},null,2)],10,pe),e("span",{class:b(["text-sm font-medium",(y=u.value.stats)!=null&&y.waitlist_enabled?"text-medroid-orange":"text-gray-500"])},n((V=u.value.stats)!=null&&V.waitlist_enabled?"Enabled":"Disabled"),3)])])]),f.value?c("",!0):(o(),l("div",fe,[e("div",ye,[e("div",be,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])])],-1)),e("div",_e,[e("dl",null,[t[7]||(t[7]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Users",-1)),e("dd",he,n(((W=u.value.stats)==null?void 0:W.total_users)||0),1)])])])]),e("div",we,[e("div",ke,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])])],-1)),e("div",Ce,[e("dl",null,[t[9]||(t[9]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Recent Signups",-1)),e("dd",Me,n(((q=u.value.stats)==null?void 0:q.total_signups_period)||0),1)])])])]),e("div",je,[e("div",Ie,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])])],-1)),e("div",Se,[e("dl",null,[t[11]||(t[11]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Founder Signups",-1)),e("dd",Be,n(((N=u.value.stats)==null?void 0:N.founder_signups)||0),1)])])])]),e("div",Re,[e("div",Fe,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])],-1)),e("div",Ee,[e("dl",null,[t[13]||(t[13]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Referral Signups",-1)),e("dd",ze,n(((U=u.value.stats)==null?void 0:U.referral_signups)||0),1)])])])])])),f.value?c("",!0):(o(),l("div",Le,[e("div",De,[e("div",Te,[e("div",{class:"flex justify-between items-center mb-4"},[t[15]||(t[15]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"Waitlist Requests",-1)),e("div",{class:"flex space-x-2"},[e("button",{onClick:ee,class:"text-xs bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700 transition-colors"}," Bulk Invite "),e("button",{onClick:_,class:"text-sm text-medroid-orange hover:text-medroid-orange/80"}," Refresh ")])]),v.value.length>0?(o(),l("div",Ae,[(o(!0),l(M,null,T(ae(),s=>(o(),l("div",{key:s.id,class:"flex items-center justify-between p-3 border border-gray-200 rounded-lg"},[e("div",Pe,[e("div",Ve,n(s.name||"Anonymous"),1),e("div",We,n(s.email),1),e("div",qe,n(E(s.created_at)),1)]),e("div",Ne,[e("span",{class:b(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",s.status==="pending"?"bg-yellow-100 text-yellow-800":s.status==="invited"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"])},n(s.status),3),s.status==="pending"?(o(),l("button",{key:0,onClick:At=>Y(s),class:"text-xs bg-medroid-orange text-white px-2 py-1 rounded hover:bg-medroid-orange/90"}," Invite ",8,Ue)):c("",!0)])]))),128)),e("div",$e,[w.value?(o(),l("div",Ge,[e("button",{onClick:se,class:"text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded hover:bg-gray-200 mr-2"}," Load More "),e("button",{onClick:t[1]||(t[1]=s=>w.value=!1),class:"text-sm text-medroid-orange hover:text-medroid-orange/80"}," Show Less ")])):(o(),l("div",He,[e("button",{onClick:t[0]||(t[0]=s=>w.value=!0),class:"text-sm text-medroid-orange hover:text-medroid-orange/80"}," View All Requests ("+n(v.value.length)+") ",1)]))])])):(o(),l("div",Je," No waitlist requests yet "))])]),e("div",Ke,[e("div",Oe,[t[16]||(t[16]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 mb-4"},"Recent Invitations",-1)),I.value.length>0?(o(),l("div",Qe,[(o(!0),l(M,null,T(I.value.slice(0,5),s=>(o(),l("div",{key:s.id,class:"flex items-center justify-between p-3 border border-gray-200 rounded-lg"},[e("div",Xe,[e("div",Ye,n(s.email),1),e("div",Ze,n(s.club_type),1),e("div",et,n(E(s.created_at)),1)]),e("span",{class:b(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",s.status==="sent"?"bg-blue-100 text-blue-800":s.status==="used"?"bg-green-100 text-green-800":s.status==="expired"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"])},n(s.status),3)]))),128))])):(o(),l("div",tt," No invitations sent yet "))])])])),!f.value&&u.value.recent_signups?(o(),l("div",st,[e("div",at,[t[18]||(t[18]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 mb-4"},"Recent Signups",-1)),e("div",lt,[e("table",ot,[t[17]||(t[17]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"User"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Type"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Referrer/Code"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date")])],-1)),e("tbody",nt,[(o(!0),l(M,null,T(u.value.recent_signups,s=>(o(),l("tr",{key:s.id},[e("td",rt,[e("div",null,[e("div",it,n(s.name),1),e("div",dt,n(s.email),1)])]),e("td",ut,[e("span",{class:b(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",X(s.referral_type)])},n(s.referral_type==="founder"?"Founder":s.referral_type==="user_referral"?"Referral":"Direct"),3)]),e("td",ct,n(s.referrer_name||s.founder_code||"-"),1),e("td",mt,n(E(s.created_at)),1)]))),128))])])])])])):c("",!0),f.value?(o(),l("div",gt,t[19]||(t[19]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-medroid-orange"},null,-1)]))):c("",!0)])]),S.value?(o(),l("div",vt,[e("div",xt,[e("div",{class:"flex justify-between items-center mb-4"},[t[21]||(t[21]=e("h3",{class:"text-lg font-medium text-gray-900"},"Send Invitation",-1)),e("button",{onClick:L,class:"text-gray-400 hover:text-gray-600"},t[20]||(t[20]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),x.value?(o(),l("div",pt,[t[22]||(t[22]=e("p",{class:"text-sm text-gray-600"},"Sending invitation to:",-1)),e("p",ft,n(x.value.name||"Anonymous"),1),e("p",yt,n(x.value.email),1)])):c("",!0),e("div",bt,[t[24]||(t[24]=e("label",{for:"club_type",class:"block text-sm font-medium text-gray-700 mb-2"}," Club Type ",-1)),A(e("select",{id:"club_type","onUpdate:modelValue":t[2]||(t[2]=s=>R.value.club_type=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"},t[23]||(t[23]=[e("option",{value:"regular"},"Regular Club",-1),e("option",{value:"premium"},"Premium Club",-1),e("option",{value:"founder"},"Founder Club",-1)]),512),[[H,R.value.club_type]])]),g.value?(o(),l("div",_t," Invitation sent successfully! ")):c("",!0),i.value?(o(),l("div",ht,n(i.value),1)):c("",!0),e("div",wt,[e("button",{onClick:L,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"}," Cancel "),e("button",{onClick:Z,disabled:k.value,class:"px-4 py-2 text-sm font-medium text-white bg-medroid-orange border border-transparent rounded-md hover:bg-medroid-orange/90 disabled:opacity-50"},[k.value?(o(),l("span",Ct,"Sending...")):(o(),l("span",Mt,"Send Invitation"))],8,kt)])])])):c("",!0),B.value?(o(),l("div",jt,[e("div",It,[e("div",{class:"flex justify-between items-center mb-4"},[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900"},"Send Bulk Invitations",-1)),e("button",{onClick:D,class:"text-gray-400 hover:text-gray-600"},t[25]||(t[25]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",St,[t[27]||(t[27]=e("label",{for:"bulk_emails",class:"block text-sm font-medium text-gray-700 mb-2"}," Email Addresses (comma-separated) ",-1)),A(e("textarea",{id:"bulk_emails","onUpdate:modelValue":t[3]||(t[3]=s=>m.value.emails=s),rows:"6",placeholder:"<EMAIL>, <EMAIL>, <EMAIL>",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"},null,512),[[de,m.value.emails]]),t[28]||(t[28]=e("p",{class:"text-xs text-gray-500 mt-1"},"Enter multiple email addresses separated by commas",-1))]),e("div",Bt,[t[30]||(t[30]=e("label",{for:"bulk_club_type",class:"block text-sm font-medium text-gray-700 mb-2"}," Club Type ",-1)),A(e("select",{id:"bulk_club_type","onUpdate:modelValue":t[4]||(t[4]=s=>m.value.club_type=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange"},t[29]||(t[29]=[e("option",{value:"regular"},"Regular Club",-1),e("option",{value:"premium"},"Premium Club",-1),e("option",{value:"founder"},"Founder Club",-1)]),512),[[H,m.value.club_type]])]),g.value?(o(),l("div",Rt," Bulk invitations sent successfully! ")):c("",!0),i.value?(o(),l("div",Ft,n(i.value),1)):c("",!0),e("div",Et,[e("button",{onClick:D,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"}," Cancel "),e("button",{onClick:te,disabled:C.value,class:"px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 disabled:opacity-50"},[C.value?(o(),l("span",Lt,"Sending...")):(o(),l("span",Dt,"Send Bulk Invitations"))],8,zt)])])])):c("",!0)]}),_:1})],64))}});export{Ut as default};
