#!/bin/bash

# Script to start ngrok with a static domain for Medroid development
# Usage: ./start-ngrok-static.sh [domain-name]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Medroid Development Environment${NC}"

# Check if Laravel server is running
if ! pgrep -f "php artisan serve" > /dev/null; then
    echo -e "${YELLOW}⚠️  Laravel server not running. Starting it first...${NC}"
    php artisan serve --port=8000 &
    sleep 3
    echo -e "${GREEN}✅ Laravel server started on port 8000${NC}"
else
    echo -e "${GREEN}✅ Laravel server already running${NC}"
fi

# Get the static domain from parameter or use default
STATIC_DOMAIN=${1:-""}

if [ -z "$STATIC_DOMAIN" ]; then
    echo -e "${YELLOW}📝 No static domain provided. Using dynamic ngrok URL...${NC}"
    echo -e "${YELLOW}💡 To use a static domain, run: ./start-ngrok-static.sh your-static-domain.ngrok-free.app${NC}"
    
    # Start ngrok normally
    echo -e "${GREEN}🌐 Starting ngrok tunnel...${NC}"
    ngrok http 8000
else
    echo -e "${GREEN}🌐 Starting ngrok with static domain: $STATIC_DOMAIN${NC}"
    
    # Start ngrok with static domain
    ngrok http 8000 --domain=$STATIC_DOMAIN
fi
