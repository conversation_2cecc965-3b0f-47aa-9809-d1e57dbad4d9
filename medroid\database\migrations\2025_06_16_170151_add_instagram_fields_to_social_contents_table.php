<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            $table->string('username')->nullable()->after('source_id');
            $table->string('instagram_username')->nullable()->after('username');
            $table->string('permalink')->nullable()->after('instagram_username');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            $table->dropColumn(['username', 'instagram_username', 'permalink']);
        });
    }
};
