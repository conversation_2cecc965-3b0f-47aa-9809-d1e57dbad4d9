<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            $table->string('external_id')->nullable()->after('source_id');
            $table->string('thumbnail_url')->nullable()->after('media_url');
            $table->string('external_url')->nullable()->after('thumbnail_url');
            $table->timestamp('published_at')->nullable()->after('filtered_status');
            $table->json('metadata')->nullable()->after('engagement_metrics');

            $table->index(['source', 'external_id']);
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            $table->dropIndex(['source', 'external_id']);
            $table->dropIndex(['published_at']);

            $table->dropColumn([
                'external_id',
                'thumbnail_url',
                'external_url',
                'published_at',
                'metadata'
            ]);
        });
    }
};
