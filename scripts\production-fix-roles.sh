#!/bin/bash

# Production Role Fix Script
# This script safely fixes user role assignments in production

echo "=== Medroid Production Role Fix ==="
echo "Starting at: $(date)"

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "Error: artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

# Create backup directory with timestamp
BACKUP_DIR="backups/role-fix-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "1. Creating database backup..."
# Backup critical tables
php artisan db:table-backup users "$BACKUP_DIR/users_backup.sql" 2>/dev/null || echo "Warning: Could not backup users table"
php artisan db:table-backup model_has_roles "$BACKUP_DIR/model_has_roles_backup.sql" 2>/dev/null || echo "Warning: Could not backup model_has_roles table"
php artisan db:table-backup patients "$BACKUP_DIR/patients_backup.sql" 2>/dev/null || echo "Warning: Could not backup patients table"
php artisan db:table-backup providers "$BACKUP_DIR/providers_backup.sql" 2>/dev/null || echo "Warning: Could not backup providers table"

echo "2. Running dry-run to check what will be fixed..."
php artisan users:fix-roles --dry-run

echo ""
echo "3. Do you want to proceed with the actual fix? (y/N)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "4. Applying role fixes..."
    
    # Clear caches first
    echo "   - Clearing caches..."
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    
    # Run the fix command
    echo "   - Fixing user roles..."
    php artisan users:fix-roles
    
    # Clear permission cache
    echo "   - Clearing permission cache..."
    php artisan permission:cache-reset
    
    # Optimize for production
    echo "   - Optimizing for production..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    echo ""
    echo "✓ Role fix completed successfully!"
    echo "✓ Backup saved to: $BACKUP_DIR"
    echo "✓ Completed at: $(date)"
    
else
    echo "Operation cancelled by user."
    exit 0
fi

echo ""
echo "=== Post-Fix Verification ==="
echo "Running verification checks..."

# Verify role assignments
echo "Checking role assignments..."
php artisan tinker --execute="
\$usersWithoutRoles = App\Models\User::whereDoesntHave('roles')->count();
\$patientsWithoutProfiles = App\Models\User::where('role', 'patient')->whereDoesntHave('patient')->count();
\$providersWithoutProfiles = App\Models\User::where('role', 'provider')->whereDoesntHave('provider')->count();
echo 'Users without Spatie roles: ' . \$usersWithoutRoles . PHP_EOL;
echo 'Patients without profiles: ' . \$patientsWithoutProfiles . PHP_EOL;
echo 'Providers without profiles: ' . \$providersWithoutProfiles . PHP_EOL;
"

echo ""
echo "=== Fix Complete ==="
echo "If you see any issues, you can restore from backup in: $BACKUP_DIR"
