import{_ as v}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import{r as c,o as b,d as a,e as r,f as x,u as w,m as k,g as N,i as t,x as C,t as o,F as g,p as M,A as p,n as B}from"./vendor-BK2qhpQJ.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const S={class:"p-6"},V={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},j={class:"bg-white rounded-lg shadow p-6"},D={class:"flex items-center"},T={class:"ml-4"},L={class:"text-2xl font-bold text-gray-900"},z={class:"bg-white rounded-lg shadow p-6"},A={class:"flex items-center"},F={class:"ml-4"},R={class:"text-2xl font-bold text-gray-900"},E={class:"bg-white rounded-lg shadow p-6"},H={class:"flex items-center"},P={class:"ml-4"},U={class:"text-2xl font-bold text-gray-900"},$={class:"bg-white rounded-lg shadow p-6"},q={class:"flex items-center"},G={class:"ml-4"},I={class:"text-2xl font-bold text-gray-900"},J={class:"bg-white rounded-lg shadow"},K={key:0,class:"p-6"},O={key:1,class:"p-6 text-center text-gray-500"},Q={key:2,class:"overflow-x-auto"},W={class:"min-w-full divide-y divide-gray-200"},X={class:"bg-white divide-y divide-gray-200"},Y={class:"px-6 py-4 whitespace-nowrap"},Z={class:"text-sm font-medium text-gray-900"},tt={class:"text-sm text-gray-500"},et={class:"px-6 py-4 whitespace-nowrap"},st={class:"px-6 py-4 whitespace-nowrap"},nt={class:"flex items-center"},ot={class:"text-sm text-gray-500"},it={class:"px-6 py-4 whitespace-nowrap"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},lt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},dt={key:0,class:"text-green-600 hover:text-green-900 mr-3"},ft={__name:"Notifications",setup(ct){const m=[{title:"Dashboard",href:"/dashboard"},{title:"Notifications",href:"/notifications"}],d=c(!1),i=c([]),l=c({total_notifications:0,sent_notifications:0,pending_notifications:0,failed_notifications:0}),f=async()=>{d.value=!0;try{const s=await window.axios.get("/notifications-list");i.value=s.data||[],l.value={total_notifications:i.value.length,sent_notifications:i.value.filter(e=>e.status==="sent").length,pending_notifications:i.value.filter(e=>e.status==="pending").length,failed_notifications:i.value.filter(e=>e.status==="failed").length}}catch(s){console.error("Error fetching notifications:",s),i.value=[],l.value={total_notifications:0,sent_notifications:0,pending_notifications:0,failed_notifications:0}}finally{d.value=!1}};b(()=>{f()});const u=s=>s?new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",y=s=>{switch(s){case"sent":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"failed":return"bg-red-100 text-red-800";case"draft":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}},h=s=>{switch(s){case"appointment":return"bg-blue-100 text-blue-800";case"system":return"bg-purple-100 text-purple-800";case"marketing":return"bg-pink-100 text-pink-800";case"reminder":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},_=s=>{switch(s){case"patient":return"bg-green-100 text-green-800";case"provider":return"bg-blue-100 text-blue-800";case"admin":return"bg-purple-100 text-purple-800";case"all":return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}};return(s,e)=>(r(),a(g,null,[x(w(k),{title:"Notifications Management"}),x(v,{breadcrumbs:m},{default:N(()=>[t("div",S,[e[13]||(e[13]=t("div",{class:"mb-6 flex justify-between items-center"},[t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"Notifications Management"),t("p",{class:"text-gray-600"},"Manage and track system notifications")]),t("button",{class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"},[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})]),C(" Send Notification ")])],-1)),t("div",V,[t("div",j,[t("div",D,[e[1]||(e[1]=t("div",{class:"p-2 bg-blue-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})])],-1)),t("div",T,[e[0]||(e[0]=t("p",{class:"text-sm font-medium text-gray-600"},"Total Notifications",-1)),t("p",L,o(l.value.total_notifications),1)])])]),t("div",z,[t("div",A,[e[3]||(e[3]=t("div",{class:"p-2 bg-green-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",F,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-600"},"Sent",-1)),t("p",R,o(l.value.sent_notifications),1)])])]),t("div",E,[t("div",H,[e[5]||(e[5]=t("div",{class:"p-2 bg-yellow-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",P,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-600"},"Pending",-1)),t("p",U,o(l.value.pending_notifications),1)])])]),t("div",$,[t("div",q,[e[7]||(e[7]=t("div",{class:"p-2 bg-red-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",G,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-600"},"Failed",-1)),t("p",I,o(l.value.failed_notifications),1)])])])]),t("div",J,[e[12]||(e[12]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h2",{class:"text-lg font-medium text-gray-900"},"Recent Notifications")],-1)),d.value?(r(),a("div",K,e[8]||(e[8]=[t("div",{class:"animate-pulse"},[t("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),t("div",{class:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),t("div",{class:"h-4 bg-gray-200 rounded w-5/6"})],-1)]))):i.value.length===0?(r(),a("div",O," No notifications found. ")):(r(),a("div",Q,[t("table",W,[e[11]||(e[11]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Title"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Type"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Recipients"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Created"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Sent"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),t("tbody",X,[(r(!0),a(g,null,M(i.value,n=>(r(),a("tr",{key:n.id},[t("td",Y,[t("div",Z,o(n.title),1),t("div",tt,o(n.message.substring(0,50))+"...",1)]),t("td",et,[t("span",{class:p([h(n.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(n.type),3)]),t("td",st,[t("div",nt,[t("span",{class:p([_(n.recipient_type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2"])},o(n.recipient_type),3),t("span",ot,"("+o(n.recipient_count)+")",1)])]),t("td",it,[t("span",{class:p([y(n.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(n.status),3)]),t("td",at,o(u(n.created_at)),1),t("td",rt,o(u(n.sent_at)),1),t("td",lt,[e[9]||(e[9]=t("button",{class:"text-blue-600 hover:text-blue-900 mr-3"},"View",-1)),n.status==="pending"?(r(),a("button",dt,"Send")):B("",!0),e[10]||(e[10]=t("button",{class:"text-red-600 hover:text-red-900"},"Delete",-1))])]))),128))])])]))])])]),_:1})],64))}};export{ft as default};
