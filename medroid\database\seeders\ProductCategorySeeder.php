<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductCategory;
use Illuminate\Support\Str;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // Healthcare Products
            [
                'name' => 'Medical Equipment',
                'description' => 'Medical devices and equipment for healthcare professionals',
                'sort_order' => 1,
                'subcategories' => [
                    'Diagnostic Equipment',
                    'Surgical Instruments',
                    'Monitoring Devices',
                    'Therapeutic Equipment'
                ]
            ],
            [
                'name' => 'Pharmaceuticals',
                'description' => 'Medications and pharmaceutical products',
                'sort_order' => 2,
                'subcategories' => [
                    'Prescription Medications',
                    'Over-the-Counter',
                    'Supplements',
                    'Vitamins'
                ]
            ],
            [
                'name' => 'Personal Care',
                'description' => 'Personal health and wellness products',
                'sort_order' => 3,
                'subcategories' => [
                    'Skincare',
                    'Oral Care',
                    'Hair Care',
                    'Body Care'
                ]
            ],
            
            // Digital Products
            [
                'name' => 'Digital Health',
                'description' => 'Digital health products and services',
                'sort_order' => 4,
                'subcategories' => [
                    'Health Apps',
                    'Telemedicine Services',
                    'Digital Consultations',
                    'Health Monitoring Software'
                ]
            ],
            [
                'name' => 'Educational Content',
                'description' => 'Educational materials and courses',
                'sort_order' => 5,
                'subcategories' => [
                    'Medical Courses',
                    'Health Guides',
                    'Training Materials',
                    'Certification Programs'
                ]
            ],
            
            // Wellness Products
            [
                'name' => 'Fitness & Wellness',
                'description' => 'Fitness and wellness products',
                'sort_order' => 6,
                'subcategories' => [
                    'Exercise Equipment',
                    'Nutrition Products',
                    'Wellness Accessories',
                    'Recovery Tools'
                ]
            ],
            [
                'name' => 'Mental Health',
                'description' => 'Mental health and wellness products',
                'sort_order' => 7,
                'subcategories' => [
                    'Therapy Tools',
                    'Meditation Products',
                    'Stress Relief',
                    'Mental Wellness Apps'
                ]
            ],
            
            // General Categories
            [
                'name' => 'Books & Publications',
                'description' => 'Medical and health-related books and publications',
                'sort_order' => 8,
                'subcategories' => [
                    'Medical Textbooks',
                    'Health Magazines',
                    'Research Papers',
                    'Reference Materials'
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            // Create parent category
            $parentCategory = ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'description' => $categoryData['description'],
                'parent_id' => null,
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);

            // Create subcategories if they exist
            if (isset($categoryData['subcategories'])) {
                foreach ($categoryData['subcategories'] as $index => $subcategoryName) {
                    ProductCategory::create([
                        'name' => $subcategoryName,
                        'slug' => Str::slug($subcategoryName),
                        'description' => "Subcategory of {$categoryData['name']}",
                        'parent_id' => $parentCategory->id,
                        'sort_order' => $index + 1,
                        'is_active' => true,
                    ]);
                }
            }
        }

        $this->command->info('Product categories seeded successfully!');
    }
}
