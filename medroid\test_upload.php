<?php

// Simple test script to debug file upload
echo "PHP Upload Configuration:\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";

echo "\nRequest Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
echo "Content Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'Not set') . "\n";
echo "Content Length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'Not set') . "\n";

echo "\n_POST data:\n";
var_dump($_POST);

echo "\n_FILES data:\n";
var_dump($_FILES);

echo "\nAll input:\n";
var_dump(file_get_contents('php://input'));
