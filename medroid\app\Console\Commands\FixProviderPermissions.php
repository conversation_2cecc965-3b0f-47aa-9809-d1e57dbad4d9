<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class FixProviderPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:provider-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix provider permissions for product management';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing provider permissions...');

        // Ensure provider role exists
        $providerRole = Role::firstOrCreate(['name' => 'provider', 'guard_name' => 'web']);
        $this->info("✅ Provider role exists: {$providerRole->name}");

        // Define required permissions for providers
        $requiredPermissions = [
            'view products',
            'create products', 
            'edit products',
            'delete products'
        ];

        $assignedCount = 0;
        foreach ($requiredPermissions as $permissionName) {
            $permission = Permission::firstOrCreate(['name' => $permissionName, 'guard_name' => 'web']);
            
            if (!$providerRole->hasPermissionTo($permission)) {
                $providerRole->givePermissionTo($permission);
                $assignedCount++;
                $this->info("✅ Assigned permission: {$permissionName}");
            } else {
                $this->info("ℹ️  Already has permission: {$permissionName}");
            }
        }

        // Check provider users
        $providerUsers = User::where('role', 'provider')->get();
        $this->info("\n📊 Provider users found: {$providerUsers->count()}");

        $usersFixed = 0;
        foreach ($providerUsers as $user) {
            if (!$user->hasRole('provider')) {
                $user->assignRole('provider');
                $usersFixed++;
                $this->info("✅ Assigned provider role to user: {$user->email}");
            }
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        $this->info("🧹 Cleared permission cache");

        $this->info("\n🎉 Summary:");
        $this->info("- Permissions assigned: {$assignedCount}");
        $this->info("- Users fixed: {$usersFixed}");
        $this->info("- Total provider users: {$providerUsers->count()}");

        // Verify a sample provider user
        if ($providerUsers->count() > 0) {
            $sampleUser = $providerUsers->first();
            $this->info("\n🔍 Verification for user: {$sampleUser->email}");
            $this->info("- Has provider role: " . ($sampleUser->hasRole('provider') ? 'Yes' : 'No'));
            $this->info("- Can create products: " . ($sampleUser->can('create products') ? 'Yes' : 'No'));
            $this->info("- Total permissions: " . $sampleUser->getAllPermissions()->count());
        }

        $this->info("\n✅ Provider permissions fix completed!");
        
        return 0;
    }
}
