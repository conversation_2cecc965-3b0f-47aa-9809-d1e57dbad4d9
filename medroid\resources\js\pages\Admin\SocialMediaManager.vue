<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Head } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import axios from 'axios'

// Breadcrumbs
const breadcrumbs = [
    { name: 'Dashboard', href: '/dashboard' },
    { name: 'Social Media Manager', href: '/admin/social-media' }
]

// State
const loading = ref(false)
const activeTab = ref('dashboard')
const stats = reactive({
    total_posts: 0,
    pending_posts: 0,
    approved_posts: 0,
    rejected_posts: 0,
    total_stories: 0,
    active_stories: 0,
    total_users: 0,
    posts_today: 0,
    stories_today: 0
})

const posts = reactive({
    data: [],
    current_page: 1,
    last_page: 1,
    total: 0,
    loading: false
})

const stories = reactive({
    data: [],
    current_page: 1,
    last_page: 1,
    total: 0,
    loading: false
})

const filters = reactive({
    posts: {
        status: 'all',
        source: 'all',
        search: '',
        date_from: '',
        date_to: '',
        sort_by: 'created_at',
        sort_order: 'desc'
    },
    stories: {
        status: 'all',
        search: '',
        date_from: '',
        date_to: '',
        sort_by: 'created_at',
        sort_order: 'desc'
    }
})

// Computed
const pendingPostsPercentage = computed(() => {
    if (stats.total_posts === 0) return 0
    return Math.round((stats.pending_posts / stats.total_posts) * 100)
})

const approvedPostsPercentage = computed(() => {
    if (stats.total_posts === 0) return 0
    return Math.round((stats.approved_posts / stats.total_posts) * 100)
})

// Methods
const loadDashboard = async () => {
    loading.value = true
    try {
        const response = await axios.get('/admin/api/social-media')
        Object.assign(stats, response.data.stats)
    } catch (error) {
        console.error('Error loading dashboard:', error)
    } finally {
        loading.value = false
    }
}

const loadPosts = async (page = 1) => {
    posts.loading = true
    try {
        const params = {
            page,
            per_page: 20,
            ...filters.posts
        }

        const response = await axios.get('/admin/api/social-media/posts', { params })
        Object.assign(posts, response.data)
    } catch (error) {
        console.error('Error loading posts:', error)
    } finally {
        posts.loading = false
    }
}

const loadStories = async (page = 1) => {
    stories.loading = true
    try {
        const params = {
            page,
            per_page: 20,
            ...filters.stories
        }

        const response = await axios.get('/admin/api/social-media/stories', { params })
        Object.assign(stories, response.data)
    } catch (error) {
        console.error('Error loading stories:', error)
    } finally {
        stories.loading = false
    }
}

const updatePostStatus = async (postId, status, reason = '') => {
    try {
        await axios.patch(`/admin/api/social-media/posts/${postId}/status`, {
            status,
            reason
        })

        // Reload posts and dashboard
        await Promise.all([loadPosts(posts.current_page), loadDashboard()])

        alert('Post status updated successfully')
    } catch (error) {
        console.error('Error updating post status:', error)
        alert('Failed to update post status')
    }
}

const deletePost = async (postId) => {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        try {
            await axios.delete(`/admin/api/social-media/posts/${postId}`)

            // Reload posts and dashboard
            await Promise.all([loadPosts(posts.current_page), loadDashboard()])

            alert('Post deleted successfully')
        } catch (error) {
            console.error('Error deleting post:', error)
            alert('Failed to delete post')
        }
    }
}

const deleteStory = async (storyId) => {
    if (confirm('Are you sure you want to delete this story? This action cannot be undone.')) {
        try {
            await axios.delete(`/admin/api/social-media/stories/${storyId}`)

            // Reload stories and dashboard
            await Promise.all([loadStories(stories.current_page), loadDashboard()])

            alert('Story deleted successfully')
        } catch (error) {
            console.error('Error deleting story:', error)
            alert('Failed to delete story')
        }
    }
}

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const getStatusColor = (status) => {
    switch (status) {
        case 'approved': return 'text-green-600 bg-green-100'
        case 'rejected': return 'text-red-600 bg-red-100'
        case 'pending': return 'text-yellow-600 bg-yellow-100'
        default: return 'text-gray-600 bg-gray-100'
    }
}

// Watch for filter changes
const applyPostFilters = () => {
    loadPosts(1)
}

const applyStoryFilters = () => {
    loadStories(1)
}

// Initialize
onMounted(() => {
    loadDashboard()
    loadPosts()
    loadStories()
})
</script>

<template>
    <Head title="Social Media Manager - Admin" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="min-h-screen bg-gray-50">
            <!-- Header -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="py-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Social Media Manager</h1>
                                <p class="text-gray-600">Manage posts, stories, and user-generated content</p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="text-sm text-gray-500">
                                    Last updated: {{ new Date().toLocaleTimeString() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav class="flex space-x-8">
                        <button
                            @click="activeTab = 'dashboard'"
                            :class="[
                                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                activeTab === 'dashboard'
                                    ? 'border-teal-500 text-teal-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            Dashboard
                        </button>
                        <button
                            @click="activeTab = 'posts'; loadPosts()"
                            :class="[
                                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                activeTab === 'posts'
                                    ? 'border-teal-500 text-teal-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            Posts Management
                        </button>
                        <button
                            @click="activeTab = 'stories'; loadStories()"
                            :class="[
                                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                activeTab === 'stories'
                                    ? 'border-teal-500 text-teal-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            Stories Management
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Content -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <!-- Dashboard Tab -->
                <div v-if="activeTab === 'dashboard'" class="space-y-6">
                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Posts</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ stats.total_posts }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Pending Posts</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ stats.pending_posts }}</p>
                                    <p class="text-xs text-gray-500">{{ pendingPostsPercentage }}% of total</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Approved Posts</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ stats.approved_posts }}</p>
                                    <p class="text-xs text-gray-500">{{ approvedPostsPercentage }}% of total</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Active Stories</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ stats.active_stories }}</p>
                                    <p class="text-xs text-gray-500">{{ stats.total_stories }} total</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Today's Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Today's Activity</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Posts Created</span>
                                    <span class="text-sm font-medium text-gray-900">{{ stats.posts_today }}</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Stories Created</span>
                                    <span class="text-sm font-medium text-gray-900">{{ stats.stories_today }}</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Total Users</span>
                                    <span class="text-sm font-medium text-gray-900">{{ stats.total_users }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-3">
                                <button
                                    @click="activeTab = 'posts'; loadPosts()"
                                    class="w-full text-left px-4 py-3 bg-teal-50 text-teal-700 rounded-lg hover:bg-teal-100 transition-colors"
                                >
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                        </svg>
                                        Manage Posts
                                    </div>
                                </button>
                                <button
                                    @click="activeTab = 'stories'; loadStories()"
                                    class="w-full text-left px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                                >
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2" />
                                        </svg>
                                        Manage Stories
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom styles for the admin interface */
</style>