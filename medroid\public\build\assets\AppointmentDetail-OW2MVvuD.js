import{_ as E}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import{z as P,c as k,r as L,d as l,e as r,f as g,u as x,m as j,g as c,i as t,t as s,A as b,n as m,F as v,p as R,y as $,P as h,x as u,W as w}from"./vendor-BK2qhpQJ.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const q={class:"flex items-center justify-between"},V={class:"flex mt-2","aria-label":"Breadcrumb"},z={class:"inline-flex items-center space-x-1 md:space-x-3"},U={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},W={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},X={class:"flex space-x-3"},H=["disabled"],M={key:0,class:"fas fa-spinner fa-spin mr-2"},O={key:1,class:"fas fa-times mr-2"},I={class:"py-12"},K={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},G={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},J={class:"p-6"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Y={class:"space-y-4"},Z={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},_={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},tt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},et={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},at={class:"space-y-4"},nt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},st={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},ot={key:0},rt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},lt={key:1},it={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},gt={__name:"AppointmentDetail",props:{appointment:{type:Object,required:!0}},setup(a){const y=a,A=P(),C=k(()=>{var n;return(n=A.props.auth)==null?void 0:n.user}),p=k(()=>{var o;const n=(o=C.value)==null?void 0:o.role;let e="/dashboard";return n==="provider"?e="/provider/appointments":n==="patient"?e="/patient/appointments":(n==="admin"||n==="manager"||n==="super_admin")&&(e="/manage/appointments"),[{title:"Dashboard",href:"/dashboard"},{title:"Appointments",href:e},{title:"Appointment Details",href:"#"}]}),d=L(!1),D=n=>({scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",no_show:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"})[n]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",S=n=>({telemedicine:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300","in-person":"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"})[n]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",B=()=>{w.visit(`/appointments/${y.appointment.id}/edit`)},T=async()=>{var n;if(confirm("Are you sure you want to cancel this appointment?")){d.value=!0;try{const e=(n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"),o=await fetch(`/delete-appointment/${y.appointment.id}`,{method:"DELETE",headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e||""},credentials:"same-origin"});if(o.ok)alert("Appointment cancelled successfully."),w.visit("/appointments");else{const i=await o.json().catch(()=>({}));alert(i.message||"Failed to cancel appointment. Please try again.")}}catch(e){console.error("Error cancelling appointment:",e),alert("Failed to cancel appointment. Please try again.")}finally{d.value=!1}}},F=n=>new Date(n).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),N=n=>new Date(`2000-01-01 ${n}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return(n,e)=>(r(),l(v,null,[g(x(j),{title:"Appointment Details"}),g(E,null,{header:c(()=>[t("div",q,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Appointment Details ",-1)),t("nav",V,[t("ol",z,[(r(!0),l(v,null,R(p.value,(o,i)=>(r(),l("li",{key:i,class:"inline-flex items-center"},[i<p.value.length-1?(r(),$(x(h),{key:0,href:o.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[u(s(o.title),1)]),_:2},1032,["href"])):(r(),l("span",U,s(o.title),1)),i<p.value.length-1?(r(),l("svg",W,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):m("",!0)]))),128))])])]),t("div",X,[g(x(h),{href:p.value[1].href,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"},{default:c(()=>e[2]||(e[2]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),u(" Back to Appointments ")])),_:1},8,["href"]),a.appointment.status==="scheduled"?(r(),l("button",{key:0,onClick:B,class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"},e[3]||(e[3]=[t("i",{class:"fas fa-edit mr-2"},null,-1),u(" Edit Appointment ")]))):m("",!0),a.appointment.status==="scheduled"?(r(),l("button",{key:1,onClick:T,disabled:d.value,class:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"},[d.value?(r(),l("i",M)):(r(),l("i",O)),u(" "+s(d.value?"Cancelling...":"Cancel Appointment"),1)],8,H)):m("",!0)])])]),default:c(()=>{var o,i,f;return[t("div",I,[t("div",K,[t("div",G,[t("div",J,[t("div",Q,[t("div",null,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Appointment Information ",-1)),t("div",Y,[t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Service",-1)),t("p",Z,s(((o=a.appointment.service)==null?void 0:o.name)||a.appointment.reason||"Consultation"),1)]),t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Type",-1)),t("span",{class:b([S(a.appointment.is_telemedicine?"telemedicine":"in-person"),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"])},s(a.appointment.is_telemedicine?"telemedicine":"in-person"),3)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Status",-1)),t("span",{class:b([D(a.appointment.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1"])},s(a.appointment.status),3)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Date",-1)),t("p",_,s(F(a.appointment.date||a.appointment.scheduled_at)),1)]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Time",-1)),t("p",tt,s(N(a.appointment.time||a.appointment.scheduled_at)),1)]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Amount",-1)),t("p",et,"$"+s(typeof a.appointment.amount=="number"?a.appointment.amount.toFixed(2):parseFloat(a.appointment.amount||0).toFixed(2)),1)])])]),t("div",null,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Participants ",-1)),t("div",at,[t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Patient",-1)),t("p",nt,s(a.appointment.patient_name||((i=a.appointment.patient)==null?void 0:i.name)||"N/A"),1)]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Provider",-1)),t("p",st,s(a.appointment.provider_name||((f=a.appointment.provider)==null?void 0:f.name)||"N/A"),1)]),a.appointment.notes?(r(),l("div",ot,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Notes",-1)),t("p",rt,s(a.appointment.notes),1)])):m("",!0),a.appointment.cancellation_reason?(r(),l("div",lt,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Cancellation Reason",-1)),t("p",it,s(a.appointment.cancellation_reason),1)])):m("",!0)])])])])])])])]}),_:1})],64))}};export{gt as default};
