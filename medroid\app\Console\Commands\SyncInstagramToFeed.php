<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SocialMediaPost;
use App\Models\InstagramAccount;
use App\Services\InstagramService;

class SyncInstagramToFeed extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'instagram:sync-to-feed {--force : Force sync all posts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Instagram posts to the discover feed';

    protected $instagramService;

    public function __construct(InstagramService $instagramService)
    {
        parent::__construct();
        $this->instagramService = $instagramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting Instagram to feed sync...');

        $force = $this->option('force');

        // Get all Instagram posts
        $query = SocialMediaPost::with(['instagramAccount'])
            ->where('platform', 'instagram')
            ->where('is_visible', true);

        if (!$force) {
            // Only sync posts from the last 30 days if not forcing
            $query->where('posted_at', '>=', now()->subDays(30));
        }

        $posts = $query->get();

        $this->info("Found {$posts->count()} Instagram posts to sync");

        $synced = 0;
        $errors = 0;

        foreach ($posts as $post) {
            try {
                if ($post->instagramAccount) {
                    // Use reflection to call the protected method
                    $reflection = new \ReflectionClass($this->instagramService);
                    $method = $reflection->getMethod('createSocialContentFromPost');
                    $method->setAccessible(true);
                    $method->invoke($this->instagramService, $post, $post->instagramAccount);

                    $synced++;
                    $this->line("✓ Synced post: {$post->platform_post_id}");
                } else {
                    $this->warn("⚠ No Instagram account found for post: {$post->platform_post_id}");
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Error syncing post {$post->platform_post_id}: " . $e->getMessage());
            }
        }

        $this->info("Sync completed!");
        $this->info("✓ Synced: {$synced}");
        if ($errors > 0) {
            $this->warn("⚠ Errors: {$errors}");
        }

        return 0;
    }
}
