const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Create-Dzx0-gGM.js","assets/vendor-BK2qhpQJ.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/Primitive-BOKwGa1V.js","assets/createLucideIcon-Cyf5BO7D.js","assets/AppLayout-B4Zax8Ug.css","assets/Edit-C7krhgSu.js","assets/Index-BTGW0_h0.js","assets/Show-DHWqMNgH.js","assets/Index-Bxvuti_H.js","assets/Show-C4jxFQYS.js","assets/Create-dQVkFyEr.js","assets/Edit-CqWwSTXs.js","assets/Index--mMHYtKr.js","assets/BulkImportModal-BnpCeDiK.js","assets/AppointmentDetail-OW2MVvuD.js","assets/AppointmentEdit-rzsbQUJR.js","assets/AppointmentPayment-DdomM7tz.js","assets/Appointments-CWV864sv.js","assets/Appointments-Dc5S3XBs.css","assets/Chat-D37tC1Fe.js","assets/ChatInput-uvF_bxz7.js","assets/ChatInput-BmGnubPc.css","assets/Chat-DtNjTBwp.css","assets/ChatHistory-kPX3W32L.js","assets/Chats-DYMQIbX5.js","assets/Clinics-D3wBVmef.js","assets/Clubs-BWryC75s.js","assets/CreditHistory-DBXKMQJj.js","assets/Credits-CQioasMq.js","assets/Dashboard-DZuh31w_.js","assets/Dashboard_backup-BL3fEzgf.js","assets/Dashboard_backup-mxSR4WGf.css","assets/Discover-DvGrUEyd.js","assets/EmailTemplates-D7qXJB20.js","assets/Notifications-f_vKZ1ZA.js","assets/Patients-7NDNgMwZ.js","assets/Payments-BVQevfgP.js","assets/Permissions-DhhjU5ZT.js","assets/Availability-C3J0Boz4.js","assets/Earnings-DJ6CYYCO.js","assets/Patients-e0S8GAMp.js","assets/Products-Bi6xPFI7.js","assets/Schedule-BX6dd3zS.js","assets/Services-BK1B5oSH.js","assets/ProviderRegister-CW-fIU-i.js","assets/InputError.vue_vue_type_script_setup_true_lang-CMeAZ6jx.js","assets/Providers-abBISBPz.js","assets/Referrals-DCorWM8z.js","assets/Services-B6z2FQU5.js","assets/Shop-C6EQpLHp.js","assets/Cart-yZW6EgEt.js","assets/Checkout-Dbv3nSgq.js","assets/OrderDetail-Bbcqt085.js","assets/Orders-BqaPOQOm.js","assets/ProductDetail-DWgSUCAL.js","assets/SystemVerification-BCxYF1SF.js","assets/Users-DwwlXsPi.js","assets/Waitlist-B47mYJAr.js","assets/Welcome-ClRJYW54.js","assets/Welcome-DrPWS9zi.css","assets/ConfirmPassword-CMmTjt1I.js","assets/index-BsQMq1AH.js","assets/Label.vue_vue_type_script_setup_true_lang-Cu3Z5Mpv.js","assets/index-vK5yNL7A.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-BTw2AY9u.js","assets/ForgotPassword-Dx8mT5xe.js","assets/TextLink.vue_vue_type_script_setup_true_lang-CA1BYzEg.js","assets/FounderSignup-zY3w8LVd.js","assets/Register-ztlszAPw.js","assets/Register-B2WbpIMy.css","assets/ResetPassword-DJQG8KgM.js","assets/VerifyEmail-DVe6d7HN.js","assets/Appearance-DROm8Msx.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-CnAxxXgy.js","assets/Layout.vue_vue_type_script_setup_true_lang-BvmLVDru.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-D6pS4Y6a.js","assets/useBodyScrollLock-Czdv3moC.js","assets/Password-_quSaf-P.js","assets/Profile-XAnI6VxQ.js"])))=>i.map(i=>d[i]);
import{r as T,o as I,c as E,w as D,a as v,L as S,W as y,b as w,k as V,h as C}from"./vendor-BK2qhpQJ.js";const k="modulepreload",x=function(e){return"/build/"+e},h={},t=function(r,o,u){let d=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),_=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));d=Promise.allSettled(o.map(n=>{if(n=x(n),n in h)return;h[n]=!0;const p=n.endsWith(".css"),i=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");if(a.rel=p?"stylesheet":k,p||(a.as="script"),a.crossOrigin="",a.href=n,_&&a.setAttribute("nonce",_),document.head.appendChild(a),p)return new Promise((f,O)=>{a.addEventListener("load",f),a.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${n}`)))})}))}function c(s){const _=new Event("vite:preloadError",{cancelable:!0});if(_.payload=s,window.dispatchEvent(_),!_.defaultPrevented)throw s}return d.then(s=>{for(const _ of s||[])_.status==="rejected"&&c(_.reason);return r().catch(c)})};async function F(e,r){for(const o of Array.isArray(e)?e:[e]){const u=r[o];if(!(typeof u>"u"))return typeof u=="function"?u():u}throw new Error(`Page not found: ${e}`)}function P(e){if(!(typeof window>"u"))if(e==="system"){const o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",o==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,r,o=365)=>{if(typeof document>"u")return;const u=o*24*60*60;document.cookie=`${e}=${r};path=/;max-age=${u};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),L=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=L();P(e||"system")};function j(){var r;if(typeof window>"u")return;const e=L();P(e||"system"),(r=z())==null||r.addEventListener("change",$)}function Q(){const e=T("system");I(()=>{const o=localStorage.getItem("appearance");o&&(e.value=o)});function r(o){e.value=o,localStorage.setItem("appearance",o),b("appearance",o),P(o)}return{appearance:e,updateAppearance:r}}const m={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},l=T("normal"),N=()=>{const e=localStorage.getItem("medroid-font-size");e&&m[e]&&(l.value=e)},q=e=>{localStorage.setItem("medroid-font-size",e)};N();function W(){const e=E(()=>{var i;return((i=m[l.value])==null?void 0:i.scale)||1}),r=E(()=>{var i;return((i=m[l.value])==null?void 0:i.name)||"Normal"}),o=E(()=>{var i;return((i=m[l.value])==null?void 0:i.description)||""}),u=E(()=>Object.entries(m).map(([i,a])=>({key:i,...a}))),d=E(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),c=i=>{m[i]&&(l.value=i,q(i),p())},s=()=>{const i=Object.keys(m),a=i.indexOf(l.value);a<i.length-1&&c(i[a+1])},_=()=>{const i=Object.keys(m),a=i.indexOf(l.value);a>0&&c(i[a-1])},n=()=>{c("normal")},p=()=>{const i=document.documentElement;Object.entries(d.value).forEach(([a,f])=>{i.style.setProperty(a,f)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${l.value}`)};return D(l,()=>{p()},{immediate:!0}),{currentFontSize:l,fontSizeScale:e,fontSizeName:r,fontSizeDescription:o,availableFontSizes:u,fontSizeStyles:d,setFontSize:c,increaseFontSize:s,decreaseFontSize:_,resetFontSize:n,applyFontSizeToDocument:p,FONT_SIZES:m}}const X="Medroid";v.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";v.defaults.withCredentials=!0;const g=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},A=async()=>{try{if((await fetch("/sanctum/csrf-cookie",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}})).ok)return await new Promise(r=>setTimeout(r,100)),g()}catch(e){console.error("Failed to refresh CSRF token:",e)}return null},R=g();R&&(v.defaults.headers.common["X-CSRF-TOKEN"]=R);const U=async()=>{let e=g();return e||(console.log("Initializing CSRF token..."),e=await A(),e?console.log("CSRF token initialized successfully"):console.warn("Failed to initialize CSRF token")),e};v.interceptors.request.use(async e=>{let r=g();return r||(console.warn("No CSRF token found, attempting to refresh..."),r=await A()),r?e.headers["X-CSRF-TOKEN"]=r:console.error("Unable to obtain CSRF token"),e.headers.Accept=e.headers.Accept||"application/json",e.headers["Content-Type"]=e.headers["Content-Type"]||"application/json",e},e=>Promise.reject(e));v.interceptors.response.use(e=>e,async e=>{var o,u,d,c,s,_;const r=e.config;if(((o=e.response)==null?void 0:o.status)===419&&!r._retry){console.warn("CSRF token mismatch detected, attempting to refresh and retry..."),r._retry=!0;try{const n=await A();if(n)return r.headers["X-CSRF-TOKEN"]=n,v.defaults.headers.common["X-CSRF-TOKEN"]=n,await new Promise(p=>setTimeout(p,50)),v(r);console.error("Failed to refresh CSRF token, reloading page..."),setTimeout(()=>{window.confirm("Session expired. Reload page to continue?")&&window.location.reload()},1e3)}catch(n){console.error("Error during token refresh:",n)}}if(((u=e.response)==null?void 0:u.status)===500&&!r._serverRetry&&!((d=r.url)!=null&&d.includes("/logout"))){console.warn("Server error detected, attempting retry..."),r._serverRetry=!0,await new Promise(n=>setTimeout(n,1e3));try{return v(r)}catch(n){console.error("Server error retry failed:",n)}}return(c=r.url)!=null&&c.includes("/logout")?(console.log("Logout request - not intercepting"),Promise.reject(e)):(((s=e.response)==null?void 0:s.status)===401&&((_=r.url)!=null&&_.includes("/logout")||(console.warn("Authentication failed, redirecting to login..."),window.location.href="/login")),Promise.reject(e))});window.axios=v;S({title:e=>`${e} - ${X}`,resolve:e=>F(`./pages/${e}.vue`,Object.assign({"./pages/Admin/Categories/Create.vue":()=>t(()=>import("./Create-Dzx0-gGM.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./pages/Admin/Categories/Edit.vue":()=>t(()=>import("./Edit-C7krhgSu.js"),__vite__mapDeps([7,1,2,3,4,5,6])),"./pages/Admin/Categories/Index.vue":()=>t(()=>import("./Index-BTGW0_h0.js"),__vite__mapDeps([8,1,2,3,4,5,6])),"./pages/Admin/Categories/Show.vue":()=>t(()=>import("./Show-DHWqMNgH.js"),__vite__mapDeps([9,2,1,3,4,5,6])),"./pages/Admin/Orders/Index.vue":()=>t(()=>import("./Index-Bxvuti_H.js"),__vite__mapDeps([10,1,2,3,4,5,6])),"./pages/Admin/Orders/Show.vue":()=>t(()=>import("./Show-C4jxFQYS.js"),__vite__mapDeps([11,2,1,3,4,5,6])),"./pages/Admin/Products/Create.vue":()=>t(()=>import("./Create-dQVkFyEr.js"),__vite__mapDeps([12,1,2,3,4,5,6])),"./pages/Admin/Products/Edit.vue":()=>t(()=>import("./Edit-CqWwSTXs.js"),__vite__mapDeps([13,1,2,3,4,5,6])),"./pages/Admin/Products/Index.vue":()=>t(()=>import("./Index--mMHYtKr.js"),__vite__mapDeps([14,1,2,3,4,5,6,15])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-OW2MVvuD.js"),__vite__mapDeps([16,2,1,3,4,5,6])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-rzsbQUJR.js"),__vite__mapDeps([17,1,2,3,4,5,6])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-DdomM7tz.js"),__vite__mapDeps([18,1,2,3,4,5,6])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-CWV864sv.js"),__vite__mapDeps([19,2,1,3,4,5,6,20])),"./pages/Chat.vue":()=>t(()=>import("./Chat-D37tC1Fe.js"),__vite__mapDeps([21,1,2,3,4,5,6,22,23,24])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-kPX3W32L.js"),__vite__mapDeps([25,2,1,3,4,5,6])),"./pages/Chats.vue":()=>t(()=>import("./Chats-DYMQIbX5.js"),__vite__mapDeps([26,1,2,3,4,5,6])),"./pages/Clinics.vue":()=>t(()=>import("./Clinics-D3wBVmef.js"),__vite__mapDeps([27,1,2,3,4,5,6])),"./pages/Clubs.vue":()=>t(()=>import("./Clubs-BWryC75s.js"),__vite__mapDeps([28,1,2,3,4,5,6])),"./pages/CreditHistory.vue":()=>t(()=>import("./CreditHistory-DBXKMQJj.js"),__vite__mapDeps([29,1,2,3,4,5,6])),"./pages/Credits.vue":()=>t(()=>import("./Credits-CQioasMq.js"),__vite__mapDeps([30,1,2,3,4,5,6])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-DZuh31w_.js"),__vite__mapDeps([31,1,2,3,4,5,6])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-BL3fEzgf.js"),__vite__mapDeps([32,1,2,3,4,5,6,33])),"./pages/Discover.vue":()=>t(()=>import("./Discover-DvGrUEyd.js"),__vite__mapDeps([34,2,1,3,4,5,6])),"./pages/EmailTemplates.vue":()=>t(()=>import("./EmailTemplates-D7qXJB20.js"),__vite__mapDeps([35,1,2,3,4,5,6])),"./pages/Notifications.vue":()=>t(()=>import("./Notifications-f_vKZ1ZA.js"),__vite__mapDeps([36,2,1,3,4,5,6])),"./pages/Patients.vue":()=>t(()=>import("./Patients-7NDNgMwZ.js"),__vite__mapDeps([37,1,2,3,4,5,6])),"./pages/Payments.vue":()=>t(()=>import("./Payments-BVQevfgP.js"),__vite__mapDeps([38,1,2,3,4,5,6])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-DhhjU5ZT.js"),__vite__mapDeps([39,2,1,3,4,5,6])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-C3J0Boz4.js"),__vite__mapDeps([40,1,2,3,4,5,6])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-DJ6CYYCO.js"),__vite__mapDeps([41,2,1,3,4,5,6])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-e0S8GAMp.js"),__vite__mapDeps([42,1,2,3,4,5,6])),"./pages/Provider/Products.vue":()=>t(()=>import("./Products-Bi6xPFI7.js"),__vite__mapDeps([43,1,2,3,4,5,6,15])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-BX6dd3zS.js"),__vite__mapDeps([44,1,2,3,4,5,6])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-BK1B5oSH.js"),__vite__mapDeps([45,2,1,3,4,5,6])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-CW-fIU-i.js"),__vite__mapDeps([46,1,47])),"./pages/Providers.vue":()=>t(()=>import("./Providers-abBISBPz.js"),__vite__mapDeps([48,1,2,3,4,5,6])),"./pages/Referrals.vue":()=>t(()=>import("./Referrals-DCorWM8z.js"),__vite__mapDeps([49,2,1,3,4,5,6])),"./pages/Services.vue":()=>t(()=>import("./Services-B6z2FQU5.js"),__vite__mapDeps([50,1,2,3,4,5,6])),"./pages/Shop.vue":()=>t(()=>import("./Shop-C6EQpLHp.js"),__vite__mapDeps([51,1,2,3,4,5,6])),"./pages/Shop/Cart.vue":()=>t(()=>import("./Cart-yZW6EgEt.js"),__vite__mapDeps([52,2,1,3,4,5,6])),"./pages/Shop/Checkout.vue":()=>t(()=>import("./Checkout-Dbv3nSgq.js"),__vite__mapDeps([53,1,2,3,4,5,6])),"./pages/Shop/OrderDetail.vue":()=>t(()=>import("./OrderDetail-Bbcqt085.js"),__vite__mapDeps([54,2,1,3,4,5,6])),"./pages/Shop/Orders.vue":()=>t(()=>import("./Orders-BqaPOQOm.js"),__vite__mapDeps([55,1,2,3,4,5,6])),"./pages/Shop/ProductDetail.vue":()=>t(()=>import("./ProductDetail-DWgSUCAL.js"),__vite__mapDeps([56,2,1,3,4,5,6])),"./pages/SystemVerification.vue":()=>t(()=>import("./SystemVerification-BCxYF1SF.js"),__vite__mapDeps([57,1,2,3,4,5,6])),"./pages/Users.vue":()=>t(()=>import("./Users-DwwlXsPi.js"),__vite__mapDeps([58,1,2,3,4,5,6])),"./pages/Waitlist.vue":()=>t(()=>import("./Waitlist-B47mYJAr.js"),__vite__mapDeps([59,1,2,3,4,5,6])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-ClRJYW54.js"),__vite__mapDeps([60,1,22,3,23,61])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-CMmTjt1I.js"),__vite__mapDeps([62,1,47,63,4,64,65,66,5])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-Dx8mT5xe.js"),__vite__mapDeps([67,1,47,68,63,4,64,65,66,5])),"./pages/auth/FounderSignup.vue":()=>t(()=>import("./FounderSignup-zY3w8LVd.js"),__vite__mapDeps([69,1,47])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-ztlszAPw.js"),__vite__mapDeps([70,1,47,3,71])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-DJQG8KgM.js"),__vite__mapDeps([72,1,47,63,4,64,65,66,5])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-DVe6d7HN.js"),__vite__mapDeps([73,1,68,63,4,66,5])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-DROm8Msx.js"),__vite__mapDeps([74,1,3,5,75,2,4,6,76,63,65,77])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-D6pS4Y6a.js"),__vite__mapDeps([78,1,76,63,4,65,64,79])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-_quSaf-P.js"),__vite__mapDeps([80,1,47,2,3,4,5,6,76,63,65,75,64])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-XAnI6VxQ.js"),__vite__mapDeps([81,1,75,47,63,4,79,65,64,5,2,3,6,76]))})),setup({el:e,App:r,props:o,plugin:u}){w({render:()=>C(r,o)}).use(u).use(V).mount(e)},progress:{color:"#4B5563"}}).then(()=>{y.on("error",e=>{var r,o,u,d,c,s;if(console.log("Inertia request error:",e),(o=(r=e.response)==null?void 0:r.url)!=null&&o.includes("/logout")||(c=(d=(u=e.response)==null?void 0:u.config)==null?void 0:d.url)!=null&&c.includes("/logout")){console.log("Logout error handled gracefully, redirecting to home"),window.location.href="/";return}if(((s=e.response)==null?void 0:s.status)===419){console.warn("CSRF error on Inertia request, reloading page"),window.location.reload();return}})}).catch(e=>{console.error("Error initializing Inertia app:",e)});j();const{applyFontSizeToDocument:K}=W();K();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(const r of e)(r.scope.includes("datadog")||r.scope.includes("sw.js"))&&r.unregister()}).catch(function(e){});U().catch(e=>{console.warn("Failed to initialize CSRF token on startup:",e)});export{Q as a,W as u};
