<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\InstagramAccount;
use App\Services\InstagramService;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🚀 Testing Instagram Integration...\n\n";

try {
    // Test Instagram Service
    $instagramService = new InstagramService();
    
    // Test access token
    $accessToken = 'IGAANhaJ8WMGdBZAE1LaUxEcG9HbG9nbGhoX2NfTGZAGMGw4bV9IVURaU1h3VnJOaWlmMzhPX0JDeWdPTU1KcEVFbm9BTkNuQ1ZAHYmk5T2RZATkllMjJwcW05cFJHQU4zbWFGS3ZATenFmOFlOeENqdXZAwVmUxbndldk05ZAmxTMThxVQZDZD';
    $userId = '*****************';
    
    echo "✅ Testing getUserProfile...\n";
    $profile = $instagramService->getUserProfile($accessToken, $userId);
    
    if ($profile) {
        echo "✅ Profile fetched successfully!\n";
        echo "   Username: " . ($profile['username'] ?? 'N/A') . "\n";
        echo "   Account Type: " . ($profile['account_type'] ?? 'N/A') . "\n";
        echo "   Media Count: " . ($profile['media_count'] ?? 'N/A') . "\n\n";
    } else {
        echo "❌ Failed to fetch profile\n\n";
    }
    
    echo "✅ Testing getUserMedia...\n";
    $reflection = new ReflectionClass($instagramService);
    $method = $reflection->getMethod('getUserMedia');
    $method->setAccessible(true);
    
    $media = $method->invoke($instagramService, $accessToken, $userId, 3);
    
    if ($media && isset($media['data'])) {
        echo "✅ Media fetched successfully!\n";
        echo "   Posts retrieved: " . count($media['data']) . "\n";
        
        foreach ($media['data'] as $index => $post) {
            echo "   Post " . ($index + 1) . ": " . ($post['media_type'] ?? 'Unknown') . " - " . substr($post['caption'] ?? 'No caption', 0, 50) . "...\n";
        }
        echo "\n";
    } else {
        echo "❌ Failed to fetch media\n\n";
    }
    
    // Test database operations
    echo "✅ Testing database operations...\n";
    
    // Create or get test user
    $user = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Instagram Test User',
            'password' => bcrypt('password123'),
            'email_verified_at' => now()
        ]
    );
    
    echo "   Test user created/found: ID " . $user->id . "\n";
    
    // Create Instagram account record
    $instagramAccount = InstagramAccount::updateOrCreate(
        [
            'user_id' => $user->id,
            'instagram_user_id' => $userId
        ],
        [
            'username' => $profile['username'] ?? 'spark_soul_shine',
            'access_token' => $accessToken,
            'account_type' => $profile['account_type'] ?? 'MEDIA_CREATOR',
            'media_count' => $profile['media_count'] ?? 32,
            'expires_at' => now()->addDays(60),
            'is_active' => true,
        ]
    );
    
    echo "   Instagram account created/updated: ID " . $instagramAccount->id . "\n";
    echo "   Username: " . $instagramAccount->username . "\n";
    echo "   Account Type: " . $instagramAccount->account_type . "\n\n";
    
    echo "🎉 All tests passed! Instagram integration is working perfectly!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
