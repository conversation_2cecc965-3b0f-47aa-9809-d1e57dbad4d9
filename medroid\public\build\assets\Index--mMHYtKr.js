import{z as q,c as p,r as n,o as K,d as l,e as r,f as x,u as v,m as Q,g as f,i as t,n as b,x as y,P,l as C,v as G,F as S,p as D,t as o,q as V,A as I,y as H}from"./vendor-BK2qhpQJ.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import{_ as O}from"./BulkImportModal-BnpCeDiK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const W={class:"py-6"},X={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},Y={class:"mb-6 bg-white overflow-hidden shadow-sm sm:rounded-lg"},Z={class:"p-6"},ee={class:"flex items-center justify-between"},te={key:0,class:"flex space-x-3"},se={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ae={class:"p-6"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},le=["value"],oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},de={class:"p-6 text-gray-900 dark:text-gray-100"},ne={key:0,class:"text-center py-8"},ie={key:1,class:"text-center py-8"},ue={key:2,class:"overflow-x-auto"},ce={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},pe={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ge={class:"px-6 py-4 whitespace-nowrap"},xe={class:"flex items-center"},ye={class:"flex-shrink-0 h-10 w-10"},me=["src","alt"],ve={key:1,class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},fe={class:"ml-4"},be={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},he={class:"text-sm text-gray-500 dark:text-gray-400"},ke={class:"px-6 py-4 whitespace-nowrap"},_e={class:"text-sm text-gray-900 dark:text-gray-100"},we={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"text-sm text-gray-900 dark:text-gray-100"},Se={key:0,class:"text-xs text-gray-500 line-through ml-1"},Be={class:"px-6 py-4 whitespace-nowrap"},$e={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ae={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Le={__name:"Index",setup(De){const M=q(),i=p(()=>{var a;return(a=M.props.auth)==null?void 0:a.user}),N=[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"}],h=n(!1),k=n([]),B=n([]),d=n(""),u=n("all"),c=n("all"),_=n(!1),w=async()=>{var a;h.value=!0;try{const e=new URLSearchParams;d.value&&e.append("search",d.value),u.value!=="all"&&e.append("category",u.value),c.value!=="all"&&e.append("type",c.value);const s=await window.axios.get(`/admin/products-list?${e.toString()}`);k.value=((a=s.data.products)==null?void 0:a.data)||s.data.products||[],s.data.categories&&(B.value=s.data.categories)}catch(e){console.error("Error fetching products:",e),k.value=[]}finally{h.value=!1}},T=a=>a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",U=a=>a==="digital"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",$=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),A=p(()=>k.value.filter(a=>{const e=!d.value||a.name.toLowerCase().includes(d.value.toLowerCase())||a.sku.toLowerCase().includes(d.value.toLowerCase()),s=u.value==="all"||a.category_id==u.value,m=c.value==="all"||a.type===c.value;return e&&s&&m})),g=p(()=>{var a,e;return((e=(a=i.value)==null?void 0:a.roles)==null?void 0:e.some(s=>s.name==="admin"))||!1}),L=p(()=>{var a,e;return g.value||((e=(a=i.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("create products"))||!1}),E=p(()=>{var a,e;return g.value||((e=(a=i.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("edit products"))||!1}),j=p(()=>{var a,e;return g.value||((e=(a=i.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("delete products"))||!1}),F=a=>{var e;return g.value?!0:E.value&&a.user_id===((e=i.value)==null?void 0:e.id)},z=a=>{var e;return g.value?!0:j.value&&a.user_id===((e=i.value)==null?void 0:e.id)},R=a=>{alert(`Successfully imported ${a.imported_count} products!`),w()};return K(()=>{w()}),(a,e)=>(r(),l(S,null,[x(v(Q),{title:"Product Management"}),x(J,{breadcrumbs:N},{default:f(()=>[t("div",W,[t("div",X,[t("div",Y,[t("div",Z,[t("div",ee,[e[7]||(e[7]=t("div",null,[t("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Product Management "),t("p",{class:"text-gray-600 text-sm mt-1"},"Manage your products and inventory")],-1)),L.value?(r(),l("div",te,[t("button",{onClick:e[0]||(e[0]=s=>_.value=!0),class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"},e[5]||(e[5]=[t("i",{class:"fas fa-upload mr-2"},null,-1),y(" Bulk Import ")])),x(v(P),{href:"/admin/products/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:f(()=>e[6]||(e[6]=[t("i",{class:"fas fa-plus mr-2"},null,-1),y(" Add Product ")])),_:1})])):b("",!0)])])]),t("div",se,[t("div",ae,[t("div",re,[t("div",null,[C(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>d.value=s),type:"text",placeholder:"Search products...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[G,d.value]])]),t("div",null,[C(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>u.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[8]||(e[8]=t("option",{value:"all"},"All Categories",-1)),(r(!0),l(S,null,D(B.value,s=>(r(),l("option",{key:s.id,value:s.id},o(s.name),9,le))),128))],512),[[V,u.value]])]),t("div",null,[C(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>c.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[9]||(e[9]=[t("option",{value:"all"},"All Types",-1),t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[V,c.value]])]),t("div",null,[t("button",{onClick:w,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),t("div",oe,[t("div",de,[h.value?(r(),l("div",ne,e[10]||(e[10]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):A.value.length===0?(r(),l("div",ie,e[11]||(e[11]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"No products found.",-1)]))):(r(),l("div",ue,[t("table",ce,[e[15]||(e[15]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",pe,[(r(!0),l(S,null,D(A.value,s=>{var m;return r(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ge,[t("div",xe,[t("div",ye,[s.primary_image?(r(),l("img",{key:0,src:`/storage/${s.primary_image}`,alt:s.name,class:"h-10 w-10 rounded object-cover"},null,8,me)):(r(),l("div",ve,e[12]||(e[12]=[t("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"},null,-1)])))]),t("div",fe,[t("div",be,o(s.name),1),t("div",he," SKU: "+o(s.sku),1)])])]),t("td",ke,[t("span",_e,o(((m=s.category)==null?void 0:m.name)||"N/A"),1)]),t("td",we,[t("span",{class:I([U(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(s.type),3)]),t("td",Pe,[t("div",Ce,[y(o($(s.price))+" ",1),s.sale_price?(r(),l("span",Se,o($(s.sale_price)),1)):b("",!0)])]),t("td",Be,[t("span",{class:I([T(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(s.is_active?"Active":"Inactive"),3)]),t("td",$e,[x(v(P),{href:`/admin/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:f(()=>e[13]||(e[13]=[y(" View ")])),_:2},1032,["href"]),F(s)?(r(),H(v(P),{key:0,href:`/admin/products/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:f(()=>e[14]||(e[14]=[y(" Edit ")])),_:2},1032,["href"])):b("",!0),z(s)?(r(),l("button",Ae," Delete ")):b("",!0)])])}),128))])])]))])])])]),x(O,{"is-open":_.value,onClose:e[4]||(e[4]=s=>_.value=!1),onImported:R},null,8,["is-open"])]),_:1})],64))}};export{Le as default};
