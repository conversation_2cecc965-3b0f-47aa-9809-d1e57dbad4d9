import{M as c,U as f,y as n,e as i,g as a,f as o,d as u,n as m,i as p,u as e,m as _,j as y,x as d}from"./vendor-BK2qhpQJ.js";import{_ as k}from"./TextLink.vue_vue_type_script_setup_true_lang-CA1BYzEg.js";import{_ as v}from"./index-BsQMq1AH.js";import{L as b,_ as g}from"./AuthLayout.vue_vue_type_script_setup_true_lang-BTw2AY9u.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const x={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},L=c({__name:"VerifyEmail",props:{status:{}},setup(h){const s=f({}),l=()=>{s.post(route("verification.send"))};return(r,t)=>(i(),n(g,{title:"Verify email",description:"Please verify your email address by clicking on the link we just emailed to you."},{default:a(()=>[o(e(_),{title:"Email verification"}),r.status==="verification-link-sent"?(i(),u("div",x," A new verification link has been sent to the email address you provided during registration. ")):m("",!0),p("form",{onSubmit:y(l,["prevent"]),class:"space-y-6 text-center"},[o(e(v),{disabled:e(s).processing,variant:"secondary"},{default:a(()=>[e(s).processing?(i(),n(e(b),{key:0,class:"h-4 w-4 animate-spin"})):m("",!0),t[0]||(t[0]=d(" Resend verification email "))]),_:1},8,["disabled"]),o(k,{href:r.route("logout"),method:"post",as:"button",class:"mx-auto block text-sm"},{default:a(()=>t[1]||(t[1]=[d(" Log out ")])),_:1},8,["href"])],32)]),_:1}))}});export{L as default};
