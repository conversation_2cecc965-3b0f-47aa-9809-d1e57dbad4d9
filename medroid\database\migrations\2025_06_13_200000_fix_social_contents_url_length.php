<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            // Change media_url from string(255) to text to handle long Instagram URLs
            $table->text('media_url')->nullable()->change();

            // Change external_url to text as well
            $table->text('external_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            $table->string('media_url')->nullable()->change();
            $table->string('external_url')->nullable()->change();
        });
    }
};
