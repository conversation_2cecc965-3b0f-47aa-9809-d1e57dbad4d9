<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\InstagramService;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 Debugging Instagram OAuth URL Generation...\n\n";

try {
    // Create Instagram service
    $instagramService = new InstagramService();
    
    // Generate auth URL with test state
    $state = base64_encode(json_encode([
        'user_id' => 1,
        'timestamp' => time(),
        'csrf_token' => 'test_token'
    ]));
    
    echo "📋 Configuration Check:\n";
    echo "   App ID: " . config('services.instagram.app_id') . "\n";
    echo "   Redirect URI: " . config('services.instagram.redirect_uri') . "\n";
    echo "   State: " . $state . "\n\n";
    
    // Generate the URL
    $authUrl = $instagramService->getAuthorizationUrl($state);
    
    echo "🔗 Generated Auth URL:\n";
    echo $authUrl . "\n\n";
    
    // Parse the URL to check components
    $parsedUrl = parse_url($authUrl);
    parse_str($parsedUrl['query'] ?? '', $queryParams);
    
    echo "📊 URL Components:\n";
    echo "   Scheme: " . ($parsedUrl['scheme'] ?? 'N/A') . "\n";
    echo "   Host: " . ($parsedUrl['host'] ?? 'N/A') . "\n";
    echo "   Path: " . ($parsedUrl['path'] ?? 'N/A') . "\n\n";
    
    echo "🔧 Query Parameters:\n";
    foreach ($queryParams as $key => $value) {
        echo "   {$key}: " . (strlen($value) > 100 ? substr($value, 0, 100) . "..." : $value) . "\n";
    }
    
    // Validate required parameters
    $requiredParams = ['client_id', 'redirect_uri', 'scope', 'response_type'];
    $missingParams = [];
    
    foreach ($requiredParams as $param) {
        if (!isset($queryParams[$param]) || empty($queryParams[$param])) {
            $missingParams[] = $param;
        }
    }
    
    if (empty($missingParams)) {
        echo "\n✅ All required parameters are present!\n";
        
        // Test the URL by making a HEAD request
        echo "\n🌐 Testing URL accessibility...\n";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $authUrl);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Medroid/1.0)');
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "❌ cURL Error: " . $error . "\n";
        } else {
            echo "📡 HTTP Response Code: " . $httpCode . "\n";
            if ($httpCode == 200 || $httpCode == 302) {
                echo "✅ URL is accessible!\n";
            } else {
                echo "⚠️ Unexpected response code\n";
            }
        }
        
    } else {
        echo "\n❌ Missing required parameters: " . implode(', ', $missingParams) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
