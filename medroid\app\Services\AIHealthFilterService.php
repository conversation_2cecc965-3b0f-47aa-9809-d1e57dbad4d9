<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AIHealthFilterService
{
    /**
     * Batch analyze multiple posts for health relevance
     * This is more economical than individual API calls
     */
    public function batchAnalyzePosts(array $posts)
    {
        $results = [];
        $uncachedPosts = [];
        
        // First, check cache for existing results
        foreach ($posts as $index => $post) {
            $cacheKey = 'health_analysis_' . md5($post['caption'] ?? '');
            $cachedResult = Cache::get($cacheKey);
            
            if ($cachedResult !== null) {
                $results[$index] = $cachedResult;
            } else {
                $uncachedPosts[$index] = $post;
            }
        }
        
        // If all posts are cached, return results
        if (empty($uncachedPosts)) {
            return $results;
        }
        
        // Batch analyze uncached posts
        $batchResults = $this->performBatchAIAnalysis($uncachedPosts);
        
        // Merge results and cache new ones
        foreach ($batchResults as $index => $result) {
            $results[$index] = $result;
            
            // Cache the result
            $cacheKey = 'health_analysis_' . md5($uncachedPosts[$index]['caption'] ?? '');
            Cache::put($cacheKey, $result, 60 * 24 * 7); // 7 days
        }
        
        return $results;
    }
    
    /**
     * Perform batch AI analysis using OpenAI (small chunks for economy)
     */
    private function performBatchAIAnalysis(array $posts)
    {
        // Process in smaller chunks of 3-5 posts to minimize token usage
        $chunkSize = min(3, count($posts));
        $allResults = [];

        $chunks = array_chunk($posts, $chunkSize, true);

        foreach ($chunks as $chunk) {
            try {
                $chunkResults = $this->analyzeChunk($chunk);
                $allResults = array_merge($allResults, $chunkResults);

                // Small delay between chunks to respect rate limits
                usleep(500000); // 0.5 second delay

            } catch (\Exception $e) {
                Log::warning('AI chunk analysis failed', [
                    'error' => $e->getMessage(),
                    'chunk_size' => count($chunk)
                ]);

                // Fallback for this chunk
                foreach ($chunk as $index => $post) {
                    $allResults[$index] = $this->keywordHealthCheck($post['caption'] ?? '');
                }
            }
        }

        return $allResults;
    }

    /**
     * Analyze a small chunk of posts
     */
    private function analyzeChunk(array $chunk)
    {
        $batchPrompt = $this->prepareBatchPrompt($chunk);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('OPENAI_API_KEY'),
            'Content-Type' => 'application/json',
        ])->timeout(15)->post('https://api.openai.com/v1/chat/completions', [
            'model' => 'gpt-3.5-turbo',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a health content classifier. Respond with only "YES" or "NO" for each post to indicate if it\'s related to health, wellness, mental health, fitness, nutrition, meditation, self-care, or personal wellbeing. One answer per line.'
                ],
                [
                    'role' => 'user',
                    'content' => $batchPrompt
                ]
            ],
            'max_tokens' => count($chunk) * 3, // Reduced tokens per post
            'temperature' => 0,
        ]);

        if ($response->successful()) {
            $result = $response->json();
            $aiResponse = trim($result['choices'][0]['message']['content'] ?? '');

            Log::info('AI chunk analysis completed', [
                'chunk_size' => count($chunk),
                'tokens_used' => $result['usage']['total_tokens'] ?? 0,
                'cost_estimate' => ($result['usage']['total_tokens'] ?? 0) * 0.000002 // Rough cost estimate
            ]);

            return $this->parseBatchResponse($aiResponse, $chunk);
        }

        throw new \Exception('AI API request failed');
    }
    
    /**
     * Prepare batch prompt for multiple posts
     */
    private function prepareBatchPrompt(array $posts)
    {
        $prompt = "Analyze these social media posts and determine if each is related to health, wellness, mental health, fitness, nutrition, meditation, self-care, or personal wellbeing:\n\n";
        
        foreach ($posts as $index => $post) {
            $caption = substr($post['caption'] ?? '', 0, 150); // Limit to 150 chars per post
            $prompt .= "Post " . ($index + 1) . ": " . $caption . "\n";
        }
        
        $prompt .= "\nRespond with YES or NO for each post, one per line:";
        
        return $prompt;
    }
    
    /**
     * Parse batch AI response
     */
    private function parseBatchResponse(string $response, array $posts)
    {
        $lines = array_filter(array_map('trim', explode("\n", $response)));
        $results = [];
        
        foreach ($posts as $index => $post) {
            $lineIndex = array_search($index, array_keys($posts));
            $aiAnswer = isset($lines[$lineIndex]) ? strtoupper(trim($lines[$lineIndex])) : 'NO';
            
            // Clean up response (remove numbers, dots, etc.)
            $aiAnswer = preg_replace('/^[\d\.\s]*/', '', $aiAnswer);
            $aiAnswer = trim($aiAnswer, '.');
            
            $results[$index] = $aiAnswer === 'YES';
        }
        
        Log::info('Batch AI health analysis completed', [
            'post_count' => count($posts),
            'health_posts' => array_sum($results)
        ]);
        
        return $results;
    }
    
    /**
     * Fallback analysis using enhanced keyword matching
     */
    private function fallbackBatchAnalysis(array $posts)
    {
        $results = [];
        
        foreach ($posts as $index => $post) {
            $results[$index] = $this->keywordHealthCheck($post['caption'] ?? '');
        }
        
        return $results;
    }
    
    /**
     * Enhanced keyword-based health check
     */
    private function keywordHealthCheck(string $caption)
    {
        $caption = strtolower($caption);
        
        // Strong health indicators
        $healthIndicators = [
            // Hashtags
            '#health', '#wellness', '#fitness', '#meditation', '#yoga',
            '#mindfulness', '#selfcare', '#mentalhealth', '#nutrition',
            '#workout', '#exercise', '#healing', '#therapy', '#peaceful',
            '#calm', '#zen', '#balance', '#motivation', '#inspiration',
            
            // Keywords
            'mental health', 'physical therapy', 'self care', 'work life balance',
            'inner peace', 'good vibes', 'positive vibes', 'healthy lifestyle',
            'personal growth', 'mind body', 'healing journey', 'stress relief',
            'meditation', 'mindfulness', 'peaceful', 'tranquil', 'serenity',
            'gratitude', 'breathe', 'spiritual', 'holistic', 'natural healing',
            
            // Wellness emojis
            '🧘', '🕉️', '☮️', '🌱', '🌿', '✨', '🙏', '💚', '💙', '💜'
        ];
        
        foreach ($healthIndicators as $indicator) {
            if (strpos($caption, $indicator) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get AI analysis statistics
     */
    public function getAnalysisStats()
    {
        $cachePattern = 'health_analysis_*';
        $totalAnalyzed = 0;
        $healthRelated = 0;
        
        // This is a simplified version - in production you might want to track this differently
        return [
            'total_analyzed' => $totalAnalyzed,
            'health_related' => $healthRelated,
            'cache_hit_rate' => $totalAnalyzed > 0 ? (($totalAnalyzed - $healthRelated) / $totalAnalyzed) * 100 : 0
        ];
    }
}
