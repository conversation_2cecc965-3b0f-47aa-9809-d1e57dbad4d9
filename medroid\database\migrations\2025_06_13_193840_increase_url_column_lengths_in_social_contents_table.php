<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            // Change URL columns to LONGTEXT to handle very long Instagram URLs
            $table->longText('media_url')->nullable()->change();
            $table->longText('thumbnail_url')->nullable()->change();
            $table->longText('video_url')->nullable()->change();
            $table->longText('external_url')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_contents', function (Blueprint $table) {
            // Revert back to TEXT columns
            $table->text('media_url')->nullable()->change();
            $table->text('thumbnail_url')->nullable()->change();
            $table->text('video_url')->nullable()->change();
            $table->text('external_url')->nullable()->change();
        });
    }
};
