<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the enum to include MEDIA_CREATOR
        DB::statement("ALTER TABLE instagram_accounts MODIFY COLUMN account_type ENUM('BUSINESS', 'CREATOR', 'MEDIA_CREATOR')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum
        DB::statement("ALTER TABLE instagram_accounts MODIFY COLUMN account_type ENUM('BUSINESS', 'CREATOR')");
    }
};
