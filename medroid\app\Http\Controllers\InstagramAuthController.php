<?php

namespace App\Http\Controllers;

use App\Models\InstagramAccount;
use App\Services\InstagramService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class InstagramAuthController extends Controller
{
    protected $instagramService;

    public function __construct(InstagramService $instagramService)
    {
        $this->instagramService = $instagramService;
    }

    /**
     * Redirect user to Instagram authorization (Instagram Business API)
     */
    public function redirectToInstagram()
    {
        try {
            $state = base64_encode(json_encode([
                'user_id' => Auth::id(),
                'timestamp' => time(),
                'random' => bin2hex(random_bytes(8)), // Add randomness for security
            ]));

            $authUrl = $this->instagramService->getAuthorizationUrl($state);

            Log::info('Redirecting to Instagram authorization', [
                'user_id' => Auth::id(),
                'auth_url' => $authUrl
            ]);

            return redirect($authUrl);
        } catch (\Exception $e) {
            Log::error('Instagram redirect failed', ['error' => $e->getMessage()]);
            return redirect('/discover')->with('error', 'Failed to initiate Instagram connection');
        }
    }

    /**
     * Handle Instagram callback (Instagram Business API)
     */
    public function handleInstagramCallback(Request $request)
    {
        $code = $request->get('code');
        $state = $request->get('state');
        $error = $request->get('error');
        $errorDescription = $request->get('error_description');

        Log::info('Instagram callback received', [
            'has_code' => !empty($code),
            'has_state' => !empty($state),
            'error' => $error,
            'error_description' => $errorDescription
        ]);

        // Handle authorization errors
        if ($error) {
            Log::warning('Instagram authorization error', [
                'error' => $error,
                'description' => $errorDescription
            ]);

            $message = $error === 'access_denied'
                ? 'Instagram authorization was cancelled'
                : 'Instagram authorization failed: ' . $errorDescription;

            return redirect('/discover')->with('error', $message);
        }

        if (!$code) {
            return redirect('/discover')->with('error', 'Instagram authorization failed - no code received');
        }

        // Verify state for security
        if ($state) {
            try {
                $stateData = json_decode(base64_decode($state), true);
                if (!$stateData || !isset($stateData['user_id']) || !isset($stateData['timestamp'])) {
                    Log::warning('Invalid state parameter in Instagram callback', [
                        'state_data' => $stateData
                    ]);
                    return redirect('/discover')->with('error', 'Invalid state parameter');
                }

                // Check if state is not too old (within 10 minutes)
                $stateAge = time() - $stateData['timestamp'];
                if ($stateAge > 600) {
                    Log::warning('Expired state parameter in Instagram callback', [
                        'age_seconds' => $stateAge
                    ]);
                    return redirect('/discover')->with('error', 'Authorization session expired. Please try again.');
                }

                Log::info('State validation successful', [
                    'user_id' => $stateData['user_id'],
                    'age_seconds' => $stateAge
                ]);

            } catch (\Exception $e) {
                Log::warning('Failed to decode state parameter', ['error' => $e->getMessage()]);
                return redirect('/discover')->with('error', 'Invalid state parameter');
            }
        }

        try {
            $user = Auth::user();
            if (!$user) {
                return redirect('/login')->with('error', 'Please login to connect Instagram');
            }

            // Connect the account using the service
            $result = $this->instagramService->connectAccount($user, $code);

            if ($result['success']) {
                return redirect('/discover')->with('success', $result['message']);
            } else {
                return redirect('/discover')->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('Instagram callback error', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect('/discover')->with('error', 'Failed to connect Instagram account');
        }
    }



    /**
     * Disconnect Instagram account
     */
    public function disconnect()
    {
        $instagramAccount = InstagramAccount::where('user_id', Auth::id())->first();
        
        if ($instagramAccount) {
            $instagramAccount->update(['is_active' => false]);
        }

        return redirect('/dashboard')->with('success', 'Instagram account disconnected');
    }

    /**
     * Manually sync Instagram content
     */
    public function syncContent()
    {
        $instagramAccount = InstagramAccount::where('user_id', Auth::id())
            ->where('is_active', true)
            ->first();

        if (!$instagramAccount) {
            return response()->json(['error' => 'No active Instagram account found'], 404);
        }

        try {
            $this->instagramService->syncAccountContent($instagramAccount);
            return response()->json(['message' => 'Content synced successfully']);
        } catch (\Exception $e) {
            Log::error('Manual sync failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return response()->json(['error' => 'Sync failed'], 500);
        }
    }
}
