<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

// Get user from page props
const page = usePage();
const user = computed(() => page.props.auth?.user);

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Categories', href: '/admin/categories' },
];

const loading = ref(false);
const categories = ref([]);
const parentCategories = ref([]);
const searchQuery = ref('');
const selectedParent = ref('all');

const fetchCategories = async () => {
    loading.value = true;
    try {
        const params = new URLSearchParams();
        if (searchQuery.value) params.append('search', searchQuery.value);
        if (selectedParent.value !== 'all') params.append('parent_id', selectedParent.value);

        const response = await window.axios.get(`/admin/categories-list?${params.toString()}`);
        
        // Handle paginated response structure
        if (response.data.categories && response.data.categories.data) {
            categories.value = response.data.categories.data || [];
        } else if (Array.isArray(response.data.categories)) {
            categories.value = response.data.categories;
        } else {
            categories.value = [];
        }

        if (response.data.parent_categories) {
            parentCategories.value = response.data.parent_categories;
        }
    } catch (error) {
        console.error('Error fetching categories:', error);
        categories.value = [];
    } finally {
        loading.value = false;
    }
};

const deleteCategory = async (categoryId) => {
    if (!confirm('Are you sure you want to delete this category?')) {
        return;
    }

    try {
        const response = await window.axios.delete(`/admin/delete-category/${categoryId}`);
        if (response.data.success) {
            alert('Category deleted successfully');
            fetchCategories();
        } else {
            alert(response.data.message || 'Error deleting category');
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        alert(error.response?.data?.message || 'Error deleting category');
    }
};

const toggleStatus = async (categoryId) => {
    try {
        const response = await window.axios.patch(`/admin/toggle-category-status/${categoryId}`);
        if (response.data.success) {
            fetchCategories();
        } else {
            alert(response.data.message || 'Error updating category status');
        }
    } catch (error) {
        console.error('Error toggling category status:', error);
        alert('Error updating category status');
    }
};

const getStatusBadgeClass = (isActive) => {
    return isActive 
        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
};

const filteredCategories = computed(() => {
    return categories.value.filter(category => {
        const matchesSearch = !searchQuery.value ||
            category.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            (category.description && category.description.toLowerCase().includes(searchQuery.value.toLowerCase()));

        const matchesParent = selectedParent.value === 'all' ||
            (selectedParent.value === 'root' && !category.parent_id) ||
            category.parent_id == selectedParent.value;

        return matchesSearch && matchesParent;
    });
});

// Permission checks
const canCreateCategories = computed(() => {
    return user.value?.user_permissions?.includes('create products') || 
           user.value?.roles?.some(role => role.name === 'admin') ||
           false;
});

const canEditCategories = computed(() => {
    return user.value?.user_permissions?.includes('edit products') || 
           user.value?.roles?.some(role => role.name === 'admin') ||
           false;
});

const canDeleteCategories = computed(() => {
    return user.value?.user_permissions?.includes('delete products') || 
           user.value?.roles?.some(role => role.name === 'admin') ||
           false;
});

onMounted(() => {
    fetchCategories();
});
</script>

<template>
    <Head title="Product Categories" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Product Categories
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <div v-if="canCreateCategories">
                    <Link href="/admin/categories/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-plus mr-2"></i>
                        Add Category
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Filters -->
                <div class="mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <input 
                                    v-model="searchQuery"
                                    type="text" 
                                    placeholder="Search categories..." 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                            </div>
                            <div>
                                <select 
                                    v-model="selectedParent"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="all">All Categories</option>
                                    <option value="root">Root Categories</option>
                                    <option v-for="parent in parentCategories" :key="parent.id" :value="parent.id">
                                        {{ parent.name }} (Subcategories)
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button 
                                    @click="fetchCategories"
                                    class="w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Categories Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="filteredCategories.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No categories found.</p>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Category
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Parent
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Products
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Sort Order
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="category in filteredCategories" :key="category.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ category.name }}
                                                </div>
                                                <div v-if="category.description" class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ category.description }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ category.parent?.name || 'Root Category' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ category.products_count || 0 }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ category.sort_order }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button 
                                                @click="toggleStatus(category.id)"
                                                :class="getStatusBadgeClass(category.is_active)" 
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer hover:opacity-80"
                                            >
                                                {{ category.is_active ? 'Active' : 'Inactive' }}
                                            </button>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link :href="`/admin/categories/${category.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                View
                                            </Link>
                                            <Link v-if="canEditCategories" :href="`/admin/categories/${category.id}/edit`" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">
                                                Edit
                                            </Link>
                                            <button v-if="canDeleteCategories" @click="deleteCategory(category.id)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
