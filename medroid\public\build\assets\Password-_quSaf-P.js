import{M as g,r as f,U as v,y,e as V,g as a,f as r,u as s,m as b,i as t,j as x,x as d,aj as k,l as C,T as I}from"./vendor-BK2qhpQJ.js";import{_ as u}from"./InputError.vue_vue_type_script_setup_true_lang-CMeAZ6jx.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import{_ as $}from"./Layout.vue_vue_type_script_setup_true_lang-BvmLVDru.js";import{_ as P}from"./HeadingSmall.vue_vue_type_script_setup_true_lang-CnAxxXgy.js";import{_ as N}from"./index-BsQMq1AH.js";import{_ as m,a as c}from"./Label.vue_vue_type_script_setup_true_lang-Cu3Z5Mpv.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";import"./index-vK5yNL7A.js";const T={class:"space-y-6"},U={class:"grid gap-2"},E={class:"grid gap-2"},M={class:"grid gap-2"},B={class:"flex items-center gap-4"},j={class:"text-sm text-neutral-600"},Q=g({__name:"Password",setup(H){const w=[{title:"Password settings",href:"/settings/password"}],l=f(null),i=f(null),e=v({current_password:"",password:"",password_confirmation:""}),_=()=>{e.put(route("password.update"),{preserveScroll:!0,onSuccess:()=>e.reset(),onError:p=>{p.password&&(e.reset("password","password_confirmation"),l.value instanceof HTMLInputElement&&l.value.focus()),p.current_password&&(e.reset("current_password"),i.value instanceof HTMLInputElement&&i.value.focus())}})};return(p,o)=>(V(),y(S,{breadcrumbs:w},{default:a(()=>[r(s(b),{title:"Password settings"}),r($,null,{default:a(()=>[t("div",T,[r(P,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),t("form",{onSubmit:x(_,["prevent"]),class:"space-y-6"},[t("div",U,[r(s(m),{for:"current_password"},{default:a(()=>o[3]||(o[3]=[d("Current password")])),_:1}),r(s(c),{id:"current_password",ref_key:"currentPasswordInput",ref:i,modelValue:s(e).current_password,"onUpdate:modelValue":o[0]||(o[0]=n=>s(e).current_password=n),type:"password",class:"mt-1 block w-full",autocomplete:"current-password",placeholder:"Current password"},null,8,["modelValue"]),r(u,{message:s(e).errors.current_password},null,8,["message"])]),t("div",E,[r(s(m),{for:"password"},{default:a(()=>o[4]||(o[4]=[d("New password")])),_:1}),r(s(c),{id:"password",ref_key:"passwordInput",ref:l,modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=n=>s(e).password=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"New password"},null,8,["modelValue"]),r(u,{message:s(e).errors.password},null,8,["message"])]),t("div",M,[r(s(m),{for:"password_confirmation"},{default:a(()=>o[5]||(o[5]=[d("Confirm password")])),_:1}),r(s(c),{id:"password_confirmation",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=n=>s(e).password_confirmation=n),type:"password",class:"mt-1 block w-full",autocomplete:"new-password",placeholder:"Confirm password"},null,8,["modelValue"]),r(u,{message:s(e).errors.password_confirmation},null,8,["message"])]),t("div",B,[r(s(N),{disabled:s(e).processing},{default:a(()=>o[6]||(o[6]=[d("Save password")])),_:1},8,["disabled"]),r(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:a(()=>[C(t("p",j,"Saved.",512),[[I,s(e).recentlySuccessful]])]),_:1})])],32)])]),_:1})]),_:1}))}});export{Q as default};
