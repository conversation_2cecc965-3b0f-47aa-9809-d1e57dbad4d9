# Instagram Feed Integration Plan for Medroid Discover Tab

## Overview
This document outlines the plan to integrate Instagram health-related content alongside native posts in the Medroid discover tab.

## Current State
- ✅ Native post creation with hashtag suggestions
- ✅ Search functionality using hashtags and keywords
- ✅ Profile integration with social stats
- ✅ Story creation and display
- ✅ Medroid theme colors implemented

## Instagram Integration Strategy

### 1. Content Curation Approach
**Mixed Feed Strategy**: Display Instagram content alongside native posts in a unified timeline

#### Content Sources:
- **Native Posts**: User-generated content from Medroid users
- **Instagram Posts**: Curated health-related content from verified health accounts
- **Stories**: Both native and Instagram stories (if available via API)

### 2. Technical Implementation

#### A. Instagram Basic Display API Integration
```php
// New Controller: InstagramFeedController
class InstagramFeedController extends Controller
{
    public function fetchHealthContent()
    {
        // Fetch from verified health accounts
        // Filter content using health keywords
        // Store in social_content table with source='instagram'
    }
}
```

#### B. Database Schema Updates
```sql
-- Add Instagram-specific fields to social_content table
ALTER TABLE social_content ADD COLUMN instagram_id VARCHAR(255) NULL;
ALTER TABLE social_content ADD COLUMN instagram_permalink TEXT NULL;
ALTER TABLE social_content ADD COLUMN original_author VARCHAR(255) NULL;
ALTER TABLE social_content ADD COLUMN verification_status ENUM('verified', 'unverified') DEFAULT 'unverified';
```

#### C. Content Filtering & Moderation
- **Health Keyword Filtering**: Use AI/ML to identify health-related content
- **Manual Curation**: Admin panel to approve/reject Instagram content
- **Automatic Updates**: Scheduled jobs to fetch new content periodically

### 3. Frontend Implementation

#### A. Unified Feed Display
```vue
<!-- Mixed feed with source indicators -->
<article v-for="post in mixedFeed" :key="post.id">
  <!-- Source indicator -->
  <div class="source-badge">
    <span v-if="post.source === 'internal'" class="native-badge">Medroid</span>
    <span v-if="post.source === 'instagram'" class="instagram-badge">Instagram</span>
  </div>
  
  <!-- Post content -->
  <div class="post-content">
    <!-- Unified post display regardless of source -->
  </div>
</article>
```

#### B. Feed Mixing Algorithm
```javascript
const mixedFeed = computed(() => {
  const native = state.posts
  const instagram = state.instagramPosts
  
  // Mix posts based on:
  // 1. Recency (70% weight)
  // 2. Engagement (20% weight)  
  // 3. Relevance to user interests (10% weight)
  
  return mixPosts(native, instagram)
})
```

### 4. Content Management

#### A. Admin Dashboard Features
- **Instagram Account Management**: Add/remove health accounts to follow
- **Content Moderation**: Approve/reject fetched content
- **Keyword Management**: Update health-related keywords for filtering
- **Analytics**: Track engagement on Instagram vs native content

#### B. Automated Content Pipeline
```
Instagram API → Content Fetcher → Health Filter → Moderation Queue → Approved Content → Display in Feed
```

### 5. User Experience Enhancements

#### A. Content Interaction
- **Native Posts**: Full interaction (like, comment, share)
- **Instagram Posts**: Limited interaction (like, view on Instagram)
- **Attribution**: Clear source attribution and links to original content

#### B. Search Integration
- Search across both native and Instagram content
- Filter by content source
- Hashtag search works across all content types

### 6. Implementation Phases

#### Phase 1: Basic Integration (Week 1-2)
- [ ] Set up Instagram Basic Display API
- [ ] Create InstagramFeedController
- [ ] Implement basic content fetching
- [ ] Update database schema
- [ ] Basic mixed feed display

#### Phase 2: Content Curation (Week 3-4)
- [ ] Implement health keyword filtering
- [ ] Create admin moderation interface
- [ ] Add content approval workflow
- [ ] Implement scheduled content fetching

#### Phase 3: Advanced Features (Week 5-6)
- [ ] Smart feed mixing algorithm
- [ ] Advanced search across all content
- [ ] Analytics and insights
- [ ] Performance optimization

#### Phase 4: Polish & Launch (Week 7-8)
- [ ] UI/UX refinements
- [ ] Testing and bug fixes
- [ ] Documentation
- [ ] Production deployment

### 7. Technical Considerations

#### A. API Rate Limits
- Instagram Basic Display API: 200 requests/hour per user
- Implement caching and batch processing
- Use webhooks for real-time updates when available

#### B. Content Storage
- Store Instagram content locally for faster access
- Implement content expiration based on Instagram's terms
- Regular cleanup of outdated content

#### C. Legal & Compliance
- Respect Instagram's Terms of Service
- Proper attribution and linking
- User privacy considerations
- Content licensing compliance

### 8. Monitoring & Analytics

#### A. Key Metrics
- **Engagement Rate**: Compare native vs Instagram content
- **User Retention**: Impact on user session duration
- **Content Quality**: User feedback and interaction patterns
- **API Usage**: Monitor rate limits and performance

#### B. Success Criteria
- 30% increase in user engagement
- 50% increase in time spent on discover tab
- Positive user feedback on content variety
- Stable performance with mixed content

### 9. Risk Mitigation

#### A. Technical Risks
- **API Changes**: Instagram API deprecation or changes
- **Performance**: Large content volumes affecting load times
- **Content Quality**: Irrelevant or inappropriate content

#### B. Mitigation Strategies
- Implement fallback to native-only content
- Content caching and optimization
- Robust filtering and moderation systems
- Regular API monitoring and updates

### 10. Future Enhancements

#### A. Additional Platforms
- TikTok health content integration
- YouTube health videos
- Twitter health discussions

#### B. AI-Powered Features
- Personalized content recommendations
- Automatic health topic categorization
- Sentiment analysis for content quality

#### C. Community Features
- User-curated health content collections
- Expert verification system
- Community-driven content moderation

## Next Steps

1. **Immediate**: Set up Instagram Developer Account and API access
2. **Week 1**: Implement basic Instagram content fetching
3. **Week 2**: Create mixed feed display
4. **Week 3**: Add content moderation system
5. **Week 4**: Launch beta version for testing

## Resources Needed

- Instagram Developer Account
- Additional server resources for content processing
- Admin interface development
- Content moderation guidelines
- Legal review of Instagram content usage

---

This plan provides a comprehensive roadmap for integrating Instagram health content into the Medroid discover tab while maintaining a high-quality user experience and respecting platform guidelines.
