import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/notification_service.dart';
import 'package:medroid_app/services/call_navigation_service.dart';

/// Test widget for debugging push notifications
/// Only shows in debug mode
class TestNotificationButton extends StatelessWidget {
  const TestNotificationButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Debug: Test Notifications',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _testIncomingCall(context),
                  icon: const Icon(Icons.videocam),
                  label: const Text('Test Incoming Call'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _testLocalNotification(context),
                  icon: const Icon(Icons.notifications),
                  label: const Text('Test Local Notification'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _testIncomingCall(BuildContext context) {
    final testCallData = {
      'type': 'video_call',
      'appointment_id': '123',
      'session_id': 'test_session_${DateTime.now().millisecondsSinceEpoch}',
      'channel_name': 'test_channel',
      'provider_name': 'Dr. Test Provider',
      'provider_id': '1',
      'provider_specialty': 'General Medicine',
      'call_action': 'incoming_call',
      'timestamp': DateTime.now().toIso8601String(),
    };

    CallNavigationService.showIncomingCall(testCallData);
  }

  void _testLocalNotification(BuildContext context) async {
    try {
      final notificationService =
          RepositoryProvider.of<NotificationService>(context);

      await notificationService.showTestNotification();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test notification sent!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
