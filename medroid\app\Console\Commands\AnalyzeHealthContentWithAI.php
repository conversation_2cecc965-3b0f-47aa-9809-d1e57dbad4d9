<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SocialMediaPost;
use App\Models\SocialContent;
use App\Services\AIHealthFilterService;
use App\Services\InstagramService;

class AnalyzeHealthContentWithAI extends Command
{
    protected $signature = 'health:analyze-ai {--batch-size=3 : Number of posts to analyze in each batch (keep small for cost efficiency)} {--force : Re-analyze already processed posts}';
    protected $description = 'Analyze social media posts using AI to determine health relevance';

    private $aiFilterService;
    private $instagramService;

    public function __construct(AIHealthFilterService $aiFilterService, InstagramService $instagramService)
    {
        parent::__construct();
        $this->aiFilterService = $aiFilterService;
        $this->instagramService = $instagramService;
    }

    public function handle()
    {
        $this->info('🤖 Starting AI-powered health content analysis...');
        
        $batchSize = $this->option('batch-size');
        $force = $this->option('force');
        
        // Get posts that need analysis
        $query = SocialMediaPost::where('platform', 'instagram');
        
        if (!$force) {
            // Only analyze posts that don't have corresponding SocialContent entries
            $query->whereNotExists(function ($subQuery) {
                $subQuery->select('id')
                    ->from('social_contents')
                    ->whereColumn('social_contents.source_id', 'social_media_posts.platform_post_id')
                    ->where('social_contents.source', 'instagram');
            });
        }
        
        $totalPosts = $query->count();
        
        if ($totalPosts === 0) {
            $this->info('✅ No posts need analysis.');
            return 0;
        }
        
        $this->info("📊 Found {$totalPosts} posts to analyze");
        
        $processed = 0;
        $healthRelated = 0;
        $errors = 0;
        
        $progressBar = $this->output->createProgressBar($totalPosts);
        $progressBar->start();
        
        // Process posts in batches
        $query->chunk($batchSize, function ($posts) use (&$processed, &$healthRelated, &$errors, $progressBar) {
            try {
                // Prepare posts for batch analysis
                $postsForAnalysis = $posts->map(function ($post) {
                    return [
                        'id' => $post->id,
                        'caption' => $post->caption,
                        'platform_post_id' => $post->platform_post_id,
                        'user_id' => $post->user_id
                    ];
                })->toArray();
                
                // Batch analyze with AI
                $results = $this->aiFilterService->batchAnalyzePosts($postsForAnalysis);
                
                // Process results
                foreach ($posts as $index => $post) {
                    $isHealthRelated = $results[$index] ?? false;
                    
                    if ($isHealthRelated) {
                        $this->createSocialContentEntry($post);
                        $healthRelated++;
                    }
                    
                    $processed++;
                    $progressBar->advance();
                }
                
            } catch (\Exception $e) {
                $this->error("❌ Error processing batch: " . $e->getMessage());
                $errors += count($posts);
                $processed += count($posts);
                $progressBar->advance(count($posts));
            }
        });
        
        $progressBar->finish();
        $this->newLine(2);
        
        // Display results
        $this->info('🎉 AI Analysis Complete!');
        $this->table(
            ['Metric', 'Count', 'Percentage'],
            [
                ['Total Processed', $processed, '100%'],
                ['Health-Related', $healthRelated, $processed > 0 ? round(($healthRelated / $processed) * 100, 1) . '%' : '0%'],
                ['Non-Health', $processed - $healthRelated - $errors, $processed > 0 ? round((($processed - $healthRelated - $errors) / $processed) * 100, 1) . '%' : '0%'],
                ['Errors', $errors, $processed > 0 ? round(($errors / $processed) * 100, 1) . '%' : '0%'],
            ]
        );
        
        if ($healthRelated > 0) {
            $this->info("✨ {$healthRelated} new health-related posts added to discover feed!");
        }
        
        return 0;
    }
    
    /**
     * Create SocialContent entry for health-related post
     */
    private function createSocialContentEntry($post)
    {
        try {
            // Get the Instagram account
            $instagramAccount = \App\Models\InstagramAccount::where('user_id', $post->user_id)
                ->where('is_active', true)
                ->first();
                
            if (!$instagramAccount) {
                return;
            }
            
            // Check if SocialContent already exists
            $existingContent = SocialContent::where('source_id', $post->platform_post_id)
                ->where('source', 'instagram')
                ->first();
                
            if ($existingContent) {
                return; // Already exists
            }
            
            // Use reflection to call the protected method from InstagramService
            $reflection = new \ReflectionClass($this->instagramService);
            $method = $reflection->getMethod('createSocialContentFromPost');
            $method->setAccessible(true);
            $method->invoke($this->instagramService, $post, $instagramAccount);
            
        } catch (\Exception $e) {
            \Log::error('Failed to create SocialContent entry', [
                'post_id' => $post->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
