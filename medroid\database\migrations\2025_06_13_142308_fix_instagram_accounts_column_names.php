<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('instagram_accounts', function (Blueprint $table) {
            // Rename columns to match the model expectations
            $table->renameColumn('token_expires_at', 'expires_at');
            $table->renameColumn('last_synced_at', 'last_sync_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('instagram_accounts', function (Blueprint $table) {
            // Revert column names back to original
            $table->renameColumn('expires_at', 'token_expires_at');
            $table->renameColumn('last_sync_at', 'last_synced_at');
        });
    }
};
