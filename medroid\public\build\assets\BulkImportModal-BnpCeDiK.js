import{r as p,c as $,d as o,n as u,e as a,i as e,A as v,x as D,t as n,F as R,p as T,J as V}from"./vendor-BK2qhpQJ.js";const U={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},E={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},M={class:"mt-3"},L={class:"flex items-center justify-between mb-6"},z={class:"mb-6"},N={class:"flex items-center space-x-4 mb-4"},A={key:0,class:"space-y-4"},O={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},X=["disabled"],q={class:"flex justify-end"},K={key:1,class:"space-y-4"},G={class:"bg-green-50 border border-green-200 rounded-lg p-4"},J={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},Y={key:0},H={key:1,class:"space-y-2"},Q={class:"font-medium text-gray-900"},W={class:"text-gray-500 text-sm"},Z={class:"flex justify-between"},ee=["disabled"],te={key:2,class:"space-y-4"},se={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},le={key:0,class:"space-y-3"},oe={class:"flex items-center justify-between text-sm"},ae={class:"font-medium"},re={class:"flex items-center justify-between text-sm"},ie={class:"font-medium text-green-600"},ne={key:0,class:"flex items-center justify-between text-sm"},de={class:"font-medium text-red-600"},ue={key:1,class:"mt-4"},me={class:"max-h-32 overflow-y-auto bg-red-50 border border-red-200 rounded p-3"},pe={key:0,class:"text-red-600 text-sm mt-2"},ce={class:"flex justify-between"},ve=["disabled"],xe={key:3,class:"mt-4"},be={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},fe={key:0,class:"mt-3"},ge={class:"w-full bg-blue-200 rounded-full h-2"},ye={class:"text-blue-700 text-sm mt-1"},he={__name:"BulkImportModal",props:{isOpen:{type:Boolean,default:!1}},emits:["close","imported"],setup(_,{emit:k}){const w=k,r=p(1),f=p(!1),d=p(null),g=p(!1),l=p(null),x=p(!1),c=p(0),C=$(()=>window.location.pathname.includes("/provider/")),y=()=>C.value?"/provider":"/admin",S=async()=>{f.value=!0;try{const s=await window.axios.get(`${y()}/products/import-template`,{responseType:"blob"}),t=window.URL.createObjectURL(new Blob([s.data])),i=document.createElement("a");i.href=t,i.setAttribute("download","product_import_template.csv"),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(t)}catch(s){console.error("Error downloading template:",s),alert("Error downloading template. Please try again.")}finally{f.value=!1}},F=s=>{const t=s.target.files[0];t&&(d.value=t)},I=()=>{d.value=null,l.value=null},P=s=>{if(s===0)return"0 Bytes";const t=1024,i=["Bytes","KB","MB","GB"],b=Math.floor(Math.log(s)/Math.log(t));return parseFloat((s/Math.pow(t,b)).toFixed(2))+" "+i[b]},j=async()=>{if(d.value){g.value=!0;try{const s=new FormData;s.append("file",d.value);const t=await window.axios.post(`${y()}/products/validate-import`,s,{headers:{"Content-Type":"multipart/form-data"}});l.value=t.data,r.value=3}catch(s){console.error("Error validating file:",s),alert("Error validating file. Please check the format and try again.")}finally{g.value=!1}}},B=async()=>{if(!(!d.value||!l.value)){x.value=!0,c.value=0;try{const s=new FormData;s.append("file",d.value);const t=await window.axios.post(`${y()}/products/bulk-import`,s,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:i=>{c.value=Math.round(i.loaded*100/i.total)}});w("imported",t.data),w("close")}catch(s){console.error("Error importing products:",s),alert("Error importing products. Please try again.")}finally{x.value=!1,c.value=0}}};return(s,t)=>{var i,b,h;return _.isOpen?(a(),o("div",U,[e("div",E,[e("div",M,[e("div",L,[t[6]||(t[6]=e("h3",{class:"text-lg font-medium text-gray-900"},"Bulk Import Products",-1)),e("button",{onClick:t[0]||(t[0]=m=>s.$emit("close")),class:"text-gray-400 hover:text-gray-600"},t[5]||(t[5]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),e("div",z,[e("div",N,[e("div",{class:v(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",r.value>=1?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"])}," 1 ",2),e("span",{class:v(["text-sm",r.value>=1?"text-blue-600 font-medium":"text-gray-500"])}," Download Template ",2),t[7]||(t[7]=e("div",{class:"flex-1 h-px bg-gray-200"},null,-1)),e("div",{class:v(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",r.value>=2?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"])}," 2 ",2),e("span",{class:v(["text-sm",r.value>=2?"text-blue-600 font-medium":"text-gray-500"])}," Upload File ",2),t[8]||(t[8]=e("div",{class:"flex-1 h-px bg-gray-200"},null,-1)),e("div",{class:v(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",r.value>=3?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"])}," 3 ",2),e("span",{class:v(["text-sm",r.value>=3?"text-blue-600 font-medium":"text-gray-500"])}," Review & Import ",2)])]),r.value===1?(a(),o("div",A,[e("div",O,[t[10]||(t[10]=e("h4",{class:"font-medium text-blue-900 mb-2"},"Step 1: Download Import Template",-1)),t[11]||(t[11]=e("p",{class:"text-blue-800 text-sm mb-4"}," Download the CSV template file and fill it with your product data. Make sure to follow the format exactly. ",-1)),e("button",{onClick:S,disabled:f.value,class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"},[t[9]||(t[9]=e("i",{class:"fas fa-download mr-2"},null,-1)),D(" "+n(f.value?"Downloading...":"Download Template"),1)],8,X)]),t[12]||(t[12]=e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},[e("h5",{class:"font-medium text-yellow-900 mb-2"},"Important Notes:"),e("ul",{class:"text-yellow-800 text-sm space-y-1"},[e("li",null,"• All required fields must be filled"),e("li",null,"• SKU must be unique for each product"),e("li",null,"• Price should be in decimal format (e.g., 29.99)"),e("li",null,"• Type should be either 'physical' or 'digital'"),e("li",null,"• Category ID must exist in the system")])],-1)),e("div",q,[e("button",{onClick:t[1]||(t[1]=m=>r.value=2),class:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"}," Next: Upload File ")])])):u("",!0),r.value===2?(a(),o("div",K,[e("div",G,[t[17]||(t[17]=e("h4",{class:"font-medium text-green-900 mb-2"},"Step 2: Upload Your CSV File",-1)),t[18]||(t[18]=e("p",{class:"text-green-800 text-sm mb-4"}," Select the CSV file you've prepared with your product data. ",-1)),e("div",J,[e("input",{ref:"fileInput",type:"file",accept:".csv,.xlsx,.xls",onChange:F,class:"hidden"},null,544),d.value?(a(),o("div",H,[t[16]||(t[16]=e("i",{class:"fas fa-file-csv text-4xl text-green-500"},null,-1)),e("p",Q,n(d.value.name),1),e("p",W,n(P(d.value.size)),1),e("button",{onClick:I,class:"text-red-600 hover:text-red-800 text-sm"}," Remove file ")])):(a(),o("div",Y,[t[13]||(t[13]=e("i",{class:"fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"},null,-1)),t[14]||(t[14]=e("p",{class:"text-gray-600 mb-2"},"Click to select a file or drag and drop",-1)),t[15]||(t[15]=e("p",{class:"text-gray-500 text-sm"},"CSV, XLSX, or XLS files only",-1)),e("button",{onClick:t[2]||(t[2]=m=>s.$refs.fileInput.click()),class:"mt-4 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"}," Select File ")]))])]),e("div",Z,[e("button",{onClick:t[3]||(t[3]=m=>r.value=1),class:"px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400"}," Previous "),e("button",{onClick:j,disabled:!d.value||g.value,class:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"},n(g.value?"Validating...":"Next: Review"),9,ee)])])):u("",!0),r.value===3?(a(),o("div",te,[e("div",se,[t[23]||(t[23]=e("h4",{class:"font-medium text-purple-900 mb-2"},"Step 3: Review & Import",-1)),t[24]||(t[24]=e("p",{class:"text-purple-800 text-sm mb-4"}," Review the validation results and proceed with the import. ",-1)),l.value?(a(),o("div",le,[e("div",oe,[t[19]||(t[19]=e("span",{class:"text-gray-600"},"Total rows:",-1)),e("span",ae,n(l.value.total_rows),1)]),e("div",re,[t[20]||(t[20]=e("span",{class:"text-green-600"},"Valid rows:",-1)),e("span",ie,n(l.value.valid_rows),1)]),l.value.invalid_rows>0?(a(),o("div",ne,[t[21]||(t[21]=e("span",{class:"text-red-600"},"Invalid rows:",-1)),e("span",de,n(l.value.invalid_rows),1)])):u("",!0)])):u("",!0),((b=(i=l.value)==null?void 0:i.errors)==null?void 0:b.length)>0?(a(),o("div",ue,[t[22]||(t[22]=e("h5",{class:"font-medium text-red-900 mb-2"},"Validation Errors:",-1)),e("div",me,[(a(!0),o(R,null,T(l.value.errors.slice(0,10),m=>(a(),o("div",{key:m.row,class:"text-red-800 text-sm"}," Row "+n(m.row)+": "+n(m.message),1))),128)),l.value.errors.length>10?(a(),o("div",pe," ... and "+n(l.value.errors.length-10)+" more errors ",1)):u("",!0)])])):u("",!0)]),e("div",ce,[e("button",{onClick:t[4]||(t[4]=m=>r.value=2),class:"px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400"}," Previous "),e("button",{onClick:B,disabled:!l.value||l.value.valid_rows===0||x.value,class:"px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:opacity-50"},n(x.value?"Importing...":`Import ${((h=l.value)==null?void 0:h.valid_rows)||0} Products`),9,ve)])])):u("",!0),x.value?(a(),o("div",xe,[e("div",be,[t[25]||(t[25]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),e("span",{class:"text-blue-800 font-medium"},"Importing products...")],-1)),c.value?(a(),o("div",fe,[e("div",ge,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:V({width:`${c.value}%`})},null,4)]),e("p",ye,n(c.value)+"% complete",1)])):u("",!0)])])):u("",!0)])])])):u("",!0)}}};export{he as _};
