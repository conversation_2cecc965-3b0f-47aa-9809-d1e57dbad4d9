<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Clinic;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->command->info('Starting production user role fix...');

        // Ensure roles exist
        $this->ensureRolesExist();

        // Fix users without Spatie roles
        $this->fixUsersWithoutSpatieRoles();

        // Fix users without profiles
        $this->fixUsersWithoutProfiles();

        $this->command->info('Production user role fix completed!');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is safe and doesn't need to be reversed
        $this->command->info('This migration cannot be reversed as it fixes data integrity.');
    }

    private function ensureRolesExist()
    {
        $roles = ['admin', 'manager', 'provider', 'patient'];
        
        foreach ($roles as $roleName) {
            $role = Role::where('name', $roleName)->where('guard_name', 'web')->first();
            if (!$role) {
                Role::create(['name' => $roleName, 'guard_name' => 'web']);
                $this->command->info("Created role: {$roleName}");
            }
        }
    }

    private function fixUsersWithoutSpatieRoles()
    {
        $this->command->info('Checking users without Spatie roles...');
        
        $usersWithoutRoles = User::whereDoesntHave('roles')->get();
        
        $this->command->info("Found {$usersWithoutRoles->count()} users without Spatie roles");

        foreach ($usersWithoutRoles as $user) {
            $roleToAssign = $user->role ?: 'patient';
            
            try {
                $user->assignRole($roleToAssign);
                $this->command->info("✓ Assigned '{$roleToAssign}' role to: {$user->email}");
            } catch (\Exception $e) {
                $this->command->error("✗ Failed to assign role to {$user->email}: {$e->getMessage()}");
            }
        }
    }

    private function fixUsersWithoutProfiles()
    {
        $this->command->info('Checking users without profiles...');
        
        // Fix patients without profiles
        $patientsWithoutProfiles = User::where('role', 'patient')
            ->whereDoesntHave('patient')
            ->get();
            
        $this->command->info("Found {$patientsWithoutProfiles->count()} patients without profiles");

        foreach ($patientsWithoutProfiles as $user) {
            try {
                $this->createPatientProfile($user);
                $this->command->info("✓ Created patient profile for: {$user->email}");
            } catch (\Exception $e) {
                $this->command->error("✗ Failed to create patient profile for {$user->email}: {$e->getMessage()}");
            }
        }

        // Fix providers without profiles
        $providersWithoutProfiles = User::where('role', 'provider')
            ->whereDoesntHave('provider')
            ->get();
            
        $this->command->info("Found {$providersWithoutProfiles->count()} providers without profiles");

        foreach ($providersWithoutProfiles as $user) {
            try {
                $this->createProviderProfile($user);
                $this->command->info("✓ Created provider profile for: {$user->email}");
            } catch (\Exception $e) {
                $this->command->error("✗ Failed to create provider profile for {$user->email}: {$e->getMessage()}");
            }
        }
    }

    private function createPatientProfile($user)
    {
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first();
        }

        $user->patient()->create([
            'user_id' => $user->id,
            'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
            'phone' => null,
            'address' => null,
            'emergency_contact' => null,
            'medical_history' => null,
            'allergies' => null,
            'current_medications' => null,
        ]);

        // Initialize user credits if not exists
        if (!$user->credit) {
            $user->credit()->create([
                'balance' => 0.00,
                'total_earned' => 0.00,
                'total_spent' => 0.00,
            ]);
        }
    }

    private function createProviderProfile($user)
    {
        $defaultClinic = Clinic::where('name', 'Medroid Healthcare Center')->first();
        if (!$defaultClinic) {
            $defaultClinic = Clinic::first();
        }

        $user->provider()->create([
            'user_id' => $user->id,
            'clinic_id' => $defaultClinic ? $defaultClinic->id : null,
            'specialization' => 'General Practice',
            'license_number' => 'PENDING',
            'verification_status' => 'pending',
            'verified_at' => null,
            'gender' => null,
            'bio' => null,
            'rating' => 0,
            'weekly_availability' => [
                ['day' => 'Monday', 'slots' => []],
                ['day' => 'Tuesday', 'slots' => []],
                ['day' => 'Wednesday', 'slots' => []],
                ['day' => 'Thursday', 'slots' => []],
                ['day' => 'Friday', 'slots' => []],
                ['day' => 'Saturday', 'slots' => []],
                ['day' => 'Sunday', 'slots' => []],
            ],
            'absences' => [],
            'practice_locations' => [],
        ]);
    }
};
