<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_media_posts', function (Blueprint $table) {
            // Change URL columns to LONGTEXT to handle very long Instagram URLs
            $table->longText('media_url')->nullable()->change();
            $table->longText('thumbnail_url')->nullable()->change();
            $table->longText('permalink')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_media_posts', function (Blueprint $table) {
            // Revert back to TEXT columns
            $table->text('media_url')->nullable()->change();
            $table->text('thumbnail_url')->nullable()->change();
            $table->text('permalink')->nullable()->change();
        });
    }
};
