import{r as n,o as w,d as r,e as s,f,u as b,m as A,g as c,i as e,l as h,v as N,q as U,n as p,F as y,p as k,t as l,A as M,y as B,P as V,x as C}from"./vendor-BK2qhpQJ.js";import{_ as D}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const L={class:"flex items-center justify-between"},P={class:"flex mt-2","aria-label":"Breadcrumb"},R={class:"inline-flex items-center space-x-1 md:space-x-3"},S={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},E={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},j={class:"py-12"},z={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},F={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},T={class:"p-6"},q={class:"flex flex-col sm:flex-row gap-4"},Q={class:"flex-1"},$={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},G={class:"p-6 text-gray-900 dark:text-gray-100"},H={key:0,class:"text-center py-8"},I={key:1,class:"text-center py-8"},J={key:2,class:"overflow-x-auto"},K={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},O={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},W={class:"px-6 py-4 whitespace-nowrap"},X={class:"flex items-center"},Y={class:"ml-4"},Z={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ee={class:"text-sm text-gray-500 dark:text-gray-400"},te={class:"px-6 py-4 whitespace-nowrap"},ae={class:"px-6 py-4 whitespace-nowrap"},se={class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"},re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},de={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},le={key:0,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},pe={__name:"Users",setup(oe){const i=[{title:"Dashboard",href:"/dashboard"},{title:"Users",href:"/users"}],g=n(!1),o=n([]),u=n(""),m=n("all"),v=async()=>{g.value=!0;try{const d=await window.axios.get("/users-list");o.value=d.data.data||[]}catch(d){console.error("Error fetching users:",d),o.value=[]}finally{g.value=!1}},_=d=>d&&{admin:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",provider:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",patient:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",manager:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"}[d]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return w(()=>{v()}),(d,a)=>(s(),r(y,null,[f(b(A),{title:"User Management"}),f(D,null,{header:c(()=>[e("div",L,[e("div",null,[a[3]||(a[3]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," User Management ",-1)),e("nav",P,[e("ol",R,[(s(),r(y,null,k(i,(t,x)=>e("li",{key:x,class:"inline-flex items-center"},[x<i.length-1?(s(),B(b(V),{key:0,href:t.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[C(l(t.title),1)]),_:2},1032,["href"])):(s(),r("span",S,l(t.title),1)),x<i.length-1?(s(),r("svg",E,a[2]||(a[2]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):p("",!0)])),64))])])]),a[4]||(a[4]=e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add User ",-1))])]),default:c(()=>[e("div",j,[e("div",z,[e("div",F,[e("div",T,[e("div",q,[e("div",Q,[h(e("input",{"onUpdate:modelValue":a[0]||(a[0]=t=>u.value=t),type:"text",placeholder:"Search users...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[N,u.value]])]),e("div",null,[h(e("select",{"onUpdate:modelValue":a[1]||(a[1]=t=>m.value=t),class:"px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},a[5]||(a[5]=[e("option",{value:"all"},"All Roles",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"provider"},"Provider",-1),e("option",{value:"patient"},"Patient",-1),e("option",{value:"manager"},"Manager",-1)]),512),[[U,m.value]])])])])]),e("div",$,[e("div",G,[g.value?(s(),r("div",H,a[6]||(a[6]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):o.value.length===0?(s(),r("div",I,a[7]||(a[7]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No users found.",-1)]))):(s(),r("div",J,[e("table",K,[a[10]||(a[10]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," User "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Role "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Last Login "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",O,[d.user&&d.user.id?(s(!0),r(y,{key:0},k(o.value,t=>(s(),r("tr",{key:(t==null?void 0:t.id)||Math.random(),class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",W,[e("div",X,[a[8]||(a[8]=e("div",{class:"flex-shrink-0 h-10 w-10"},[e("div",{class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},[e("i",{class:"fas fa-user text-gray-500 dark:text-gray-400"})])],-1)),e("div",Y,[e("div",Z,l((t==null?void 0:t.name)||"N/A"),1),e("div",ee,l((t==null?void 0:t.email)||"N/A"),1)])])]),e("td",te,[e("span",{class:M([_(t==null?void 0:t.role),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l((t==null?void 0:t.role)||"N/A"),3)]),e("td",ae,[e("span",se,l((t==null?void 0:t.status)||"N/A"),1)]),e("td",re,l((t==null?void 0:t.last_login)||"N/A"),1),e("td",de,[a[9]||(a[9]=e("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," Edit ",-1)),(t==null?void 0:t.role)!=="admin"?(s(),r("button",le," Delete ")):p("",!0)])]))),128)):p("",!0)])])]))])])])])]),_:1})],64))}};export{pe as default};
