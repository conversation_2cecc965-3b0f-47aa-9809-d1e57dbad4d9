import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:medroid_app/screens/agora_video_consultation_screen.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:async';
import 'dart:math' as math;

class IncomingCallScreen extends StatefulWidget {
  final Map<String, dynamic> callData;

  const IncomingCallScreen({
    Key? key,
    required this.callData,
  }) : super(key: key);

  @override
  State<IncomingCallScreen> createState() => _IncomingCallScreenState();
}

class _IncomingCallScreenState extends State<IncomingCallScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _avatarController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _avatarAnimation;
  
  Timer? _callTimeout;
  Timer? _vibrationTimer;
  bool _isCallActive = true;
  
  // Audio player for ringtone
  late AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _setupAnimations();
    _startRingtone();
    _startVibration();
    _setupCallTimeout();
  }

  void _setupAnimations() {
    // Pulse animation for the accept button
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Avatar pulsing animation
    _avatarController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _avatarAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _avatarController, curve: Curves.easeInOut),
    );

    // Start repeating animations
    _pulseController.repeat(reverse: true);
    _avatarController.repeat(reverse: true);
  }

  void _startRingtone() async {
    try {
      // Try to play a system notification sound
      // For now, we'll use enhanced haptic feedback as ringtone simulation
      // In production, you can add actual audio files to assets/sounds/
      
      debugPrint('📞 Starting call ringtone simulation with haptic feedback');
      
      // Enhanced haptic pattern for incoming call
      HapticFeedback.heavyImpact();
      
      // You can uncomment and use this when you add actual ringtone files:
      /*
      await _audioPlayer.setReleaseMode(ReleaseMode.loop);
      await _audioPlayer.setVolume(0.8);
      await _audioPlayer.play(AssetSource('sounds/ringtone.mp3'));
      */
      
    } catch (e) {
      debugPrint('Error starting ringtone: $e');
    }
  }

  void _stopRingtone() async {
    try {
      await _audioPlayer.stop();
      debugPrint('📞 Ringtone stopped');
    } catch (e) {
      debugPrint('Error stopping ringtone: $e');
    }
  }

  void _startVibration() {
    // Vibrate the device to simulate phone ringing
    HapticFeedback.heavyImpact();
    _vibrationTimer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      if (!_isCallActive) {
        timer.cancel();
        return;
      }
      HapticFeedback.mediumImpact();
    });
  }

  void _setupCallTimeout() {
    // Auto-decline after 30 seconds
    _callTimeout = Timer(const Duration(seconds: 30), () {
      if (_isCallActive) {
        _declineCall();
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _avatarController.dispose();
    _callTimeout?.cancel();
    _vibrationTimer?.cancel();
    _stopRingtone();
    _audioPlayer.dispose();
    super.dispose();
  }

  String get providerName => widget.callData['provider_name'] ?? 'Doctor';
  String get appointmentId => widget.callData['appointment_id']?.toString() ?? '';
  String get providerSpecialty => widget.callData['provider_specialty'] ?? 'Healthcare Provider';

  void _acceptCall() async {
    setState(() {
      _isCallActive = false;
    });

    // Stop all call effects
    _pulseController.stop();
    _avatarController.stop();
    _callTimeout?.cancel();
    _vibrationTimer?.cancel();
    _stopRingtone();

    // Send accept response to backend
    await _sendCallResponse('accepted');

    // Navigate to video call screen
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => AgoraVideoConsultationScreen(
            appointmentId: appointmentId,
            isProvider: false, // Patient is joining the call
            userName: 'Patient',
          ),
        ),
      );
    }
  }

  void _declineCall() async {
    setState(() {
      _isCallActive = false;
    });

    // Stop all call effects
    _pulseController.stop();
    _avatarController.stop();
    _callTimeout?.cancel();
    _vibrationTimer?.cancel();
    _stopRingtone();

    // Send decline response to backend
    await _sendCallResponse('declined');

    // Close the screen
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  Future<void> _sendCallResponse(String response) async {
    try {
      final apiService = ApiService();
      await apiService.post(
        'video/call-response/${widget.callData['appointment_id']}',
        {
          'response': response,
          'session_id': widget.callData['session_id'],
        },
      );
      debugPrint('Call response sent: $response');
    } catch (e) {
      debugPrint('Error sending call response: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1a1a1a),
      body: SafeArea(
        child: Column(
          children: [
            // Top section with call info
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Incoming call label
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        'Incoming Video Call',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Provider avatar with animation
                    AnimatedBuilder(
                      animation: _avatarAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _avatarAnimation.value,
                          child: Container(
                            width: 160,
                            height: 160,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primaryColor,
                                  AppColors.primaryColor.withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primaryColor.withOpacity(0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.person,
                              size: 80,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 30),

                    // Provider name
                    Text(
                      providerName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // Provider specialty
                    Text(
                      providerSpecialty,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 20),

                    // Call status
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Video call invitation',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Bottom section with action buttons
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 30),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Decline button
                    GestureDetector(
                      onTap: _declineCall,
                      child: Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.red.withOpacity(0.3),
                              blurRadius: 15,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.call_end,
                          color: Colors.white,
                          size: 35,
                        ),
                      ),
                    ),

                    // Quick actions (optional)
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Message button
                        GestureDetector(
                          onTap: () {
                            // TODO: Open quick message
                          },
                          child: Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.message,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Message',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),

                    // Accept button with pulse animation
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: GestureDetector(
                            onTap: _acceptCall,
                            child: Container(
                              width: 70,
                              height: 70,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.green.withOpacity(0.4),
                                    blurRadius: 20,
                                    spreadRadius: 3,
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.videocam,
                                color: Colors.white,
                                size: 35,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}