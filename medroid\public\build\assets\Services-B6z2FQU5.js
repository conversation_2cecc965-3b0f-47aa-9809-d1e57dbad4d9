import{z as F,c as U,r as u,o as q,d as p,e as d,i as e,t as c,j as O,n as A,l as a,q as M,F as V,p as E,v as b,s as _,y as j,g as T,A as I}from"./vendor-BK2qhpQJ.js";import{_ as Q}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";const G={class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50"},H={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},J={class:"mt-3"},K={class:"flex justify-between items-center mb-4"},R={class:"text-lg font-medium text-gray-900"},W={key:0},X=["value"],Y={class:"grid grid-cols-2 gap-4"},Z={class:"space-y-3"},ee={class:"flex items-center"},te={key:0,class:"ml-6 space-y-2"},se={class:"flex items-center"},oe={class:"flex items-center"},le={class:"flex items-center"},re={class:"flex items-center"},ie={class:"grid grid-cols-2 gap-4"},ne={class:"flex justify-end space-x-3 pt-4"},ae=["disabled"],de={__name:"ServiceModal",props:{service:{type:Object,default:null},providers:{type:Array,default:()=>[]}},emits:["close","saved"],setup(w,{emit:$}){const v=w,S=$,C=F(),g=U(()=>C.props.auth.user),f=U(()=>{var n;return((n=g.value)==null?void 0:n.role)==="admin"}),y=U(()=>f.value),m=u(!1),s=u({provider_id:"",name:"",description:"",category:"",price:0,duration:30,is_telemedicine:!1,supports_video:!1,supports_audio:!1,supports_chat:!1,active:!0,discount_percentage:null,discount_valid_until:""}),k=async()=>{m.value=!0;try{const n=v.service?`/save-service/${v.service.id}`:"/save-service",t=v.service?"put":"post";await window.axios[t](n,s.value),S("saved")}catch(n){console.error("Error saving service:",n),alert("Error saving service")}finally{m.value=!1}};return q(()=>{v.service&&Object.keys(s.value).forEach(n=>{v.service[n]!==void 0&&(s.value[n]=v.service[n])})}),(n,t)=>(d(),p("div",G,[e("div",H,[e("div",J,[e("div",K,[e("h3",R,c(w.service?"Edit Service":"Create New Service"),1),e("button",{onClick:t[0]||(t[0]=o=>n.$emit("close")),class:"text-gray-400 hover:text-gray-600"},t[15]||(t[15]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("form",{onSubmit:O(k,["prevent"]),class:"space-y-4"},[y.value?(d(),p("div",W,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700"},"Provider",-1)),a(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.value.provider_id=o),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},[t[16]||(t[16]=e("option",{value:""},"Select Provider",-1)),(d(!0),p(V,null,E(w.providers,o=>{var h;return d(),p("option",{key:o.id,value:o.id},c(((h=o.user)==null?void 0:h.name)||"Unknown Provider"),9,X)}),128))],512),[[M,s.value.provider_id]])])):A("",!0),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700"},"Service Name",-1)),a(e("input",{"onUpdate:modelValue":t[2]||(t[2]=o=>s.value.name=o),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter service name"},null,512),[[b,s.value.name]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700"},"Description",-1)),a(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.value.description=o),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter service description"},null,512),[[b,s.value.description]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700"},"Category",-1)),a(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.category=o),type:"text",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter category"},null,512),[[b,s.value.category]])]),e("div",Y,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700"},"Price ($)",-1)),a(e("input",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.value.price=o),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"},null,512),[[b,s.value.price,void 0,{number:!0}]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700"},"Duration (minutes)",-1)),a(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>s.value.duration=o),type:"number",min:"5",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"30"},null,512),[[b,s.value.duration,void 0,{number:!0}]])])]),e("div",Z,[e("div",ee,[a(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>s.value.is_telemedicine=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.is_telemedicine]]),t[23]||(t[23]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Telemedicine Service ",-1))]),s.value.is_telemedicine?(d(),p("div",te,[e("div",se,[a(e("input",{"onUpdate:modelValue":t[8]||(t[8]=o=>s.value.supports_video=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.supports_video]]),t[24]||(t[24]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Supports Video Calls ",-1))]),e("div",oe,[a(e("input",{"onUpdate:modelValue":t[9]||(t[9]=o=>s.value.supports_audio=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.supports_audio]]),t[25]||(t[25]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Supports Audio Calls ",-1))]),e("div",le,[a(e("input",{"onUpdate:modelValue":t[10]||(t[10]=o=>s.value.supports_chat=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.supports_chat]]),t[26]||(t[26]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Supports Chat ",-1))])])):A("",!0)]),e("div",re,[a(e("input",{"onUpdate:modelValue":t[11]||(t[11]=o=>s.value.active=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[_,s.value.active]]),t[27]||(t[27]=e("label",{class:"ml-2 block text-sm text-gray-900"}," Active ",-1))]),e("div",ie,[e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700"},"Discount (%)",-1)),a(e("input",{"onUpdate:modelValue":t[12]||(t[12]=o=>s.value.discount_percentage=o),type:"number",step:"0.01",min:"0",max:"100",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"},null,512),[[b,s.value.discount_percentage,void 0,{number:!0}]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700"},"Discount Valid Until",-1)),a(e("input",{"onUpdate:modelValue":t[13]||(t[13]=o=>s.value.discount_valid_until=o),type:"datetime-local",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[b,s.value.discount_valid_until]])])]),e("div",ne,[e("button",{type:"button",onClick:t[14]||(t[14]=o=>n.$emit("close")),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:m.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},c(m.value?"Saving...":w.service?"Update":"Create"),9,ae)])],32)])])]))}},ue={class:"py-12"},ce={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},pe={class:"bg-white overflow-hidden shadow-xl sm:rounded-lg"},ve={class:"p-6 lg:p-8 bg-white border-b border-gray-200"},me={class:"flex justify-between items-center mb-6"},xe={class:"mb-6 flex flex-wrap gap-4"},be={class:"flex-1 min-w-64"},ge=["value"],fe=["value"],ye={class:"overflow-x-auto"},we={class:"min-w-full divide-y divide-gray-200"},ke={class:"bg-white divide-y divide-gray-200"},he={class:"px-6 py-4 whitespace-nowrap"},_e={class:"text-sm font-medium text-gray-900"},$e={class:"text-sm text-gray-500"},Se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ue={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ve={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ee={class:"px-6 py-4 whitespace-nowrap"},Ae={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Me=["onClick"],Pe=["onClick"],De={key:0,class:"text-center py-12"},Le={__name:"Services",setup(w){const $=u([]),v=u([]),S=u([]),C=u(!1),g=u(""),f=u(""),y=u(""),m=u(!1),s=u(!1),k=u(null),n=U(()=>{let i=$.value;if(g.value){const r=g.value.toLowerCase();i=i.filter(l=>{var x;return l.name.toLowerCase().includes(r)||((x=l.description)==null?void 0:x.toLowerCase().includes(r))})}return f.value&&(i=i.filter(r=>r.category===f.value)),y.value&&(i=i.filter(r=>r.provider_id==y.value)),i}),t=async()=>{C.value=!0;try{const i=await window.axios.get("/services-list");$.value=i.data}catch(i){console.error("Error loading services:",i)}finally{C.value=!1}},o=async()=>{try{const i=await window.axios.get("/get-providers");v.value=i.data}catch(i){console.error("Error loading providers:",i)}},h=async()=>{try{const i=await window.axios.get("/get-service-categories");S.value=i.data.categories||[]}catch(i){console.error("Error loading categories:",i)}},N=i=>{k.value=i,s.value=!0},z=async i=>{if(confirm(`Are you sure you want to delete "${i.name}"?`))try{await window.axios.delete(`/delete-service/${i.id}`),await t()}catch(r){console.error("Error deleting service:",r),alert("Error deleting service")}},P=()=>{m.value=!1,s.value=!1,k.value=null},B=()=>{P(),t(),h()};return q(()=>{t(),o(),h()}),(i,r)=>(d(),j(Q,{title:"Services Management"},{default:T(()=>[e("div",ue,[e("div",ce,[e("div",pe,[e("div",ve,[e("div",me,[r[4]||(r[4]=e("h1",{class:"text-2xl font-medium text-gray-900"},"Services Management",-1)),e("button",{onClick:r[0]||(r[0]=l=>m.value=!0),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Service ")]),e("div",xe,[e("div",be,[a(e("input",{"onUpdate:modelValue":r[1]||(r[1]=l=>g.value=l),type:"text",placeholder:"Search services...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[b,g.value]])]),e("div",null,[a(e("select",{"onUpdate:modelValue":r[2]||(r[2]=l=>f.value=l),class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},[r[5]||(r[5]=e("option",{value:""},"All Categories",-1)),(d(!0),p(V,null,E(S.value,l=>(d(),p("option",{key:l,value:l},c(l),9,ge))),128))],512),[[M,f.value]])]),e("div",null,[a(e("select",{"onUpdate:modelValue":r[3]||(r[3]=l=>y.value=l),class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},[r[6]||(r[6]=e("option",{value:""},"All Providers",-1)),(d(!0),p(V,null,E(v.value,l=>{var x;return d(),p("option",{key:l.id,value:l.id},c(((x=l.user)==null?void 0:x.name)||"Unknown Provider"),9,fe)}),128))],512),[[M,y.value]])])]),e("div",ye,[e("table",we,[r[7]||(r[7]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Service "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Provider "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Category "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Price "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Duration "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",ke,[(d(!0),p(V,null,E(n.value,l=>{var x,D;return d(),p("tr",{key:l.id},[e("td",he,[e("div",null,[e("div",_e,c(l.name),1),e("div",$e,c(l.description),1)])]),e("td",Se,c(((D=(x=l.provider)==null?void 0:x.user)==null?void 0:D.name)||"Unknown"),1),e("td",Ce,c(l.category||"Uncategorized"),1),e("td",Ue," $"+c(l.price),1),e("td",Ve,c(l.duration)+" min ",1),e("td",Ee,[e("span",{class:I(["px-2 inline-flex text-xs leading-5 font-semibold rounded-full",l.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},c(l.active?"Active":"Inactive"),3)]),e("td",Ae,[e("button",{onClick:L=>N(l),class:"text-indigo-600 hover:text-indigo-900 mr-3"}," Edit ",8,Me),e("button",{onClick:L=>z(l),class:"text-red-600 hover:text-red-900"}," Delete ",8,Pe)])])}),128))])])]),n.value.length===0?(d(),p("div",De,r[8]||(r[8]=[e("div",{class:"text-gray-500"},"No services found.",-1)]))):A("",!0)])])])]),m.value||s.value?(d(),j(de,{key:0,service:k.value,providers:v.value,onClose:P,onSaved:B},null,8,["service","providers"])):A("",!0)]),_:1}))}};export{Le as default};
