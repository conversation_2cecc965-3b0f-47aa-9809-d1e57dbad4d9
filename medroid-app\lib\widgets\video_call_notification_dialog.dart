import 'package:flutter/material.dart';
import '../screens/agora_video_consultation_screen.dart';

class VideoCallNotificationDialog extends StatelessWidget {
  final Map<String, dynamic> callData;

  const VideoCallNotificationDialog({
    Key? key,
    required this.callData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final providerName = callData['provider_name'] ?? 'Doctor';
    final sessionId = callData['session_id'] ?? '';
    final channelName = callData['channel_name'] ?? '';
    final appointmentId = callData['appointment_id'] ?? '';

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Provider avatar and calling indicator
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.blue.shade100,
                border: Border.all(
                  color: Colors.blue,
                  width: 3,
                ),
              ),
              child: const Icon(
                Icons.person,
                size: 40,
                color: Colors.blue,
              ),
            ),

            const SizedBox(height: 16),

            // Calling text
            Text(
              'Incoming Video Call',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),

            const SizedBox(height: 8),

            // Provider name
            Text(
              providerName,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black54,
              ),
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Decline button
                GestureDetector(
                  onTap: () => _declineCall(context),
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.red,
                    ),
                    child: const Icon(
                      Icons.call_end,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),

                // Accept button
                GestureDetector(
                  onTap: () => _acceptCall(
                      context, sessionId, channelName, appointmentId),
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.green,
                    ),
                    child: const Icon(
                      Icons.videocam,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _declineCall(BuildContext context) {
    // Close the dialog
    Navigator.of(context).pop();

    // Optionally send decline notification to backend
    _sendCallResponse('declined');
  }

  void _acceptCall(BuildContext context, String sessionId, String channelName,
      String appointmentId) {
    // Close the dialog
    Navigator.of(context).pop();

    // Send accept notification to backend
    _sendCallResponse('accepted');

    // Navigate to video call screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AgoraVideoConsultationScreen(
          appointmentId: appointmentId.toString(),
          isProvider: false, // Patient is joining the call
          userName: 'Patient', // We can get this from user data if needed
        ),
      ),
    );
  }

  void _sendCallResponse(String response) {
    // Send response to backend (optional)
    // This can be used to notify the provider about patient's response
    debugPrint('Call response: $response');
  }
}
