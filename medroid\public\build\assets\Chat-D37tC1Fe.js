import{E as Ut,r as O,a as Z,w as De,C as X,d as P,n as te,e as N,i as s,j as Ie,t as Y,F as ae,p as de,A as W,l as nt,v as jt,x as $t,I as zt,o as Vt,H as Ht,f as me,u as Ot,m as Kt,g as Yt,q as st}from"./vendor-BK2qhpQJ.js";import{M as Re,_ as Jt}from"./AppLayout.vue_vue_type_script_setup_true_lang-C_QE6t0i.js";import{C as Qt}from"./ChatInput-uvF_bxz7.js";import{_ as Gt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-BOKwGa1V.js";import"./createLucideIcon-Cyf5BO7D.js";var we={},Be,at;function Wt(){return at||(at=1,Be=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),Be}var $e={},ue={},it;function he(){if(it)return ue;it=1;let r;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return ue.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17},ue.getSymbolTotalCodewords=function(t){return o[t]},ue.getBCHDigit=function(a){let t=0;for(;a!==0;)t++,a>>>=1;return t},ue.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');r=t},ue.isKanjiModeEnabled=function(){return typeof r<"u"},ue.toSJIS=function(t){return r(t)},ue}var Pe={},lt;function ot(){return lt||(lt=1,function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function o(a){if(typeof a!="string")throw new Error("Param is not a string");switch(a.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+a)}}r.isValid=function(t){return t&&typeof t.bit<"u"&&t.bit>=0&&t.bit<4},r.from=function(t,e){if(r.isValid(t))return t;try{return o(t)}catch{return e}}}(Pe)),Pe}var Ne,ct;function Xt(){if(ct)return Ne;ct=1;function r(){this.buffer=[],this.length=0}return r.prototype={get:function(o){const a=Math.floor(o/8);return(this.buffer[a]>>>7-o%8&1)===1},put:function(o,a){for(let t=0;t<a;t++)this.putBit((o>>>a-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const a=Math.floor(this.length/8);this.buffer.length<=a&&this.buffer.push(0),o&&(this.buffer[a]|=128>>>this.length%8),this.length++}},Ne=r,Ne}var Le,ut;function Zt(){if(ut)return Le;ut=1;function r(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return r.prototype.set=function(o,a,t,e){const n=o*this.size+a;this.data[n]=t,e&&(this.reservedBit[n]=!0)},r.prototype.get=function(o,a){return this.data[o*this.size+a]},r.prototype.xor=function(o,a,t){this.data[o*this.size+a]^=t},r.prototype.isReserved=function(o,a){return this.reservedBit[o*this.size+a]},Le=r,Le}var qe={},dt;function en(){return dt||(dt=1,function(r){const o=he().getSymbolSize;r.getRowColCoords=function(t){if(t===1)return[];const e=Math.floor(t/7)+2,n=o(t),i=n===145?26:Math.ceil((n-13)/(2*e-2))*2,l=[n-7];for(let c=1;c<e-1;c++)l[c]=l[c-1]-i;return l.push(6),l.reverse()},r.getPositions=function(t){const e=[],n=r.getRowColCoords(t),i=n.length;for(let l=0;l<i;l++)for(let c=0;c<i;c++)l===0&&c===0||l===0&&c===i-1||l===i-1&&c===0||e.push([n[l],n[c]]);return e}}(qe)),qe}var Fe={},ft;function tn(){if(ft)return Fe;ft=1;const r=he().getSymbolSize,o=7;return Fe.getPositions=function(t){const e=r(t);return[[0,0],[e-o,0],[0,e-o]]},Fe}var Ue={},gt;function nn(){return gt||(gt=1,function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};r.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},r.from=function(e){return r.isValid(e)?parseInt(e,10):void 0},r.getPenaltyN1=function(e){const n=e.size;let i=0,l=0,c=0,p=null,w=null;for(let T=0;T<n;T++){l=c=0,p=w=null;for(let _=0;_<n;_++){let h=e.get(T,_);h===p?l++:(l>=5&&(i+=o.N1+(l-5)),p=h,l=1),h=e.get(_,T),h===w?c++:(c>=5&&(i+=o.N1+(c-5)),w=h,c=1)}l>=5&&(i+=o.N1+(l-5)),c>=5&&(i+=o.N1+(c-5))}return i},r.getPenaltyN2=function(e){const n=e.size;let i=0;for(let l=0;l<n-1;l++)for(let c=0;c<n-1;c++){const p=e.get(l,c)+e.get(l,c+1)+e.get(l+1,c)+e.get(l+1,c+1);(p===4||p===0)&&i++}return i*o.N2},r.getPenaltyN3=function(e){const n=e.size;let i=0,l=0,c=0;for(let p=0;p<n;p++){l=c=0;for(let w=0;w<n;w++)l=l<<1&2047|e.get(p,w),w>=10&&(l===1488||l===93)&&i++,c=c<<1&2047|e.get(w,p),w>=10&&(c===1488||c===93)&&i++}return i*o.N3},r.getPenaltyN4=function(e){let n=0;const i=e.data.length;for(let c=0;c<i;c++)n+=e.data[c];return Math.abs(Math.ceil(n*100/i/5)-10)*o.N4};function a(t,e,n){switch(t){case r.Patterns.PATTERN000:return(e+n)%2===0;case r.Patterns.PATTERN001:return e%2===0;case r.Patterns.PATTERN010:return n%3===0;case r.Patterns.PATTERN011:return(e+n)%3===0;case r.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2===0;case r.Patterns.PATTERN101:return e*n%2+e*n%3===0;case r.Patterns.PATTERN110:return(e*n%2+e*n%3)%2===0;case r.Patterns.PATTERN111:return(e*n%3+(e+n)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}r.applyMask=function(e,n){const i=n.size;for(let l=0;l<i;l++)for(let c=0;c<i;c++)n.isReserved(c,l)||n.xor(c,l,a(e,c,l))},r.getBestMask=function(e,n){const i=Object.keys(r.Patterns).length;let l=0,c=1/0;for(let p=0;p<i;p++){n(p),r.applyMask(p,e);const w=r.getPenaltyN1(e)+r.getPenaltyN2(e)+r.getPenaltyN3(e)+r.getPenaltyN4(e);r.applyMask(p,e),w<c&&(c=w,l=p)}return l}}(Ue)),Ue}var Se={},mt;function Pt(){if(mt)return Se;mt=1;const r=ot(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],a=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return Se.getBlocksCount=function(e,n){switch(n){case r.L:return o[(e-1)*4+0];case r.M:return o[(e-1)*4+1];case r.Q:return o[(e-1)*4+2];case r.H:return o[(e-1)*4+3];default:return}},Se.getTotalCodewordsCount=function(e,n){switch(n){case r.L:return a[(e-1)*4+0];case r.M:return a[(e-1)*4+1];case r.Q:return a[(e-1)*4+2];case r.H:return a[(e-1)*4+3];default:return}},Se}var je={},Ce={},ht;function on(){if(ht)return Ce;ht=1;const r=new Uint8Array(512),o=new Uint8Array(256);return function(){let t=1;for(let e=0;e<255;e++)r[e]=t,o[t]=e,t<<=1,t&256&&(t^=285);for(let e=255;e<512;e++)r[e]=r[e-255]}(),Ce.log=function(t){if(t<1)throw new Error("log("+t+")");return o[t]},Ce.exp=function(t){return r[t]},Ce.mul=function(t,e){return t===0||e===0?0:r[o[t]+o[e]]},Ce}var pt;function rn(){return pt||(pt=1,function(r){const o=on();r.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let i=0;i<t.length;i++)for(let l=0;l<e.length;l++)n[i+l]^=o.mul(t[i],e[l]);return n},r.mod=function(t,e){let n=new Uint8Array(t);for(;n.length-e.length>=0;){const i=n[0];for(let c=0;c<e.length;c++)n[c]^=o.mul(e[c],i);let l=0;for(;l<n.length&&n[l]===0;)l++;n=n.slice(l)}return n},r.generateECPolynomial=function(t){let e=new Uint8Array([1]);for(let n=0;n<t;n++)e=r.mul(e,new Uint8Array([1,o.exp(n)]));return e}}(je)),je}var ze,vt;function sn(){if(vt)return ze;vt=1;const r=rn();function o(a){this.genPoly=void 0,this.degree=a,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(t){this.degree=t,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=r.mod(e,this.genPoly),i=this.degree-n.length;if(i>0){const l=new Uint8Array(this.degree);return l.set(n,i),l}return n},ze=o,ze}var Ve={},He={},Oe={},yt;function Nt(){return yt||(yt=1,Oe.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),Oe}var re={},wt;function Lt(){if(wt)return re;wt=1;const r="[0-9]+",o="[A-Z $%*+\\-./:]+";let a="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";a=a.replace(/u/g,"\\u");const t="(?:(?![A-Z0-9 $%*+\\-./:]|"+a+`)(?:.|[\r
]))+`;re.KANJI=new RegExp(a,"g"),re.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),re.BYTE=new RegExp(t,"g"),re.NUMERIC=new RegExp(r,"g"),re.ALPHANUMERIC=new RegExp(o,"g");const e=new RegExp("^"+a+"$"),n=new RegExp("^"+r+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return re.testKanji=function(c){return e.test(c)},re.testNumeric=function(c){return n.test(c)},re.testAlphanumeric=function(c){return i.test(c)},re}var bt;function pe(){return bt||(bt=1,function(r){const o=Nt(),a=Lt();r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(n,i){if(!n.ccBits)throw new Error("Invalid mode: "+n);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?n.ccBits[0]:i<27?n.ccBits[1]:n.ccBits[2]},r.getBestModeForData=function(n){return a.testNumeric(n)?r.NUMERIC:a.testAlphanumeric(n)?r.ALPHANUMERIC:a.testKanji(n)?r.KANJI:r.BYTE},r.toString=function(n){if(n&&n.id)return n.id;throw new Error("Invalid mode")},r.isValid=function(n){return n&&n.bit&&n.ccBits};function t(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+e)}}r.from=function(n,i){if(r.isValid(n))return n;try{return t(n)}catch{return i}}}(He)),He}var xt;function an(){return xt||(xt=1,function(r){const o=he(),a=Pt(),t=ot(),e=pe(),n=Nt(),i=7973,l=o.getBCHDigit(i);function c(_,h,b){for(let v=1;v<=40;v++)if(h<=r.getCapacity(v,b,_))return v}function p(_,h){return e.getCharCountIndicator(_,h)+4}function w(_,h){let b=0;return _.forEach(function(v){const H=p(v.mode,h);b+=H+v.getBitsLength()}),b}function T(_,h){for(let b=1;b<=40;b++)if(w(_,b)<=r.getCapacity(b,h,e.MIXED))return b}r.from=function(h,b){return n.isValid(h)?parseInt(h,10):b},r.getCapacity=function(h,b,v){if(!n.isValid(h))throw new Error("Invalid QR Code version");typeof v>"u"&&(v=e.BYTE);const H=o.getSymbolTotalCodewords(h),R=a.getTotalCodewordsCount(h,b),F=(H-R)*8;if(v===e.MIXED)return F;const k=F-p(v,h);switch(v){case e.NUMERIC:return Math.floor(k/10*3);case e.ALPHANUMERIC:return Math.floor(k/11*2);case e.KANJI:return Math.floor(k/13);case e.BYTE:default:return Math.floor(k/8)}},r.getBestVersionForData=function(h,b){let v;const H=t.from(b,t.M);if(Array.isArray(h)){if(h.length>1)return T(h,H);if(h.length===0)return 1;v=h[0]}else v=h;return c(v.mode,v.getLength(),H)},r.getEncodedBits=function(h){if(!n.isValid(h)||h<7)throw new Error("Invalid QR Code version");let b=h<<12;for(;o.getBCHDigit(b)-l>=0;)b^=i<<o.getBCHDigit(b)-l;return h<<12|b}}(Ve)),Ve}var Ke={},_t;function ln(){if(_t)return Ke;_t=1;const r=he(),o=1335,a=21522,t=r.getBCHDigit(o);return Ke.getEncodedBits=function(n,i){const l=n.bit<<3|i;let c=l<<10;for(;r.getBCHDigit(c)-t>=0;)c^=o<<r.getBCHDigit(c)-t;return(l<<10|c)^a},Ke}var Ye={},Je,Ct;function cn(){if(Ct)return Je;Ct=1;const r=pe();function o(a){this.mode=r.NUMERIC,this.data=a.toString()}return o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e,n,i;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),i=parseInt(n,10),t.put(i,10);const l=this.data.length-e;l>0&&(n=this.data.substr(e),i=parseInt(n,10),t.put(i,l*3+1))},Je=o,Je}var Qe,kt;function un(){if(kt)return Qe;kt=1;const r=pe(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function a(t){this.mode=r.ALPHANUMERIC,this.data=t}return a.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},a.prototype.getLength=function(){return this.data.length},a.prototype.getBitsLength=function(){return a.getBitsLength(this.data.length)},a.prototype.write=function(e){let n;for(n=0;n+2<=this.data.length;n+=2){let i=o.indexOf(this.data[n])*45;i+=o.indexOf(this.data[n+1]),e.put(i,11)}this.data.length%2&&e.put(o.indexOf(this.data[n]),6)},Qe=a,Qe}var Ge,Et;function dn(){if(Et)return Ge;Et=1;const r=pe();function o(a){this.mode=r.BYTE,typeof a=="string"?this.data=new TextEncoder().encode(a):this.data=new Uint8Array(a)}return o.getBitsLength=function(t){return t*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(a){for(let t=0,e=this.data.length;t<e;t++)a.put(this.data[t],8)},Ge=o,Ge}var We,St;function fn(){if(St)return We;St=1;const r=pe(),o=he();function a(t){this.mode=r.KANJI,this.data=t}return a.getBitsLength=function(e){return e*13},a.prototype.getLength=function(){return this.data.length},a.prototype.getBitsLength=function(){return a.getBitsLength(this.data.length)},a.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=o.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);n=(n>>>8&255)*192+(n&255),t.put(n,13)}},We=a,We}var Xe={exports:{}},Tt;function gn(){return Tt||(Tt=1,function(r){var o={single_source_shortest_paths:function(a,t,e){var n={},i={};i[t]=0;var l=o.PriorityQueue.make();l.push(t,0);for(var c,p,w,T,_,h,b,v,H;!l.empty();){c=l.pop(),p=c.value,T=c.cost,_=a[p]||{};for(w in _)_.hasOwnProperty(w)&&(h=_[w],b=T+h,v=i[w],H=typeof i[w]>"u",(H||v>b)&&(i[w]=b,l.push(w,b),n[w]=p))}if(typeof e<"u"&&typeof i[e]>"u"){var R=["Could not find a path from ",t," to ",e,"."].join("");throw new Error(R)}return n},extract_shortest_path_from_predecessor_list:function(a,t){for(var e=[],n=t;n;)e.push(n),a[n],n=a[n];return e.reverse(),e},find_path:function(a,t,e){var n=o.single_source_shortest_paths(a,t,e);return o.extract_shortest_path_from_predecessor_list(n,e)},PriorityQueue:{make:function(a){var t=o.PriorityQueue,e={},n;a=a||{};for(n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e.queue=[],e.sorter=a.sorter||t.default_sorter,e},default_sorter:function(a,t){return a.cost-t.cost},push:function(a,t){var e={value:a,cost:t};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=o}(Xe)),Xe.exports}var Mt;function mn(){return Mt||(Mt=1,function(r){const o=pe(),a=cn(),t=un(),e=dn(),n=fn(),i=Lt(),l=he(),c=gn();function p(R){return unescape(encodeURIComponent(R)).length}function w(R,F,k){const M=[];let J;for(;(J=R.exec(k))!==null;)M.push({data:J[0],index:J.index,mode:F,length:J[0].length});return M}function T(R){const F=w(i.NUMERIC,o.NUMERIC,R),k=w(i.ALPHANUMERIC,o.ALPHANUMERIC,R);let M,J;return l.isKanjiModeEnabled()?(M=w(i.BYTE,o.BYTE,R),J=w(i.KANJI,o.KANJI,R)):(M=w(i.BYTE_KANJI,o.BYTE,R),J=[]),F.concat(k,M,J).sort(function(D,A){return D.index-A.index}).map(function(D){return{data:D.data,mode:D.mode,length:D.length}})}function _(R,F){switch(F){case o.NUMERIC:return a.getBitsLength(R);case o.ALPHANUMERIC:return t.getBitsLength(R);case o.KANJI:return n.getBitsLength(R);case o.BYTE:return e.getBitsLength(R)}}function h(R){return R.reduce(function(F,k){const M=F.length-1>=0?F[F.length-1]:null;return M&&M.mode===k.mode?(F[F.length-1].data+=k.data,F):(F.push(k),F)},[])}function b(R){const F=[];for(let k=0;k<R.length;k++){const M=R[k];switch(M.mode){case o.NUMERIC:F.push([M,{data:M.data,mode:o.ALPHANUMERIC,length:M.length},{data:M.data,mode:o.BYTE,length:M.length}]);break;case o.ALPHANUMERIC:F.push([M,{data:M.data,mode:o.BYTE,length:M.length}]);break;case o.KANJI:F.push([M,{data:M.data,mode:o.BYTE,length:p(M.data)}]);break;case o.BYTE:F.push([{data:M.data,mode:o.BYTE,length:p(M.data)}])}}return F}function v(R,F){const k={},M={start:{}};let J=["start"];for(let E=0;E<R.length;E++){const D=R[E],A=[];for(let x=0;x<D.length;x++){const f=D[x],u=""+E+x;A.push(u),k[u]={node:f,lastCount:0},M[u]={};for(let g=0;g<J.length;g++){const S=J[g];k[S]&&k[S].node.mode===f.mode?(M[S][u]=_(k[S].lastCount+f.length,f.mode)-_(k[S].lastCount,f.mode),k[S].lastCount+=f.length):(k[S]&&(k[S].lastCount=f.length),M[S][u]=_(f.length,f.mode)+4+o.getCharCountIndicator(f.mode,F))}}J=A}for(let E=0;E<J.length;E++)M[J[E]].end=0;return{map:M,table:k}}function H(R,F){let k;const M=o.getBestModeForData(R);if(k=o.from(F,M),k!==o.BYTE&&k.bit<M.bit)throw new Error('"'+R+'" cannot be encoded with mode '+o.toString(k)+`.
 Suggested mode is: `+o.toString(M));switch(k===o.KANJI&&!l.isKanjiModeEnabled()&&(k=o.BYTE),k){case o.NUMERIC:return new a(R);case o.ALPHANUMERIC:return new t(R);case o.KANJI:return new n(R);case o.BYTE:return new e(R)}}r.fromArray=function(F){return F.reduce(function(k,M){return typeof M=="string"?k.push(H(M,null)):M.data&&k.push(H(M.data,M.mode)),k},[])},r.fromString=function(F,k){const M=T(F,l.isKanjiModeEnabled()),J=b(M),E=v(J,k),D=c.find_path(E.map,"start","end"),A=[];for(let x=1;x<D.length-1;x++)A.push(E.table[D[x]].node);return r.fromArray(h(A))},r.rawSplit=function(F){return r.fromArray(T(F,l.isKanjiModeEnabled()))}}(Ye)),Ye}var At;function hn(){if(At)return $e;At=1;const r=he(),o=ot(),a=Xt(),t=Zt(),e=en(),n=tn(),i=nn(),l=Pt(),c=sn(),p=an(),w=ln(),T=pe(),_=mn();function h(E,D){const A=E.size,x=n.getPositions(D);for(let f=0;f<x.length;f++){const u=x[f][0],g=x[f][1];for(let S=-1;S<=7;S++)if(!(u+S<=-1||A<=u+S))for(let L=-1;L<=7;L++)g+L<=-1||A<=g+L||(S>=0&&S<=6&&(L===0||L===6)||L>=0&&L<=6&&(S===0||S===6)||S>=2&&S<=4&&L>=2&&L<=4?E.set(u+S,g+L,!0,!0):E.set(u+S,g+L,!1,!0))}}function b(E){const D=E.size;for(let A=8;A<D-8;A++){const x=A%2===0;E.set(A,6,x,!0),E.set(6,A,x,!0)}}function v(E,D){const A=e.getPositions(D);for(let x=0;x<A.length;x++){const f=A[x][0],u=A[x][1];for(let g=-2;g<=2;g++)for(let S=-2;S<=2;S++)g===-2||g===2||S===-2||S===2||g===0&&S===0?E.set(f+g,u+S,!0,!0):E.set(f+g,u+S,!1,!0)}}function H(E,D){const A=E.size,x=p.getEncodedBits(D);let f,u,g;for(let S=0;S<18;S++)f=Math.floor(S/3),u=S%3+A-8-3,g=(x>>S&1)===1,E.set(f,u,g,!0),E.set(u,f,g,!0)}function R(E,D,A){const x=E.size,f=w.getEncodedBits(D,A);let u,g;for(u=0;u<15;u++)g=(f>>u&1)===1,u<6?E.set(u,8,g,!0):u<8?E.set(u+1,8,g,!0):E.set(x-15+u,8,g,!0),u<8?E.set(8,x-u-1,g,!0):u<9?E.set(8,15-u-1+1,g,!0):E.set(8,15-u-1,g,!0);E.set(x-8,8,1,!0)}function F(E,D){const A=E.size;let x=-1,f=A-1,u=7,g=0;for(let S=A-1;S>0;S-=2)for(S===6&&S--;;){for(let L=0;L<2;L++)if(!E.isReserved(f,S-L)){let ne=!1;g<D.length&&(ne=(D[g]>>>u&1)===1),E.set(f,S-L,ne),u--,u===-1&&(g++,u=7)}if(f+=x,f<0||A<=f){f-=x,x=-x;break}}}function k(E,D,A){const x=new a;A.forEach(function(L){x.put(L.mode.bit,4),x.put(L.getLength(),T.getCharCountIndicator(L.mode,E)),L.write(x)});const f=r.getSymbolTotalCodewords(E),u=l.getTotalCodewordsCount(E,D),g=(f-u)*8;for(x.getLengthInBits()+4<=g&&x.put(0,4);x.getLengthInBits()%8!==0;)x.putBit(0);const S=(g-x.getLengthInBits())/8;for(let L=0;L<S;L++)x.put(L%2?17:236,8);return M(x,E,D)}function M(E,D,A){const x=r.getSymbolTotalCodewords(D),f=l.getTotalCodewordsCount(D,A),u=x-f,g=l.getBlocksCount(D,A),S=x%g,L=g-S,ne=Math.floor(x/g),fe=Math.floor(u/g),Te=fe+1,ee=ne-fe,be=new c(ee);let xe=0;const ve=new Array(g),ke=new Array(g);let ie=0;const Me=new Uint8Array(E.buffer);for(let m=0;m<g;m++){const C=m<L?fe:Te;ve[m]=Me.slice(xe,xe+C),ke[m]=be.encode(ve[m]),xe+=C,ie=Math.max(ie,C)}const ye=new Uint8Array(x);let Ee=0,y,d;for(y=0;y<ie;y++)for(d=0;d<g;d++)y<ve[d].length&&(ye[Ee++]=ve[d][y]);for(y=0;y<ee;y++)for(d=0;d<g;d++)ye[Ee++]=ke[d][y];return ye}function J(E,D,A,x){let f;if(Array.isArray(E))f=_.fromArray(E);else if(typeof E=="string"){let ne=D;if(!ne){const fe=_.rawSplit(E);ne=p.getBestVersionForData(fe,A)}f=_.fromString(E,ne||40)}else throw new Error("Invalid data");const u=p.getBestVersionForData(f,A);if(!u)throw new Error("The amount of data is too big to be stored in a QR Code");if(!D)D=u;else if(D<u)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+u+`.
`);const g=k(D,A,f),S=r.getSymbolSize(D),L=new t(S);return h(L,D),b(L),v(L,D),R(L,A,0),D>=7&&H(L,D),F(L,g),isNaN(x)&&(x=i.getBestMask(L,R.bind(null,L,A))),i.applyMask(x,L),R(L,A,x),{modules:L,version:D,errorCorrectionLevel:A,maskPattern:x,segments:f}}return $e.create=function(D,A){if(typeof D>"u"||D==="")throw new Error("No input text");let x=o.M,f,u;return typeof A<"u"&&(x=o.from(A.errorCorrectionLevel,o.M),f=p.from(A.version),u=i.from(A.maskPattern),A.toSJISFunc&&r.setToSJISFunction(A.toSJISFunc)),J(D,f,x,u)},$e}var Ze={},et={},Dt;function qt(){return Dt||(Dt=1,function(r){function o(a){if(typeof a=="number"&&(a=a.toString()),typeof a!="string")throw new Error("Color should be defined as hex string");let t=a.slice().replace("#","").split("");if(t.length<3||t.length===5||t.length>8)throw new Error("Invalid hex color: "+a);(t.length===3||t.length===4)&&(t=Array.prototype.concat.apply([],t.map(function(n){return[n,n]}))),t.length===6&&t.push("F","F");const e=parseInt(t.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+t.slice(0,6).join("")}}r.getOptions=function(t){t||(t={}),t.color||(t.color={});const e=typeof t.margin>"u"||t.margin===null||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:n,scale:n?4:i,margin:e,color:{dark:o(t.color.dark||"#000000ff"),light:o(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},r.getScale=function(t,e){return e.width&&e.width>=t+e.margin*2?e.width/(t+e.margin*2):e.scale},r.getImageWidth=function(t,e){const n=r.getScale(t,e);return Math.floor((t+e.margin*2)*n)},r.qrToImageData=function(t,e,n){const i=e.modules.size,l=e.modules.data,c=r.getScale(i,n),p=Math.floor((i+n.margin*2)*c),w=n.margin*c,T=[n.color.light,n.color.dark];for(let _=0;_<p;_++)for(let h=0;h<p;h++){let b=(_*p+h)*4,v=n.color.light;if(_>=w&&h>=w&&_<p-w&&h<p-w){const H=Math.floor((_-w)/c),R=Math.floor((h-w)/c);v=T[l[H*i+R]?1:0]}t[b++]=v.r,t[b++]=v.g,t[b++]=v.b,t[b]=v.a}}}(et)),et}var It;function pn(){return It||(It=1,function(r){const o=qt();function a(e,n,i){e.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=i,n.width=i,n.style.height=i+"px",n.style.width=i+"px"}function t(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}r.render=function(n,i,l){let c=l,p=i;typeof c>"u"&&(!i||!i.getContext)&&(c=i,i=void 0),i||(p=t()),c=o.getOptions(c);const w=o.getImageWidth(n.modules.size,c),T=p.getContext("2d"),_=T.createImageData(w,w);return o.qrToImageData(_.data,n,c),a(T,p,w),T.putImageData(_,0,0),p},r.renderToDataURL=function(n,i,l){let c=l;typeof c>"u"&&(!i||!i.getContext)&&(c=i,i=void 0),c||(c={});const p=r.render(n,i,c),w=c.type||"image/png",T=c.rendererOpts||{};return p.toDataURL(w,T.quality)}}(Ze)),Ze}var tt={},Rt;function vn(){if(Rt)return tt;Rt=1;const r=qt();function o(e,n){const i=e.a/255,l=n+'="'+e.hex+'"';return i<1?l+" "+n+'-opacity="'+i.toFixed(2).slice(1)+'"':l}function a(e,n,i){let l=e+n;return typeof i<"u"&&(l+=" "+i),l}function t(e,n,i){let l="",c=0,p=!1,w=0;for(let T=0;T<e.length;T++){const _=Math.floor(T%n),h=Math.floor(T/n);!_&&!p&&(p=!0),e[T]?(w++,T>0&&_>0&&e[T-1]||(l+=p?a("M",_+i,.5+h+i):a("m",c,0),c=0,p=!1),_+1<n&&e[T+1]||(l+=a("h",w),w=0)):c++}return l}return tt.render=function(n,i,l){const c=r.getOptions(i),p=n.modules.size,w=n.modules.data,T=p+c.margin*2,_=c.color.light.a?"<path "+o(c.color.light,"fill")+' d="M0 0h'+T+"v"+T+'H0z"/>':"",h="<path "+o(c.color.dark,"stroke")+' d="'+t(w,p,c.margin)+'"/>',b='viewBox="0 0 '+T+" "+T+'"',H='<svg xmlns="http://www.w3.org/2000/svg" '+(c.width?'width="'+c.width+'" height="'+c.width+'" ':"")+b+' shape-rendering="crispEdges">'+_+h+`</svg>
`;return typeof l=="function"&&l(null,H),H},tt}var Bt;function yn(){if(Bt)return we;Bt=1;const r=Wt(),o=hn(),a=pn(),t=vn();function e(n,i,l,c,p){const w=[].slice.call(arguments,1),T=w.length,_=typeof w[T-1]=="function";if(!_&&!r())throw new Error("Callback required as last argument");if(_){if(T<2)throw new Error("Too few arguments provided");T===2?(p=l,l=i,i=c=void 0):T===3&&(i.getContext&&typeof p>"u"?(p=c,c=void 0):(p=c,c=l,l=i,i=void 0))}else{if(T<1)throw new Error("Too few arguments provided");return T===1?(l=i,i=c=void 0):T===2&&!i.getContext&&(c=l,l=i,i=void 0),new Promise(function(h,b){try{const v=o.create(l,c);h(n(v,i,c))}catch(v){b(v)}})}try{const h=o.create(l,c);p(null,n(h,i,c))}catch(h){p(h)}}return we.create=o.create,we.toCanvas=e.bind(null,a.render),we.toDataURL=e.bind(null,a.renderToDataURL),we.toString=e.bind(null,function(n,i,l){return t.render(n,l)}),we}var wn=yn();const bn=Ut(wn);function xn(r={}){const o=O(null),a=O(!1),t=O(null),{showErrorAlert:e=!1,retryOnCsrfError:n=!0}=r,i=async h=>{a.value=!0,t.value=null;try{const b=await Z.get(h);return o.value=b.data,b.data}catch(b){const v=w(b);return t.value=v,e&&alert(v),null}finally{a.value=!1}},l=async(h,b={})=>{a.value=!0,t.value=null;try{const v=await Z.post(h,b);return o.value=v.data,v.data}catch(v){const H=w(v);return t.value=H,e&&!h.includes("/logout")&&alert(H),h.includes("/logout")?{message:"Logged out successfully"}:null}finally{a.value=!1}},c=async(h,b={})=>{a.value=!0,t.value=null;try{const v=await Z.put(h,b);return o.value=v.data,v.data}catch(v){const H=w(v);return t.value=H,e&&alert(H),null}finally{a.value=!1}},p=async h=>{a.value=!0,t.value=null;try{return await Z.delete(h),!0}catch(b){const v=w(b);return t.value=v,e&&alert(v),!1}finally{a.value=!1}},w=h=>{if(console.error("API Error:",h),h.response){const b=h.response.status,v=h.response.data;switch(b){case 401:return"Authentication required. Please log in again.";case 403:return"You do not have permission to perform this action.";case 404:return"The requested resource was not found.";case 419:return"Security token expired. Please refresh the page and try again.";case 422:if(v!=null&&v.errors){const H=Object.values(v.errors)[0];return Array.isArray(H)?H[0]:String(H)}return(v==null?void 0:v.message)||"Validation failed.";case 429:return"Too many requests. Please wait a moment and try again.";case 500:return"Server error. Please try again later.";default:return(v==null?void 0:v.message)||`Server error: ${b}`}}else return h.request?"Network error. Please check your connection and try again.":"An unexpected error occurred. Please try again."};return{data:o,loading:a,error:t,get:i,post:l,put:c,delete:p,reset:()=>{o.value=null,a.value=!1,t.value=null},clearError:()=>{t.value=null},handleError:w}}function _n(){const r=xn();return{...r,getReferralCode:()=>r.get("/referrals-code"),getReferralStats:()=>r.get("/referrals-my"),sendInvitation:n=>r.post("/send-referral-invite",{email:n}),getCreditBalance:()=>r.get("/credits-balance")}}const Cn={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},kn={class:"flex items-center justify-between mb-6"},En={class:"flex items-center space-x-3"},Sn={class:"text-sm text-gray-500"},Tn={key:0,class:"text-center py-8"},Mn={key:1,class:"space-y-6"},An={class:"flex space-x-1 bg-gray-100 rounded-lg p-1"},Dn=["onClick"],In={key:0,class:"space-y-4"},Rn={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Bn={class:"flex items-center space-x-2"},$n=["value"],Pn=["disabled"],Nn={class:"flex items-center space-x-2"},Ln=["value"],qn=["disabled"],Fn={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-center"},Un={class:"text-center"},jn={class:"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg"},zn={key:1,class:"space-y-4"},Vn=["disabled"],Hn={key:0,class:"flex items-center justify-center"},On={key:1},Kn={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md"},Yn={key:1,class:"p-3 bg-red-100 border border-red-400 text-red-700 rounded-md"},Jn={key:2,class:"space-y-4"},Qn={class:"bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4"},Gn={class:"text-center"},Wn={class:"text-3xl font-bold text-purple-600"},Xn={class:"text-xs text-purple-600 mt-1"},Zn={class:"grid grid-cols-2 gap-4"},eo={class:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-center"},to={class:"text-2xl font-bold text-blue-600"},no={class:"bg-green-50 border border-green-200 rounded-lg p-4 text-center"},oo={class:"text-2xl font-bold text-green-600"},ro={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center"},so={class:"text-2xl font-bold text-yellow-600"},ao={key:0},io={class:"space-y-3"},lo={class:"flex items-center justify-between mb-2"},co={class:"flex-1"},uo={class:"text-sm font-medium text-gray-900"},fo={class:"text-xs text-gray-500"},go={class:"flex items-center justify-between text-xs"},mo={class:"text-gray-500"},ho={key:0,class:"text-green-600"},po={key:1,class:"text-center py-8"},vo={__name:"ReferralModal",props:{isOpen:{type:Boolean,default:!1}},emits:["close"],setup(r,{emit:o}){const a=r,t=o,e=_n(),n=O(!1),i=O("share"),l=O({}),c=O({}),p=O({balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}),w=O(""),T=O(!1),_=O(!1),h=O(""),b=O(""),v=O(null),H=[{id:"share",name:"Share"},{id:"invite",name:"Invite"},{id:"stats",name:"Stats"}],R=()=>{t("close")},F=async()=>{try{const f=await Z.get("/credits-balance");p.value=f.data}catch(f){console.error("Error loading credit balance:",f),p.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}}},k=async()=>{n.value=!0;try{console.log("Loading referral data..."),await F(),l.value={referral_code:"DEMO123",referral_url:"https://medroid.ai/register?ref=DEMO123"},c.value={total_count:5,completed_count:3,pending_count:2,total_earnings:15,referrals:[{id:1,email:"<EMAIL>",status:"completed",created_at:new Date().toISOString(),earnings:3},{id:2,email:"<EMAIL>",status:"pending",created_at:new Date().toISOString(),earnings:0}]};try{const f=await Z.get("/referrals-code");console.log("Referral code response:",f.data),f.data&&(l.value=f.data);const u=await Z.get("/referrals-my");console.log("Referral stats response:",u.data),u.data&&(c.value=u.data)}catch(f){console.log("API call failed, using demo data:",f.message)}await X(),l.value.referral_url&&(console.log("Calling generateQRCode from loadReferralData"),await M())}catch(f){console.error("Error in loadReferralData:",f)}finally{n.value=!1}},M=async()=>{if(console.log("generateQRCode called",{canvasExists:!!v.value,referralUrl:l.value.referral_url}),!l.value.referral_url){console.log("No referral URL available for QR code generation");return}if(await X(),!v.value){console.log("Canvas element not available, retrying..."),setTimeout(async()=>{await M()},100);return}try{const f=v.value;console.log("Canvas element found, generating QR code..."),f.getContext("2d").clearRect(0,0,f.width,f.height),f.width=128,f.height=128,await bn.toCanvas(f,l.value.referral_url,{width:128,margin:1,color:{dark:"#000000",light:"#FFFFFF"}}),console.log("QR code generated successfully")}catch(f){console.error("Error generating QR code:",f);const u=v.value;if(u){const g=u.getContext("2d");u.width=128,u.height=128,g.fillStyle="#f3f4f6",g.fillRect(0,0,128,128),g.fillStyle="#6b7280",g.font="12px Arial",g.textAlign="center",g.fillText("QR Code",64,64),g.fillText("Error",64,80)}}},J=async(f,u)=>{if(f)try{if(await navigator.clipboard.writeText(f),u&&u.target){const g=u.target.closest("button");if(g){const S=g.innerHTML;g.innerHTML='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',setTimeout(()=>{g.innerHTML=S},1e3)}}b.value="Copied to clipboard!",setTimeout(()=>{b.value=""},3e3)}catch(g){console.error("Failed to copy:",g);const S=document.createElement("textarea");S.value=f,document.body.appendChild(S),S.select();try{document.execCommand("copy"),b.value="Copied to clipboard!",setTimeout(()=>{b.value=""},3e3)}catch(L){console.error("Fallback copy failed:",L),b.value="Failed to copy",setTimeout(()=>{b.value=""},3e3)}document.body.removeChild(S)}},E=()=>{const f=`🤖 Meet your new AI Doctor!
Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.
Download now with my code: ${l.value.referral_code} 👉 ${l.value.referral_url}
Your pocket-sized doctor is here! 👩‍⚕️📱`,u=`https://wa.me/?text=${encodeURIComponent(f)}`;window.open(u,"_blank")},D=()=>{const f="Meet your new AI Doctor - Medroid",u=`🤖 Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Download now with my code: ${l.value.referral_code} 👉 ${l.value.referral_url}

Your pocket-sized doctor is here! 👩‍⚕️📱

Best regards!`,g=`mailto:?subject=${encodeURIComponent(f)}&body=${encodeURIComponent(u)}`;window.location.href=g},A=async f=>{if(f&&f.preventDefault(),!!w.value){T.value=!0,_.value=!1,h.value="";try{console.log("Sending invitation to:",w.value);const u=await e.sendInvitation(w.value);if(u){console.log("Invitation response:",u),_.value=!0,w.value="";try{const g=await e.getReferralStats();g&&(c.value=g),await F()}catch(g){console.log("Failed to reload stats:",g)}}else h.value=e.error.value||"Failed to send invitation. Please try again."}catch(u){console.error("Unexpected error sending invitation:",u),h.value="An unexpected error occurred. Please try again."}finally{T.value=!1}}},x=f=>{if(!f)return"";try{return new Date(f).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return f}};return De(()=>a.isOpen,async f=>{f&&(k(),setTimeout(async()=>{l.value.referral_url&&v.value&&v.value.getContext("2d").getImageData(0,0,v.value.width,v.value.height).data.every(L=>L===0)&&(console.log("Fallback QR code generation triggered - canvas is empty"),await M())},500))}),De(()=>l.value.referral_url,async f=>{f&&a.isOpen&&(console.log("Referral URL changed, regenerating QR code:",f),await X(),await M())}),De(i,()=>{_.value=!1,h.value="",b.value=""}),(f,u)=>r.isOpen?(N(),P("div",Cn,[s("div",{class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm transition-opacity",onClick:Ie(R,["self"])}),s("div",{class:"flex min-h-full items-center justify-center p-4",onClick:Ie(R,["self"])},[s("div",{class:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",onClick:u[3]||(u[3]=Ie(()=>{},["stop"]))},[s("div",kn,[s("div",En,[u[5]||(u[5]=s("div",{class:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center"},[s("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),s("div",null,[u[4]||(u[4]=s("h3",{class:"text-lg font-semibold text-gray-900"},"Refer & Earn",-1)),s("p",Sn," Total Balance: $"+Y(parseFloat(p.value.balance||0).toFixed(2))+" | From Referrals: $"+Y(parseFloat(p.value.referral_earnings||0).toFixed(2)),1)])]),s("button",{onClick:R,class:"text-gray-400 hover:text-gray-600"},u[6]||(u[6]=[s("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n.value?(N(),P("div",Tn,u[7]||(u[7]=[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"},null,-1),s("p",{class:"text-gray-600"},"Loading your referral details...",-1)]))):(N(),P("div",Mn,[s("div",An,[(N(),P(ae,null,de(H,g=>s("button",{key:g.id,onClick:S=>i.value=g.id,class:W(["flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors",i.value===g.id?"bg-white text-green-600 shadow-sm":"text-gray-600 hover:text-gray-900"])},Y(g.name),11,Dn)),64))]),i.value==="share"?(N(),P("div",In,[s("div",Rn,[u[9]||(u[9]=s("label",{class:"block text-sm font-medium text-green-800 mb-2"},"Your Referral Code",-1)),s("div",Bn,[s("input",{value:l.value.referral_code||"Loading...",readonly:"",class:W(["flex-1 px-3 py-2 bg-white border border-green-300 rounded-md text-lg font-mono text-center",{"text-gray-400":!l.value.referral_code}])},null,10,$n),s("button",{onClick:u[0]||(u[0]=g=>J(l.value.referral_code,g)),disabled:!l.value.referral_code,class:"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy code"},u[8]||(u[8]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,Pn)])]),s("div",null,[u[11]||(u[11]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Referral Link",-1)),s("div",Nn,[s("input",{value:l.value.referral_url||"Loading...",readonly:"",class:W(["flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm",{"text-gray-400":!l.value.referral_url}])},null,10,Ln),s("button",{onClick:u[1]||(u[1]=g=>J(l.value.referral_url,g)),disabled:!l.value.referral_url,class:"px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy link"},u[10]||(u[10]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,qn)])]),b.value?(N(),P("div",Fn,Y(b.value),1)):te("",!0),s("div",Un,[u[12]||(u[12]=s("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"QR Code",-1)),s("div",jn,[s("canvas",{ref_key:"qrCanvas",ref:v,class:"w-32 h-32"},null,512)]),u[13]||(u[13]=s("p",{class:"text-xs text-gray-500 mt-2"},"Share this QR code with friends",-1))]),s("div",{class:"grid grid-cols-2 gap-3"},[s("button",{onClick:E,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"},u[14]||(u[14]=[s("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},[s("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})],-1),s("span",{class:"text-sm font-medium"},"WhatsApp",-1)])),s("button",{onClick:D,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"},u[15]||(u[15]=[s("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1),s("span",{class:"text-sm font-medium"},"Email",-1)]))])])):te("",!0),i.value==="invite"?(N(),P("div",zn,[s("form",{onSubmit:A,class:"space-y-4"},[s("div",null,[u[16]||(u[16]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Friend's Email",-1)),nt(s("input",{"onUpdate:modelValue":u[2]||(u[2]=g=>w.value=g),type:"email",required:"",placeholder:"Enter your friend's email",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"},null,512),[[jt,w.value]])]),s("button",{type:"submit",disabled:T.value||!w.value,class:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[T.value?(N(),P("span",Hn,u[17]||(u[17]=[s("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),$t(" Sending... ")]))):(N(),P("span",On,"Send Invitation"))],8,Vn)],32),_.value?(N(),P("div",Kn," Invitation sent successfully! ")):te("",!0),h.value?(N(),P("div",Yn,Y(h.value),1)):te("",!0)])):te("",!0),i.value==="stats"?(N(),P("div",Jn,[s("div",Qn,[s("div",Gn,[s("div",Wn,"$"+Y(parseFloat(p.value.balance||0).toFixed(2)),1),u[18]||(u[18]=s("div",{class:"text-sm text-purple-800 font-medium"},"Available Credit Balance",-1)),s("div",Xn," Total Earned: $"+Y(parseFloat(p.value.total_earned||0).toFixed(2))+" | Used: $"+Y(parseFloat(p.value.total_used||0).toFixed(2)),1)])]),s("div",Zn,[s("div",eo,[s("div",to,Y(c.value.total_count||0),1),u[19]||(u[19]=s("div",{class:"text-sm text-blue-800"},"Total Referrals",-1))]),s("div",no,[s("div",oo,Y(c.value.completed_count||0),1),u[20]||(u[20]=s("div",{class:"text-sm text-green-800"},"Completed",-1))])]),s("div",ro,[s("div",so,"$"+Y(parseFloat(p.value.referral_earnings||0).toFixed(2)),1),u[21]||(u[21]=s("div",{class:"text-sm text-yellow-800"},"Referral Earnings",-1))]),c.value.referrals&&c.value.referrals.length>0?(N(),P("div",ao,[u[22]||(u[22]=s("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Recent Referrals",-1)),s("div",io,[(N(!0),P(ae,null,de(c.value.referrals.slice(0,5),g=>(N(),P("div",{key:g.id,class:"p-3 bg-gray-50 rounded-lg border"},[s("div",lo,[s("div",co,[s("div",uo,Y(g.email||"Anonymous Referral"),1),s("div",fo,Y(x(g.created_at)),1)]),s("span",{class:W(["text-xs px-2 py-1 rounded-full font-medium",g.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"])},Y(g.status==="completed"?"Completed":"Pending"),3)]),s("div",go,[s("span",mo," Credit: $"+Y(g.credit_amount||3),1),g.status==="completed"&&g.completed_at?(N(),P("span",ho," Completed "+Y(x(g.completed_at)),1)):te("",!0)])]))),128))])])):(N(),P("div",po,u[23]||(u[23]=[zt('<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg></div><h4 class="text-sm font-medium text-gray-900 mb-1">No referrals yet</h4><p class="text-xs text-gray-500">Start sharing your referral code to earn rewards!</p>',3)])))])):te("",!0)]))])])])):te("",!0)}},yo={class:"h-full flex flex-col bg-gray-50 relative overflow-hidden"},wo={class:"absolute top-4 left-4 z-20"},bo={class:"flex items-center space-x-3"},xo={class:"absolute top-4 right-4 z-20"},_o={class:"flex items-center space-x-2"},Co={key:0,class:"ml-2"},ko={key:0,class:"ml-2"},Eo={key:0,class:"flex-1 flex flex-col items-center justify-center px-6 py-8"},So={key:1,class:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 py-8 pt-20 sm:pt-24"},To={class:"text-center mb-8 sm:mb-12"},Mo={class:"mx-auto mb-4 sm:mb-6"},Ao={class:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 mb-8 sm:mb-12 max-w-2xl w-full"},Do=["onClick"],Io=["innerHTML"],Ro={key:2,class:"flex-1 overflow-hidden pt-20 sm:pt-24"},Bo={class:"h-full flex flex-col"},$o={class:"max-w-4xl mx-auto px-3 sm:px-4 space-y-4"},Po={key:0,class:"flex items-start max-w-[85%] sm:max-w-[80%]"},No={class:"flex-shrink-0 mr-3"},Lo={class:"w-8 h-8 flex items-center justify-center"},qo={class:"bg-white text-gray-800 border border-gray-200 rounded-2xl rounded-bl-md message-bubble px-4 py-3 relative max-w-full"},Fo={class:"text-sm leading-relaxed"},Uo={key:0,class:"flex items-center space-x-2"},jo={key:1,class:"prose prose-sm max-w-none"},zo=["innerHTML"],Vo=["innerHTML"],Ho={key:0,class:"mt-3 pt-3 border-t border-gray-200"},Oo={class:"flex items-center justify-between"},Ko={class:"text-xs text-gray-500"},Yo=["href"],Jo={class:"text-xs text-gray-400 mt-2"},Qo={key:1,class:"flex justify-end max-w-[85%] sm:max-w-[80%]"},Go={class:"user-message-bubble text-white rounded-2xl rounded-br-md message-bubble px-4 py-3 relative max-w-full"},Wo={class:"text-sm leading-relaxed break-words whitespace-pre-wrap"},Xo={class:"text-teal-100 mt-2 text-right text-xs"},Zo={key:0,class:"max-w-4xl mx-auto px-3 sm:px-4"},er={class:"flex-1 min-w-0"},tr={class:"mb-4"},nr={class:"text-xs text-gray-500 mb-3"},or={class:"flex flex-wrap gap-2 mb-3"},rr={class:"flex-1 min-w-0"},sr=["value"],ar={class:"flex-1 min-w-0"},ir=["value"],lr={key:0,class:"flex items-center justify-center py-6"},cr={key:1,class:"text-center py-6"},ur={key:2},dr=["onClick"],fr={class:"flex items-center justify-between mb-1"},gr={key:0,class:"text-center pt-2"},mr={class:"mt-3 pt-3 border-t border-gray-200 flex items-center justify-between"},hr={class:"text-xs text-gray-400"},pr={class:"text-xs text-gray-400 mt-1"},vr={class:"relative"},yr={class:"max-w-4xl mx-auto"},wr={__name:"Chat",setup(r){const o=O([]),a=O(""),t=O(!1),e=O(null),n=O(null),i=O(null),l=O(!1),c=O([]),p=O([]),w=O(!1),T=O(!1),_=O(""),h=O("all"),b=O([]),v=O([]),H=O(!1),R=O(!1),F=O(typeof window<"u"?window.innerWidth:1024),k=O(!1),M=()=>{typeof window<"u"&&(F.value=window.innerWidth,k.value=window.innerWidth<640)},J=O(null),E=O(null),D=[{title:"Chat",href:"/chat"}],A=[{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,text:"I have a headache",color:"bg-blue-50 text-blue-700 border-blue-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
    </svg>`,text:"My tummy hurts",color:"bg-green-50 text-green-700 border-green-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
    </svg>`,text:"I can't lose weight",color:"bg-purple-50 text-purple-700 border-purple-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
    </svg>`,text:"My urine burns",color:"bg-orange-50 text-orange-700 border-orange-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
    </svg>`,text:"Book appointment",color:"bg-teal-50 text-teal-700 border-teal-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z" />
    </svg>`,text:"I have a skin rash",color:"bg-indigo-50 text-indigo-700 border-indigo-200"}],x=y=>{if(!y)return y;let d=y;return d=d.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),d=d.replace(/^## (.*?):/gm,'<h2 class="font-bold text-gray-900 mt-4 mb-3 text-lg">$1:</h2>'),d=d.replace(/^### (.*?):/gm,'<h3 class="font-semibold text-gray-900 mt-3 mb-2">$1:</h3>'),d=d.replace(/^#### (.*?):/gm,'<h4 class="font-semibold text-gray-900 mt-2 mb-1 text-sm">$1:</h4>'),d=d.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g,'<div class="mb-2"><strong>$1. $2</strong></div>'),d=d.replace(/- Why I think so: (.*?)(?=\d+\.|$)/gs,'<div class="ml-4 text-gray-900 mb-2">Why I think so: $1</div>'),d=d.replace(/^• \*\*(.*?):\*\* (.*$)/gm,'<div class="mb-1"><strong>$1:</strong> $2</div>'),d=d.replace(/^- \*\*(.*?)\*\*/gm,'<div class="mb-2"><strong>• $1</strong></div>'),d=d.replace(/^- (.*$)/gm,'<div class="mb-1 ml-4">• $1</div>'),d=d.replace(/\*\*(.*?):\*\*/g,'<h4 class="font-semibold text-gray-900 mt-4 mb-2">$1:</h4>'),d=d.replace(/^\*\*(.*?):\*\* (.*$)/gm,'<div class="mb-1"><strong>$1:</strong> $2</div>'),d=d.replace(/\n\n/g,'</p><p class="mb-2">'),d=d.replace(/\n/g,"<br>"),d=`<p class="mb-2">${d}</p>`,d=d.replace(/<p class="mb-2"><\/p>/g,""),d=d.replace(/<br><br>/g,"<br>"),d},f=(y,d)=>{const m=o.value.find(V=>V.id===y);if(!m)return;m.isStreaming=!0,m.displayedContent="",m.streamingLines=[],J.value=y;const C=d.split(`
`).filter(V=>V.trim()!=="");let U=0;const z=()=>{if(U<C.length){const V=C[U];m.streamingLines.push({content:V,id:`line-${U}`,fadeIn:!0}),m.displayedContent=m.streamingLines.map(q=>q.content).join(`
`),U++,X(()=>ee());const B=V.length>100?800:500;E.value=setTimeout(z,B)}else I()},I=()=>{m.isStreaming=!1,m.displayedContent=d,m.formatted=x(d),m.streamingLines=[],J.value=null,E.value&&(clearTimeout(E.value),E.value=null),X(()=>ee())};setTimeout(()=>{z()},200)},u=y=>{if(console.log("Processing backend slots:",y),!Array.isArray(y)){console.warn("backendSlots is not an array:",y),p.value=[],c.value=[];return}const d=[],m=new Date().toISOString().split("T")[0];y.forEach(C=>{if(!C||!C.provider){console.warn("Invalid provider slot:",C);return}const U=C.provider,z=C.service||null,I=C.date,V=C.day_of_week;C.slots&&Array.isArray(C.slots)&&C.slots.forEach(B=>{const q={id:`${U.id}-${I}-${B.start_time}`,provider_id:U.id,service_id:(z==null?void 0:z.id)||1,provider:`Dr. ${U.name}`,time:B.start_time,end_time:B.end_time,date:`${V}, ${new Date(I).toLocaleDateString()}`,full_date:I,price:(z==null?void 0:z.price)||50,datetime:new Date(`${I} ${B.start_time}`),isToday:I===m};d.push(q)})}),d.sort((C,U)=>C.isToday&&!U.isToday?-1:!C.isToday&&U.isToday?1:C.datetime-U.datetime),p.value=d,T.value=!1,g(d),ie(),console.log("Processed slots:",d.length,"Today slots:",Array.isArray(d)?d.filter(C=>C.isToday).length:0)},g=y=>{var z;if(!Array.isArray(y)){console.warn("slots is not an array in setupFilterOptions:",y),b.value=[{value:"",label:"All Days"}],v.value=[{value:"all",label:"All Providers"}],_.value="",h.value="all";return}const d=[...new Set(y.map(I=>I.full_date))],m=new Date().toISOString().split("T")[0];b.value=d.map(I=>{const V=new Date(I),B=I===m,q=V.toDateString()===new Date(Date.now()+24*60*60*1e3).toDateString();let $=V.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"});return B?$=`Today (${$})`:q&&($=`Tomorrow (${$})`),{value:I,label:$}}).sort((I,V)=>new Date(I.value)-new Date(V.value)),b.value.push({value:"",label:"All Days"});const C=[...new Set(y.map(I=>({id:I.provider_id,name:I.provider})))];v.value=[{value:"all",label:"All Providers"},...C.map(I=>({value:I.id.toString(),label:I.name}))],b.value.find(I=>I.value===m)?_.value=m:_.value=((z=b.value[0])==null?void 0:z.value)||""},S=async()=>{var y;w.value=!0,await X(),ee();try{console.log("TEST: Loading providers...");const d=await Z.get("/providers"),m=d.data.data||d.data.providers||d.data||[];if(console.log("TEST: Providers loaded:",m.length),m.length===0){o.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available providers at the moment. Please try again later or contact our office directly.",timestamp:new Date});return}const C=new Date,U=[];let z=[];try{z=(await Z.get("/appointments-list")).data.appointments||[],console.log("Existing appointments loaded:",z.length)}catch{console.log("Could not load existing appointments, proceeding without conflict check")}const I=[];for(let j=0;j<=14;j++){const K=new Date(C);K.setDate(K.getDate()+j);const Q=K.toISOString().split("T")[0];I.push({value:Q,label:K.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"}),fullDate:K})}b.value=I;const V=C.toISOString().split("T")[0];_.value||(_.value=V),v.value=[{value:"all",label:"All Providers"},...m.map(j=>{var K;return{value:j.id.toString(),label:`Dr. ${((K=j.user)==null?void 0:K.name)||"Provider"}`}})];for(let j=0;j<=14;j++){const K=new Date(C);K.setDate(K.getDate()+j);const Q=K.toISOString().split("T")[0];console.log(`TEST: Checking date ${Q}`);for(const G of m){console.log(`TEST: Checking provider ${G.id} - ${(y=G.user)==null?void 0:y.name}`);try{const ge=(await Z.get(`/providers/${G.id}/available-slots?date=${Q}`)).data.available_slots||[];if(console.log(`TEST: Provider ${G.id} has ${ge.length} slots on ${Q}`),ge.length>0){const Ae=ge.filter(se=>{const ce=`${Q} ${se.start_time}`;return!z.some(_e=>{var rt;return _e.status==="cancelled"?!1:`${_e.date||((rt=_e.scheduled_at)==null?void 0:rt.split("T")[0])} ${_e.time||_e.start_time}`===ce})});if(console.log(`TEST: Provider ${G.id} has ${Ae.length} available slots after filtering`),Ae.length===0){console.log(`TEST: All slots for provider ${G.id} on ${Q} are already booked`);continue}let oe=null;try{const ce=(await Z.get(`/providers/${G.id}/services`)).data.services||[];console.log(`TEST: Services for provider ${G.id}:`,ce),ce.length>0&&(oe=ce[0],console.log("TEST: Selected service:",oe),console.log(`TEST: Service ID: ${oe.id}, Name: ${oe.name}, Price: ${oe.price}`))}catch(se){console.error(`Error loading services for provider ${G.id}:`,se)}if(!oe||!oe.id){console.error(`No valid service found for provider ${G.id}, skipping slots`);continue}const Ft=Ae.map(se=>{var ce;return{id:`${G.id}-${Q}-${se.start_time}`,provider_id:G.id,service_id:oe.id,provider:`Dr. ${((ce=G.user)==null?void 0:ce.name)||"Provider"}`,specialty:G.specialization||"General Practice",date:K.toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"}),time:se.start_time,end_time:se.end_time,full_date:Q,datetime:new Date(`${Q}T${se.start_time}`),price:oe.price||0,duration:oe.duration||15,service_name:oe.name||"Consultation"}});U.push(...Ft)}}catch(le){console.error(`Error loading slots for provider ${G.id} on ${Q}:`,le)}}if(U.length>=50)break}if(console.log("TEST: Total slots found:",U.length),U.length===0){o.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available appointment slots in the next week. Please contact our office directly to schedule an appointment.",timestamp:new Date});return}U.sort((j,K)=>j.datetime-K.datetime);const B={};U.forEach(j=>{B[j.provider_id]||(B[j.provider_id]=[]),B[j.provider_id].push(j)});const q=[],$=Math.ceil(8/Object.keys(B).length);Object.keys(B).forEach(j=>{const K=B[j].slice(0,$);q.push(...K)}),q.sort((j,K)=>j.datetime-K.datetime),p.value=q,T.value=!1,l.value=!0,ie(),await X(),ee()}catch(d){console.error("Error loading appointment slots:",d),o.value.push({id:Date.now(),type:"ai",content:"I apologize, but I cannot access the appointment system right now. Please contact our office directly to schedule an appointment.",timestamp:new Date})}finally{w.value=!1,await X(),ee()}},L=async y=>{var d,m,C,U,z;try{let I="Appointment booked through AI chat consultation",V="General consultation";if(i.value)try{const K=await Z.get(`/web-api/chat/conversation/${i.value}`);if(K.data&&K.data.referral_note){I=K.data.referral_note;const Q=I.replace(/\*\*/g,"").replace(/###/g,"").replace(/\n+/g," ").trim();V=Q.length>150?Q.substring(0,150)+"...":Q}}catch{console.log("Could not fetch referral note, using default reason")}let B=y.service_id;if(y.provider_id===3&&(B=4,console.log("Fixed service ID for Provider 3 to Service ID 4")),!B)throw new Error("Invalid appointment slot: missing service ID");console.log("Using service ID:",B,"for provider:",y.provider_id);const q={provider_id:y.provider_id,service_id:B,date:y.full_date,time_slot:{start_time:y.time,end_time:y.end_time},reason:V,notes:I,currency:"USD"};console.log("Booking appointment with data (UPDATED):",q),console.log("Slot data:",y),console.log("Service ID being sent:",q.service_id),console.log("Provider ID being sent:",q.provider_id);const $=await fetch("/save-appointment-with-payment",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(q)});if(!$.ok){const K=await $.json();throw new Error(K.message||`HTTP ${$.status}`)}const j=await $.json();if(j.appointment){const K=j.appointment,Q=j.payment;if(Q&&Q.client_secret){const G=`🏥 Appointment Created - Payment Required

Provider: ${y.provider}
Date: ${y.date}
Time: ${y.time}
Appointment ID: #${K.id}
Amount: $${Q.amount}

Your appointment has been created and is pending payment. Please click the "Pay Now" button below to complete your booking.`,le={id:Date.now(),type:"ai",content:G,timestamp:new Date,formatted:x(G),showPaymentButton:!0,appointmentId:K.id,paymentAmount:Q.amount,paymentUrl:`/appointments/${K.id}/payment?conversation=${i.value}`};if(o.value.push(le),i.value)try{await Z.post("/web-api/chat/message",{conversation_id:i.value,message:le.content,role:"assistant"}),console.log("Payment message saved to chat history")}catch(ge){console.error("Error saving payment message to chat history:",ge)}}else{const G=`✅ **Appointment Confirmed!**

**Provider:** ${y.provider}
**Date:** ${y.date}
**Time:** ${y.time}
**Appointment ID:** #${K.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`,le={id:Date.now(),type:"ai",content:G,timestamp:new Date,formatted:x(G)};if(o.value.push(le),i.value)try{await Z.post("/web-api/chat/message",{conversation_id:i.value,message:le.content,role:"assistant"}),console.log("Confirmation message saved to chat history")}catch(ge){console.error("Error saving confirmation message to chat history:",ge)}}}else throw new Error("No appointment data returned");l.value=!1,await X(),ee()}catch(I){console.error("Error booking appointment:",I);let V="Unknown error occurred";(C=(m=I.response)==null?void 0:m.data)!=null&&C.message?V=I.response.data.message:(z=(U=I.response)==null?void 0:U.data)!=null&&z.errors?V=Object.values(I.response.data.errors).flat().join(", "):I.message&&(V=I.message),console.log("Detailed error:",V);const B={id:Date.now(),type:"ai",content:`I apologize, but there was an error booking your appointment with ${y.provider}.

**Error Details:** ${V}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`,timestamp:new Date,formatted:x(`I apologize, but there was an error booking your appointment with ${y.provider}.

**Error Details:** ${V}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`)};if(o.value.push(B),i.value)try{await Z.post("/web-api/chat/message",{conversation_id:i.value,message:B.content,role:"assistant"})}catch(q){console.error("Error saving error message to chat history:",q)}l.value=!1,await X(),ee()}},ne=async(y=null)=>{var U,z;const d=y||a.value.trim();if(!d)return;o.value.push({id:Date.now(),type:"user",content:d,timestamp:new Date}),y||(a.value=""),await X(),ee();const C={id:Date.now()+1,type:"ai",content:"",timestamp:new Date,isStreaming:!1,displayedContent:"",isPlaceholder:!0};o.value.push(C),await X(),ee(),t.value=!0;try{if(!i.value){const q=await fetch("/web-api/chat/start",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((U=document.querySelector('meta[name="csrf-token"]'))==null?void 0:U.getAttribute("content"))||""},credentials:"include",body:JSON.stringify({})}),$=await q.json();if(!q.ok)throw console.error("Start conversation failed:",$),new Error($.message||"Failed to start conversation");if(!$.conversation_id)throw new Error("No conversation ID returned");i.value=String($.conversation_id)}const I={conversation_id:String(i.value),message:d,include_patient_context:!0,generate_title:!0,request_full_response:!1};console.log("Sending payload:",I);const V=await fetch("/web-api/chat/message",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((z=document.querySelector('meta[name="csrf-token"]'))==null?void 0:z.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(I)}),B=await V.json();if(V.ok&&B.message){const q=o.value.find($=>$.isPlaceholder&&$.type==="ai");if(q)q.content=B.message,q.formatted=x(B.message),q.isPlaceholder=!1,q.isStreaming=!1,q.displayedContent="",f(q.id,B.message);else{const $=Date.now()+1,j={id:$,type:"ai",content:B.message,timestamp:new Date,formatted:x(B.message),isStreaming:!1,displayedContent:""};o.value.push(j),await X(),ee(),f($,B.message)}if(B.available_slots&&Array.isArray(B.available_slots))console.log("Backend provided appointment slots directly:",B.available_slots),u(B.available_slots),setTimeout(()=>{l.value=!0,X(()=>{ee()})},500);else if(B.appointment_options||B.show_appointments)setTimeout(()=>{S()},1e3);else{const $=B.message.toLowerCase();["would you like to see available appointment slots","shall i show you available appointments","would you like to book an appointment now","let me show you available slots","here are the available appointment times","perfect! i found available appointment slots","appointment options provided"].some(Q=>$.includes(Q))&&setTimeout(()=>{S()},1e3)}}else{console.error("Failed to get AI response:",B);const q=`Sorry, I encountered an error: ${B.message||"Please try again."}`,$=o.value.find(j=>j.isPlaceholder&&j.type==="ai");if($)$.content=q,$.isPlaceholder=!1,$.isStreaming=!1,$.displayedContent="",f($.id,q);else{const j=Date.now()+1,K={id:j,type:"ai",content:q,timestamp:new Date,isStreaming:!1,displayedContent:""};o.value.push(K),await X(),f(j,q)}}}catch(I){console.error("An error occurred while sending your message:",I);const V=`Sorry, I encountered an error: ${I.message||"Please try again."}`,B=o.value.find(q=>q.isPlaceholder&&q.type==="ai");if(B)B.content=V,B.isPlaceholder=!1,B.isStreaming=!1,B.displayedContent="",f(B.id,V);else{const q=Date.now()+1,$={id:q,type:"ai",content:V,timestamp:new Date,isStreaming:!1,displayedContent:""};o.value.push($),await X(),f(q,V)}}finally{t.value=!1,await X(),ee(),n.value&&n.value.focus&&setTimeout(()=>{n.value.focus()},100)}},fe=y=>{ne(y.text)},Te=y=>{y.key==="Enter"&&!y.shiftKey&&(y.preventDefault(),ne())},ee=()=>{e.value&&(e.value.scrollTop=e.value.scrollHeight)},be=y=>new Date(y).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),xe=()=>{o.value=[],i.value=null,l.value=!1,a.value="",n.value&&n.value.focus&&setTimeout(()=>{n.value.focus()},100)},ve=()=>{R.value=!0},ke=()=>{R.value=!1},ie=()=>{if(!Array.isArray(p.value)){console.warn("allAvailableSlots.value is not an array:",p.value),p.value=[],c.value=[];return}let y=[...p.value];_.value&&_.value!==""&&_.value!=="all"&&(y=y.filter(d=>d.full_date===_.value)),h.value&&h.value!=="all"&&(y=y.filter(d=>d.provider_id.toString()===h.value)),c.value=y,T.value=!0},Me=async y=>{if(y){H.value=!0;try{const m=(await Z.get(`/web-api/chat/conversation/${y}`)).data;m&&m.id&&(i.value=y,m.messages&&Array.isArray(m.messages)&&(o.value=m.messages.map(C=>({id:C._id||C.id||Date.now()+Math.random(),type:C.role==="user"?"user":"ai",content:C.content||C.message,timestamp:new Date(C.timestamp||C.created_at||Date.now()),formatted:C.role!=="user"?x(C.content||C.message):void 0})),await X(),ee()))}catch(d){console.error("Error loading conversation:",d),i.value=null,o.value=[]}finally{H.value=!1}}},ye=async()=>{var y,d,m;try{const C=localStorage.getItem("anonymous_conversation");if(!C)return null;const U=JSON.parse(C);if(!U.conversation_id||!U.anonymous_id)return null;console.log("Transferring anonymous conversation:",U.conversation_id),await new Promise(V=>setTimeout(V,500));const z={conversation_id:String(U.conversation_id),anonymous_id:String(U.anonymous_id)};if(console.log("Transfer payload:",z),(await Z.post("/web-api/chat/transfer-anonymous",z,{headers:{Accept:"application/json","Content-Type":"application/json","X-CSRF-TOKEN":((y=document.querySelector('meta[name="csrf-token"]'))==null?void 0:y.getAttribute("content"))||"","X-Requested-With":"XMLHttpRequest"},withCredentials:!0})).data.success)return console.log("Anonymous conversation transferred successfully"),localStorage.removeItem("anonymous_conversation"),U.conversation_id}catch(C){if(console.error("Error transferring anonymous conversation:",C),C.response&&(console.error("Response status:",C.response.status),console.error("Response data:",C.response.data)),((d=C.response)==null?void 0:d.status)===401)return console.log("Authentication required for transfer, will retry later"),null;if(((m=C.response)==null?void 0:m.status)===422)return console.error("Validation errors:",C.response.data.errors),null;localStorage.removeItem("anonymous_conversation")}return null},Ee=async y=>{var d,m,C;try{const z=(await Z.get(`/api/appointments/${y}`)).data.appointment,I=$=>{try{return new Date($).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})}catch{return $||"Date not available"}},B=`🎉 **Payment Successful - Appointment Confirmed!**

Excellent! Your payment has been processed successfully and your appointment is now confirmed.

**📅 Appointment Details:**
**Provider:** ${($=>{var j;return(j=$==null?void 0:$.user)!=null&&j.name?$.user.name:$!=null&&$.name?`Dr. ${$.name}`:"Your Doctor"})(z.provider)}
**Date:** ${I(z.date)}
**Time:** ${((d=z.time_slot)==null?void 0:d.start_time)||"Time TBD"} - ${((m=z.time_slot)==null?void 0:m.end_time)||"Time TBD"}
**Service:** ${((C=z.service)==null?void 0:C.name)||"Consultation"}
**Amount Paid:** $${z.amount||"0.00"}
**Appointment ID:** #${z.id||"N/A"}

**📧 What's Next:**
• You'll receive a confirmation email shortly
• A calendar invite will be sent to your email
• You can view and manage this appointment in your appointments section

Is there anything else I can help you with regarding your upcoming appointment or any other health concerns?`,q={id:Date.now(),type:"ai",content:B,timestamp:new Date,formatted:x(B)};if(o.value.push(q),i.value)try{await Z.post("/web-api/chat/message",{conversation_id:i.value,message:q.content,role:"assistant"}),console.log("Payment confirmation message saved to chat history")}catch($){console.error("Error saving confirmation message to chat history:",$)}await X(),ee()}catch(U){console.error("Error handling payment success:",U);const z=`🎉 **Payment Successful!**

Great news! Your payment has been processed successfully and your appointment is confirmed.

**Appointment ID:** #${y}

You'll receive a confirmation email shortly with all the details. You can also view your appointment in the appointments section.

Is there anything else I can help you with today?`,I={id:Date.now(),type:"ai",content:z,timestamp:new Date,formatted:x(z)};o.value.push(I),await X(),ee()}};return Vt(async()=>{M(),typeof window<"u"&&window.addEventListener("resize",M);const y=new URLSearchParams(window.location.search),d=y.get("conversation"),m=y.get("payment_success"),C=y.get("appointment_id");let U=await ye();!U&&localStorage.getItem("anonymous_conversation")&&(console.log("Retrying anonymous conversation transfer after authentication delay..."),await new Promise(I=>setTimeout(I,1500)),U=await ye());const z=d||U;z&&Me(z),m==="true"&&C&&(await Ee(C),window.history.replaceState({},document.title,window.location.pathname))}),Ht(()=>{E.value&&clearTimeout(E.value),typeof window<"u"&&window.removeEventListener("resize",M)}),(y,d)=>(N(),P(ae,null,[me(Ot(Kt),{title:"Chat - Medroid"}),me(Jt,{breadcrumbs:D},{default:Yt(()=>[s("div",yo,[s("div",wo,[s("div",bo,[me(Re,{size:32}),d[5]||(d[5]=s("div",{class:"hidden sm:block"},[s("h1",{class:"text-sm font-semibold text-gray-900"},"Medroid AI"),s("p",{class:"text-xs text-gray-500"},"Your AI Doctor")],-1))])]),s("div",xo,[s("div",_o,[s("button",{onClick:xe,class:W(["flex items-center text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm border border-white/50 rounded-full hover:bg-white/90 transition-all duration-200 shadow-lg",k.value?"p-2":"px-3 py-2 space-x-2"])},[d[6]||(d[6]=s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)),k.value?te("",!0):(N(),P("span",Co,"New Chat"))],2),s("button",{onClick:ve,class:W(["flex items-center text-sm font-medium text-green-600 bg-green-50/80 backdrop-blur-sm border border-green-200/50 rounded-full hover:bg-green-100/80 transition-all duration-200 shadow-lg",k.value?"p-2":"px-3 py-2 space-x-2"])},[d[7]||(d[7]=s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),k.value?te("",!0):(N(),P("span",ko,"Refer & Earn"))],2)])]),H.value?(N(),P("div",Eo,d[8]||(d[8]=[s("div",{class:"text-center"},[s("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),s("p",{class:"text-gray-600"},"Loading conversation...")],-1)]))):o.value.length===0?(N(),P("div",So,[s("div",To,[s("div",Mo,[me(Re,{size:k.value?60:80,"show-shadow":!0},null,8,["size"])]),d[9]||(d[9]=s("h1",{class:"text-2xl sm:text-4xl font-bold text-gray-900 mb-3 sm:mb-4"},"Welcome to Medroid",-1)),d[10]||(d[10]=s("p",{class:"text-base sm:text-lg text-gray-600 max-w-md mx-auto px-4"}," Your personal AI Dr. Ask me anything about your symptoms. ",-1))]),s("div",Ao,[(N(),P(ae,null,de(A,m=>s("button",{key:m.text,onClick:C=>fe(m),class:W([m.color,"flex flex-col items-center rounded-xl border-2 hover:shadow-md transition-all duration-200 hover:scale-105",k.value?"p-3":"p-4"])},[s("div",{class:"mb-2",innerHTML:m.icon},null,8,Io),s("span",{class:W(["font-medium text-center",k.value?"text-xs":"text-sm"])},Y(m.text),3)],10,Do)),64))])])):(N(),P("div",Ro,[s("div",Bo,[s("div",{ref_key:"chatContainer",ref:e,class:"flex-1 overflow-y-auto pt-4 pb-2"},[s("div",$o,[(N(!0),P(ae,null,de(o.value,m=>(N(),P("div",{key:m.id,class:W(["flex mb-4 transition-all duration-500 ease-out",m.type==="user"?"justify-end":"justify-start"])},[m.type==="ai"?(N(),P("div",Po,[s("div",No,[s("div",Lo,[me(Re,{size:24})])]),s("div",qo,[s("div",Fo,[m.isPlaceholder?(N(),P("div",Uo,d[11]||(d[11]=[s("span",{class:"text-gray-600"},"AI is thinking",-1),s("div",{class:"flex space-x-1"},[s("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce"}),s("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce",style:{"animation-delay":"0.15s"}}),s("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce",style:{"animation-delay":"0.3s"}})],-1)]))):m.isStreaming&&m.streamingLines?(N(),P("div",jo,[(N(!0),P(ae,null,de(m.streamingLines,C=>(N(),P("div",{key:C.id,class:"animate-fade-in-line",innerHTML:x(C.content)},null,8,zo))),128)),d[12]||(d[12]=s("span",{class:"inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse"},null,-1))])):(N(),P("div",{key:2,class:"prose prose-sm max-w-none",innerHTML:m.formatted||m.content},null,8,Vo))]),m.showPaymentButton?(N(),P("div",Ho,[s("div",Oo,[s("div",Ko," Appointment #"+Y(m.appointmentId)+" • $"+Y(m.paymentAmount),1),s("a",{href:m.paymentUrl,class:"inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"},d[13]||(d[13]=[s("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),$t(" Pay Now ")]),8,Yo)])])):te("",!0),s("div",Jo,Y(be(m.timestamp)),1)])])):(N(),P("div",Qo,[s("div",Go,[s("div",Wo,Y(m.content),1),s("div",Xo,Y(be(m.timestamp)),1)])]))],2))),128))]),l.value?(N(),P("div",Zo,[s("div",{class:W(["flex items-start",k.value?"space-x-2":"space-x-3"])},[s("div",{class:W(["bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1",k.value?"w-5 h-5":"w-6 h-6"])},[(N(),P("svg",{class:W(["text-white",k.value?"w-2.5 h-2.5":"w-3 h-3"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},d[14]||(d[14]=[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},null,-1)]),2))],2),s("div",er,[s("div",{class:W(["text-gray-800 leading-snug prose prose-sm max-w-none break-words",(k.value,"text-sm")])},[s("div",{class:W(["bg-white border border-gray-200 rounded-xl shadow-sm",k.value?"p-3":"p-4"])},[s("div",tr,[d[16]||(d[16]=s("h3",{class:"text-sm font-medium text-gray-900 mb-2"},"Available Appointments",-1)),s("p",nr,Y(c.value.length)+" slots available • Click any slot to book ",1),s("div",or,[s("div",rr,[nt(s("select",{"onUpdate:modelValue":d[0]||(d[0]=m=>_.value=m),onChange:ie,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"},[d[15]||(d[15]=s("option",{value:""},"All Days",-1)),(N(!0),P(ae,null,de(b.value,m=>(N(),P("option",{key:m.value,value:m.value},Y(m.label),9,sr))),128))],544),[[st,_.value]])]),s("div",ar,[nt(s("select",{"onUpdate:modelValue":d[1]||(d[1]=m=>h.value=m),onChange:ie,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"},[(N(!0),P(ae,null,de(v.value,m=>(N(),P("option",{key:m.value,value:m.value},Y(m.label),9,ir))),128))],544),[[st,h.value]])])])]),w.value?(N(),P("div",lr,d[17]||(d[17]=[s("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-teal-500"},null,-1),s("span",{class:"ml-2 text-xs text-gray-600"},"Loading slots...",-1)]))):c.value.length===0?(N(),P("div",cr,d[18]||(d[18]=[s("p",{class:"text-xs text-gray-500"},"No appointments available for selected filters",-1)]))):(N(),P("div",ur,[s("div",{class:W(["grid gap-2 mb-3",k.value?"grid-cols-1":"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"])},[(N(!0),P(ae,null,de(T.value?c.value:c.value.slice(0,k.value?6:9),m=>(N(),P("button",{key:m.id,onClick:C=>L(m),class:W(["flex flex-col bg-gray-50 hover:bg-teal-50 border border-gray-200 hover:border-teal-300 rounded-lg transition-all duration-200 text-left",k.value?"p-2.5":"p-3"])},[s("div",fr,[s("div",{class:W(["font-medium text-gray-900",(k.value,"text-sm")])},Y(m.time),3),s("div",{class:W(["font-semibold text-teal-600",(k.value,"text-sm")])}," $"+Y(m.price),3)]),s("div",{class:W(["text-gray-500 mb-1",(k.value,"text-xs")])},Y(m.provider.replace("Dr. ","")),3),_.value===""?(N(),P("div",{key:0,class:W(["text-gray-400",(k.value,"text-xs")])},Y(m.date.split(",")[0]),3)):te("",!0)],10,dr))),128))],2),c.value.length>9?(N(),P("div",gr,[s("button",{onClick:d[2]||(d[2]=m=>T.value=!T.value),class:"text-xs text-teal-600 hover:text-teal-700 font-medium px-3 py-1 rounded-md hover:bg-teal-50 transition-colors"},Y(T.value?"Show less":`Show ${c.value.length-9} more slots`),1)])):te("",!0)])),s("div",mr,[s("button",{onClick:d[3]||(d[3]=m=>l.value=!1),class:"text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200"}," Close "),s("div",hr,Y(_.value?"Filtered by date":"All available days"),1)])],2)],2),s("div",pr,Y(be(new Date)),1)])],2)])):te("",!0)],512)])])),s("div",vr,[d[19]||(d[19]=s("div",{class:"absolute inset-x-0 bottom-0 h-20 sm:h-24 bg-gray-50 -z-10"},null,-1)),s("div",{class:W(["bg-gray-50 relative",k.value?"px-3 py-3":"px-6 py-4"])},[s("div",yr,[me(Qt,{ref_key:"chatInputRef",ref:n,modelValue:a.value,"onUpdate:modelValue":d[4]||(d[4]=m=>a.value=m),placeholder:"Type your health question...","is-loading":t.value,"show-tools":!1,"show-version":!1,onSend:ne,onKeydown:Te},null,8,["modelValue","is-loading"])])],2)])]),me(vo,{"is-open":R.value,onClose:ke},null,8,["is-open"])]),_:1})],64))}},Mr=Gt(wr,[["__scopeId","data-v-87f82a89"]]);export{Mr as default};
