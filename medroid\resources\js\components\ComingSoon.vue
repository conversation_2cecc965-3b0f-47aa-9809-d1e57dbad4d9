<script setup>
import { computed } from 'vue'

const props = defineProps({
    title: {
        type: String,
        default: 'Coming Soon'
    },
    description: {
        type: String,
        required: true
    },
    features: {
        type: Array,
        required: true
    },
    actionButton: {
        type: Object,
        default: () => ({
            text: 'Go Back',
            action: null,
            icon: null
        })
    },
    actionDescription: {
        type: String,
        default: ''
    },
    minHeight: {
        type: String,
        default: 'min-h-[600px]'
    },
    iconGradient: {
        type: String,
        default: 'from-teal-500 to-cyan-500'
    },
    buttonGradient: {
        type: String,
        default: 'from-teal-500 to-cyan-500'
    },
    buttonHoverGradient: {
        type: String,
        default: 'from-teal-600 to-cyan-600'
    },
    featureDotColor: {
        type: String,
        default: 'bg-teal-500'
    }
})

const emit = defineEmits(['action'])

const handleAction = () => {
    if (props.actionButton.action) {
        emit('action', props.actionButton.action)
    }
}
</script>

<template>
    <div :class="[minHeight, 'bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center']">
        <div class="max-w-md mx-auto text-center px-6">
            <!-- Lock Icon -->
            <div :class="['w-24 h-24 mx-auto mb-8 bg-gradient-to-br rounded-full flex items-center justify-center shadow-lg', iconGradient]">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
            </div>

            <!-- Coming Soon Text -->
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                {{ title }}
            </h1>

            <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                {{ description }}
            </p>

            <!-- Features Preview -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">What's Coming:</h3>
                <div class="space-y-3 text-left">
                    <div v-for="feature in features" :key="feature" class="flex items-center space-x-3">
                        <div :class="['w-2 h-2 rounded-full', featureDotColor]"></div>
                        <span class="text-gray-700">{{ feature }}</span>
                    </div>
                </div>
            </div>

            <!-- Action Button -->
            <div v-if="actionButton.text" class="space-y-4">
                <button
                    @click="handleAction"
                    :class="[
                        'inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5',
                        `${buttonGradient} hover:${buttonHoverGradient}`
                    ]"
                >
                    <component 
                        v-if="actionButton.icon" 
                        :is="actionButton.icon" 
                        class="w-5 h-5 mr-2"
                    />
                    <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    {{ actionButton.text }}
                </button>

                <p v-if="actionDescription" class="text-sm text-gray-500">
                    {{ actionDescription }}
                </p>
            </div>
        </div>
    </div>
</template>
