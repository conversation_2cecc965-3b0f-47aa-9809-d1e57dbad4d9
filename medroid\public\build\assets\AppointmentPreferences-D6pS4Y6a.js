var bt=e=>{throw TypeError(e)};var xn=(e,t,n)=>t.has(e)||bt("Cannot "+n);var Re=(e,t,n)=>(xn(e,t,"read from private field"),n?n.call(e):t.get(e)),Ct=(e,t,n)=>t.has(e)?bt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n);import{c as k,r as E,Y as bn,w as Ce,a1 as Cn,a2 as _n,a8 as we,u as c,M,y as N,e as T,g as b,N as I,a9 as An,aa as Sn,a3 as je,h as _t,ab as Pn,ac as Yt,o as ge,ad as $n,d as F,f as P,Q as J,J as Gt,i as z,G as Xt,n as _e,F as Jt,p as On,j as Ae,C as ke,a5 as En,a6 as Tn,a7 as Rn,ae as Bn,x as D,t as xe,A as Ie,U as kn,m as Dn,a as At}from"./vendor-BK2qhpQJ.js";import{a as Mn,b as In,_ as Vn}from"./Layout.vue_vue_type_script_setup_true_lang-BvmLVDru.js";import{_ as St}from"./index-BsQMq1AH.js";import{u as Ue,b as $e,c as Pt,d as Ln,_ as Be,a as zn}from"./Label.vue_vue_type_script_setup_true_lang-Cu3Z5Mpv.js";import{i as Fn,c as Oe,g as Zt,u as Qt,a as en,b as Wn,d as Nn,_ as Hn,e as jn,f as $t,h as tn,P as Un,j as Kn}from"./useBodyScrollLock-Czdv3moC.js";import{P as he,S as Ot,c as ae}from"./Primitive-BOKwGa1V.js";import{d as qn,e as Yn,f as Gn,a as Xn,r as Ke}from"./index-vK5yNL7A.js";const Jn=["top","right","bottom","left"],ce=Math.min,q=Math.max,We=Math.round,Fe=Math.floor,te=e=>({x:e,y:e}),Zn={left:"right",right:"left",bottom:"top",top:"bottom"},Qn={start:"end",end:"start"};function st(e,t,n){return q(e,ce(t,n))}function ie(e,t){return typeof e=="function"?e(t):e}function se(e){return e.split("-")[0]}function Ee(e){return e.split("-")[1]}function pt(e){return e==="x"?"y":"x"}function mt(e){return e==="y"?"height":"width"}function ue(e){return["top","bottom"].includes(se(e))?"y":"x"}function gt(e){return pt(ue(e))}function eo(e,t,n){n===void 0&&(n=!1);const o=Ee(e),r=gt(e),i=mt(r);let a=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Ne(a)),[a,Ne(a)]}function to(e){const t=Ne(e);return[at(e),t,at(t)]}function at(e){return e.replace(/start|end/g,t=>Qn[t])}function no(e,t,n){const o=["left","right"],r=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?i:a;default:return[]}}function oo(e,t,n,o){const r=Ee(e);let i=no(se(e),n==="start",o);return r&&(i=i.map(a=>a+"-"+r),t&&(i=i.concat(i.map(at)))),i}function Ne(e){return e.replace(/left|right|bottom|top/g,t=>Zn[t])}function ro(e){return{top:0,right:0,bottom:0,left:0,...e}}function nn(e){return typeof e!="number"?ro(e):{top:e,right:e,bottom:e,left:e}}function He(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function Et(e,t,n){let{reference:o,floating:r}=e;const i=ue(t),a=gt(t),l=mt(a),f=se(t),u=i==="y",s=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,p=o[l]/2-r[l]/2;let m;switch(f){case"top":m={x:s,y:o.y-r.height};break;case"bottom":m={x:s,y:o.y+o.height};break;case"right":m={x:o.x+o.width,y:d};break;case"left":m={x:o.x-r.width,y:d};break;default:m={x:o.x,y:o.y}}switch(Ee(t)){case"start":m[a]-=p*(n&&u?-1:1);break;case"end":m[a]+=p*(n&&u?-1:1);break}return m}const io=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),f=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:r}),{x:s,y:d}=Et(u,o,f),p=o,m={},g=0;for(let w=0;w<l.length;w++){const{name:x,fn:A}=l[w],{x:y,y:h,data:v,reset:_}=await A({x:s,y:d,initialPlacement:o,placement:p,strategy:r,middlewareData:m,rects:u,platform:a,elements:{reference:e,floating:t}});s=y??s,d=h??d,m={...m,[x]:{...m[x],...v}},_&&g<=50&&(g++,typeof _=="object"&&(_.placement&&(p=_.placement),_.rects&&(u=_.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:r}):_.rects),{x:s,y:d}=Et(u,p,f)),w=-1)}return{x:s,y:d,placement:p,strategy:r,middlewareData:m}};async function De(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:i,rects:a,elements:l,strategy:f}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=ie(t,e),g=nn(m),x=l[p?d==="floating"?"reference":"floating":d],A=He(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(x)))==null||n?x:x.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:s,strategy:f})),y=d==="floating"?{x:o,y:r,width:a.floating.width,height:a.floating.height}:a.reference,h=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),v=await(i.isElement==null?void 0:i.isElement(h))?await(i.getScale==null?void 0:i.getScale(h))||{x:1,y:1}:{x:1,y:1},_=He(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:h,strategy:f}):y);return{top:(A.top-_.top+g.top)/v.y,bottom:(_.bottom-A.bottom+g.bottom)/v.y,left:(A.left-_.left+g.left)/v.x,right:(_.right-A.right+g.right)/v.x}}const so=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:i,platform:a,elements:l,middlewareData:f}=t,{element:u,padding:s=0}=ie(e,t)||{};if(u==null)return{};const d=nn(s),p={x:n,y:o},m=gt(r),g=mt(m),w=await a.getDimensions(u),x=m==="y",A=x?"top":"left",y=x?"bottom":"right",h=x?"clientHeight":"clientWidth",v=i.reference[g]+i.reference[m]-p[m]-i.floating[g],_=p[m]-i.reference[m],S=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let C=S?S[h]:0;(!C||!await(a.isElement==null?void 0:a.isElement(S)))&&(C=l.floating[h]||i.floating[g]);const $=v/2-_/2,R=C/2-w[g]/2-1,B=ce(d[A],R),O=ce(d[y],R),L=B,H=C-w[g]-O,V=C/2-w[g]/2+$,U=st(L,V,H),K=!f.arrow&&Ee(r)!=null&&V!==U&&i.reference[g]/2-(V<L?B:O)-w[g]/2<0,W=K?V<L?V-L:V-H:0;return{[m]:p[m]+W,data:{[m]:U,centerOffset:V-U-W,...K&&{alignmentOffset:W}},reset:K}}}),ao=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:i,rects:a,initialPlacement:l,platform:f,elements:u}=t,{mainAxis:s=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:w=!0,...x}=ie(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const A=se(r),y=ue(l),h=se(l)===l,v=await(f.isRTL==null?void 0:f.isRTL(u.floating)),_=p||(h||!w?[Ne(l)]:to(l)),S=g!=="none";!p&&S&&_.push(...oo(l,w,g,v));const C=[l,..._],$=await De(t,x),R=[];let B=((o=i.flip)==null?void 0:o.overflows)||[];if(s&&R.push($[A]),d){const V=eo(r,a,v);R.push($[V[0]],$[V[1]])}if(B=[...B,{placement:r,overflows:R}],!R.every(V=>V<=0)){var O,L;const V=(((O=i.flip)==null?void 0:O.index)||0)+1,U=C[V];if(U)return{data:{index:V,overflows:B},reset:{placement:U}};let K=(L=B.filter(W=>W.overflows[0]<=0).sort((W,Z)=>W.overflows[1]-Z.overflows[1])[0])==null?void 0:L.placement;if(!K)switch(m){case"bestFit":{var H;const W=(H=B.filter(Z=>{if(S){const Q=ue(Z.placement);return Q===y||Q==="y"}return!0}).map(Z=>[Z.placement,Z.overflows.filter(Q=>Q>0).reduce((Q,ye)=>Q+ye,0)]).sort((Z,Q)=>Z[1]-Q[1])[0])==null?void 0:H[0];W&&(K=W);break}case"initialPlacement":K=l;break}if(r!==K)return{reset:{placement:K}}}return{}}}};function Tt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Rt(e){return Jn.some(t=>e[t]>=0)}const lo=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ie(e,t);switch(o){case"referenceHidden":{const i=await De(t,{...r,elementContext:"reference"}),a=Tt(i,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Rt(a)}}}case"escaped":{const i=await De(t,{...r,altBoundary:!0}),a=Tt(i,n.floating);return{data:{escapedOffsets:a,escaped:Rt(a)}}}default:return{}}}}};async function co(e,t){const{placement:n,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),a=se(n),l=Ee(n),f=ue(n)==="y",u=["left","top"].includes(a)?-1:1,s=i&&f?-1:1,d=ie(t,e);let{mainAxis:p,crossAxis:m,alignmentAxis:g}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof g=="number"&&(m=l==="end"?g*-1:g),f?{x:m*s,y:p*u}:{x:p*u,y:m*s}}const uo=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:i,placement:a,middlewareData:l}=t,f=await co(t,e);return a===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:r+f.x,y:i+f.y,data:{...f,placement:a}}}}},fo=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:x=>{let{x:A,y}=x;return{x:A,y}}},...f}=ie(e,t),u={x:n,y:o},s=await De(t,f),d=ue(se(r)),p=pt(d);let m=u[p],g=u[d];if(i){const x=p==="y"?"top":"left",A=p==="y"?"bottom":"right",y=m+s[x],h=m-s[A];m=st(y,m,h)}if(a){const x=d==="y"?"top":"left",A=d==="y"?"bottom":"right",y=g+s[x],h=g-s[A];g=st(y,g,h)}const w=l.fn({...t,[p]:m,[d]:g});return{...w,data:{x:w.x-n,y:w.y-o,enabled:{[p]:i,[d]:a}}}}}},po=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:f=!0,crossAxis:u=!0}=ie(e,t),s={x:n,y:o},d=ue(r),p=pt(d);let m=s[p],g=s[d];const w=ie(l,t),x=typeof w=="number"?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(f){const h=p==="y"?"height":"width",v=i.reference[p]-i.floating[h]+x.mainAxis,_=i.reference[p]+i.reference[h]-x.mainAxis;m<v?m=v:m>_&&(m=_)}if(u){var A,y;const h=p==="y"?"width":"height",v=["top","left"].includes(se(r)),_=i.reference[d]-i.floating[h]+(v&&((A=a.offset)==null?void 0:A[d])||0)+(v?0:x.crossAxis),S=i.reference[d]+i.reference[h]+(v?0:((y=a.offset)==null?void 0:y[d])||0)-(v?x.crossAxis:0);g<_?g=_:g>S&&(g=S)}return{[p]:m,[d]:g}}}},mo=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:i,platform:a,elements:l}=t,{apply:f=()=>{},...u}=ie(e,t),s=await De(t,u),d=se(r),p=Ee(r),m=ue(r)==="y",{width:g,height:w}=i.floating;let x,A;d==="top"||d==="bottom"?(x=d,A=p===(await(a.isRTL==null?void 0:a.isRTL(l.floating))?"start":"end")?"left":"right"):(A=d,x=p==="end"?"top":"bottom");const y=w-s.top-s.bottom,h=g-s.left-s.right,v=ce(w-s[x],y),_=ce(g-s[A],h),S=!t.middlewareData.shift;let C=v,$=_;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&($=h),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(C=y),S&&!p){const B=q(s.left,0),O=q(s.right,0),L=q(s.top,0),H=q(s.bottom,0);m?$=g-2*(B!==0||O!==0?B+O:q(s.left,s.right)):C=w-2*(L!==0||H!==0?L+H:q(s.top,s.bottom))}await f({...t,availableWidth:$,availableHeight:C});const R=await a.getDimensions(l.floating);return g!==R.width||w!==R.height?{reset:{rects:!0}}:{}}}};function qe(){return typeof window<"u"}function ve(e){return ht(e)?(e.nodeName||"").toLowerCase():"#document"}function Y(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function oe(e){var t;return(t=(ht(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function ht(e){return qe()?e instanceof Node||e instanceof Y(e).Node:!1}function G(e){return qe()?e instanceof Element||e instanceof Y(e).Element:!1}function ne(e){return qe()?e instanceof HTMLElement||e instanceof Y(e).HTMLElement:!1}function Bt(e){return!qe()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Y(e).ShadowRoot}function Ve(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=X(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function go(e){return["table","td","th"].includes(ve(e))}function Ye(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function vt(e){const t=yt(),n=G(e)?X(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function ho(e){let t=de(e);for(;ne(t)&&!Pe(t);){if(vt(t))return t;if(Ye(t))return null;t=de(t)}return null}function yt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Pe(e){return["html","body","#document"].includes(ve(e))}function X(e){return Y(e).getComputedStyle(e)}function Ge(e){return G(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function de(e){if(ve(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Bt(e)&&e.host||oe(e);return Bt(t)?t.host:t}function on(e){const t=de(e);return Pe(t)?e.ownerDocument?e.ownerDocument.body:e.body:ne(t)&&Ve(t)?t:on(t)}function Me(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=on(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),a=Y(r);if(i){const l=lt(a);return t.concat(a,a.visualViewport||[],Ve(r)?r:[],l&&n?Me(l):[])}return t.concat(r,Me(r,[],n))}function lt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function rn(e){const t=X(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=ne(e),i=r?e.offsetWidth:n,a=r?e.offsetHeight:o,l=We(n)!==i||We(o)!==a;return l&&(n=i,o=a),{width:n,height:o,$:l}}function wt(e){return G(e)?e:e.contextElement}function Se(e){const t=wt(e);if(!ne(t))return te(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:i}=rn(t);let a=(i?We(n.width):n.width)/o,l=(i?We(n.height):n.height)/r;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const vo=te(0);function sn(e){const t=Y(e);return!yt()||!t.visualViewport?vo:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function yo(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Y(e)?!1:t}function me(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=wt(e);let a=te(1);t&&(o?G(o)&&(a=Se(o)):a=Se(e));const l=yo(i,n,o)?sn(i):te(0);let f=(r.left+l.x)/a.x,u=(r.top+l.y)/a.y,s=r.width/a.x,d=r.height/a.y;if(i){const p=Y(i),m=o&&G(o)?Y(o):o;let g=p,w=lt(g);for(;w&&o&&m!==g;){const x=Se(w),A=w.getBoundingClientRect(),y=X(w),h=A.left+(w.clientLeft+parseFloat(y.paddingLeft))*x.x,v=A.top+(w.clientTop+parseFloat(y.paddingTop))*x.y;f*=x.x,u*=x.y,s*=x.x,d*=x.y,f+=h,u+=v,g=Y(w),w=lt(g)}}return He({width:s,height:d,x:f,y:u})}function xt(e,t){const n=Ge(e).scrollLeft;return t?t.left+n:me(oe(e)).left+n}function an(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:xt(e,o)),i=o.top+t.scrollTop;return{x:r,y:i}}function wo(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const i=r==="fixed",a=oe(o),l=t?Ye(t.floating):!1;if(o===a||l&&i)return n;let f={scrollLeft:0,scrollTop:0},u=te(1);const s=te(0),d=ne(o);if((d||!d&&!i)&&((ve(o)!=="body"||Ve(a))&&(f=Ge(o)),ne(o))){const m=me(o);u=Se(o),s.x=m.x+o.clientLeft,s.y=m.y+o.clientTop}const p=a&&!d&&!i?an(a,f,!0):te(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+s.x+p.x,y:n.y*u.y-f.scrollTop*u.y+s.y+p.y}}function xo(e){return Array.from(e.getClientRects())}function bo(e){const t=oe(e),n=Ge(e),o=e.ownerDocument.body,r=q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let a=-n.scrollLeft+xt(e);const l=-n.scrollTop;return X(o).direction==="rtl"&&(a+=q(t.clientWidth,o.clientWidth)-r),{width:r,height:i,x:a,y:l}}function Co(e,t){const n=Y(e),o=oe(e),r=n.visualViewport;let i=o.clientWidth,a=o.clientHeight,l=0,f=0;if(r){i=r.width,a=r.height;const u=yt();(!u||u&&t==="fixed")&&(l=r.offsetLeft,f=r.offsetTop)}return{width:i,height:a,x:l,y:f}}function _o(e,t){const n=me(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,i=ne(e)?Se(e):te(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y,f=r*i.x,u=o*i.y;return{width:a,height:l,x:f,y:u}}function kt(e,t,n){let o;if(t==="viewport")o=Co(e,n);else if(t==="document")o=bo(oe(e));else if(G(t))o=_o(t,n);else{const r=sn(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return He(o)}function ln(e,t){const n=de(e);return n===t||!G(n)||Pe(n)?!1:X(n).position==="fixed"||ln(n,t)}function Ao(e,t){const n=t.get(e);if(n)return n;let o=Me(e,[],!1).filter(l=>G(l)&&ve(l)!=="body"),r=null;const i=X(e).position==="fixed";let a=i?de(e):e;for(;G(a)&&!Pe(a);){const l=X(a),f=vt(a);!f&&l.position==="fixed"&&(r=null),(i?!f&&!r:!f&&l.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Ve(a)&&!f&&ln(e,a))?o=o.filter(s=>s!==a):r=l,a=de(a)}return t.set(e,o),o}function So(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const a=[...n==="clippingAncestors"?Ye(t)?[]:Ao(t,this._c):[].concat(n),o],l=a[0],f=a.reduce((u,s)=>{const d=kt(t,s,r);return u.top=q(d.top,u.top),u.right=ce(d.right,u.right),u.bottom=ce(d.bottom,u.bottom),u.left=q(d.left,u.left),u},kt(t,l,r));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function Po(e){const{width:t,height:n}=rn(e);return{width:t,height:n}}function $o(e,t,n){const o=ne(t),r=oe(t),i=n==="fixed",a=me(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const f=te(0);if(o||!o&&!i)if((ve(t)!=="body"||Ve(r))&&(l=Ge(t)),o){const p=me(t,!0,i,t);f.x=p.x+t.clientLeft,f.y=p.y+t.clientTop}else r&&(f.x=xt(r));const u=r&&!o&&!i?an(r,l):te(0),s=a.left+l.scrollLeft-f.x-u.x,d=a.top+l.scrollTop-f.y-u.y;return{x:s,y:d,width:a.width,height:a.height}}function et(e){return X(e).position==="static"}function Dt(e,t){if(!ne(e)||X(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return oe(e)===n&&(n=n.ownerDocument.body),n}function cn(e,t){const n=Y(e);if(Ye(e))return n;if(!ne(e)){let r=de(e);for(;r&&!Pe(r);){if(G(r)&&!et(r))return r;r=de(r)}return n}let o=Dt(e,t);for(;o&&go(o)&&et(o);)o=Dt(o,t);return o&&Pe(o)&&et(o)&&!vt(o)?n:o||ho(e)||n}const Oo=async function(e){const t=this.getOffsetParent||cn,n=this.getDimensions,o=await n(e.floating);return{reference:$o(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Eo(e){return X(e).direction==="rtl"}const To={convertOffsetParentRelativeRectToViewportRelativeRect:wo,getDocumentElement:oe,getClippingRect:So,getOffsetParent:cn,getElementRects:Oo,getClientRects:xo,getDimensions:Po,getScale:Se,isElement:G,isRTL:Eo};function un(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ro(e,t){let n=null,o;const r=oe(e);function i(){var l;clearTimeout(o),(l=n)==null||l.disconnect(),n=null}function a(l,f){l===void 0&&(l=!1),f===void 0&&(f=1),i();const u=e.getBoundingClientRect(),{left:s,top:d,width:p,height:m}=u;if(l||t(),!p||!m)return;const g=Fe(d),w=Fe(r.clientWidth-(s+p)),x=Fe(r.clientHeight-(d+m)),A=Fe(s),h={rootMargin:-g+"px "+-w+"px "+-x+"px "+-A+"px",threshold:q(0,ce(1,f))||1};let v=!0;function _(S){const C=S[0].intersectionRatio;if(C!==f){if(!v)return a();C?a(!1,C):o=setTimeout(()=>{a(!1,1e-7)},1e3)}C===1&&!un(u,e.getBoundingClientRect())&&a(),v=!1}try{n=new IntersectionObserver(_,{...h,root:r.ownerDocument})}catch{n=new IntersectionObserver(_,h)}n.observe(e)}return a(!0),i}function Bo(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:f=!1}=o,u=wt(e),s=r||i?[...u?Me(u):[],...Me(t)]:[];s.forEach(A=>{r&&A.addEventListener("scroll",n,{passive:!0}),i&&A.addEventListener("resize",n)});const d=u&&l?Ro(u,n):null;let p=-1,m=null;a&&(m=new ResizeObserver(A=>{let[y]=A;y&&y.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var h;(h=m)==null||h.observe(t)})),n()}),u&&!f&&m.observe(u),m.observe(t));let g,w=f?me(e):null;f&&x();function x(){const A=me(e);w&&!un(w,A)&&n(),w=A,g=requestAnimationFrame(x)}return n(),()=>{var A;s.forEach(y=>{r&&y.removeEventListener("scroll",n),i&&y.removeEventListener("resize",n)}),d==null||d(),(A=m)==null||A.disconnect(),m=null,f&&cancelAnimationFrame(g)}}const ko=uo,Do=fo,Mt=ao,Mo=mo,Io=lo,Vo=so,Lo=po,zo=(e,t,n)=>{const o=new Map,r={platform:To,...n},i={...r.platform,_c:o};return io(e,t,{...r,platform:i})};function Fo(e){return e!=null&&typeof e=="object"&&"$el"in e}function ct(e){if(Fo(e)){const t=e.$el;return ht(t)&&ve(t)==="#comment"?null:t}return e}function be(e){return typeof e=="function"?e():c(e)}function Wo(e){return{name:"arrow",options:e,fn(t){const n=ct(be(e.element));return n==null?{}:Vo({element:n,padding:e.padding}).fn(t)}}}function dn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function It(e,t){const n=dn(e);return Math.round(t*n)/n}function No(e,t,n){n===void 0&&(n={});const o=n.whileElementsMounted,r=k(()=>{var C;return(C=be(n.open))!=null?C:!0}),i=k(()=>be(n.middleware)),a=k(()=>{var C;return(C=be(n.placement))!=null?C:"bottom"}),l=k(()=>{var C;return(C=be(n.strategy))!=null?C:"absolute"}),f=k(()=>{var C;return(C=be(n.transform))!=null?C:!0}),u=k(()=>ct(e.value)),s=k(()=>ct(t.value)),d=E(0),p=E(0),m=E(l.value),g=E(a.value),w=bn({}),x=E(!1),A=k(()=>{const C={position:m.value,left:"0",top:"0"};if(!s.value)return C;const $=It(s.value,d.value),R=It(s.value,p.value);return f.value?{...C,transform:"translate("+$+"px, "+R+"px)",...dn(s.value)>=1.5&&{willChange:"transform"}}:{position:m.value,left:$+"px",top:R+"px"}});let y;function h(){if(u.value==null||s.value==null)return;const C=r.value;zo(u.value,s.value,{middleware:i.value,placement:a.value,strategy:l.value}).then($=>{d.value=$.x,p.value=$.y,m.value=$.strategy,g.value=$.placement,w.value=$.middlewareData,x.value=C!==!1})}function v(){typeof y=="function"&&(y(),y=void 0)}function _(){if(v(),o===void 0){h();return}if(u.value!=null&&s.value!=null){y=o(u.value,s.value,h);return}}function S(){r.value||(x.value=!1)}return Ce([i,a,l,r],h,{flush:"sync"}),Ce([u,s],_,{flush:"sync"}),Ce(r,S,{flush:"sync"}),Cn()&&_n(v),{x:we(d),y:we(p),strategy:we(m),placement:we(g),middlewareData:we(w),isPositioned:we(x),floatingStyles:A,update:h}}const Ho=M({__name:"VisuallyHidden",props:{feature:{default:"focusable"},asChild:{type:Boolean},as:{default:"span"}},setup(e){return(t,n)=>(T(),N(c(he),{as:t.as,"as-child":t.asChild,"aria-hidden":t.feature==="focusable"?"true":void 0,"data-hidden":t.feature==="fully-hidden"?"":void 0,tabindex:t.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:b(()=>[I(t.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}});function Vt(e){return typeof e=="string"?`'${e}'`:new jo().serialize(e)}const jo=function(){var t;class e{constructor(){Ct(this,t,new Map)}compare(o,r){const i=typeof o,a=typeof r;return i==="string"&&a==="string"?o.localeCompare(r):i==="number"&&a==="number"?o-r:String.prototype.localeCompare.call(this.serialize(o,!0),this.serialize(r,!0))}serialize(o,r){if(o===null)return"null";switch(typeof o){case"string":return r?o:`'${o}'`;case"bigint":return`${o}n`;case"object":return this.$object(o);case"function":return this.$function(o)}return String(o)}serializeObject(o){const r=Object.prototype.toString.call(o);if(r!=="[object Object]")return this.serializeBuiltInType(r.length<10?`unknown:${r}`:r.slice(8,-1),o);const i=o.constructor,a=i===Object||i===void 0?"":i.name;if(a!==""&&globalThis[a]===i)return this.serializeBuiltInType(a,o);if(typeof o.toJSON=="function"){const l=o.toJSON();return a+(l!==null&&typeof l=="object"?this.$object(l):`(${this.serialize(l)})`)}return this.serializeObjectEntries(a,Object.entries(o))}serializeBuiltInType(o,r){const i=this["$"+o];if(i)return i.call(this,r);if(typeof(r==null?void 0:r.entries)=="function")return this.serializeObjectEntries(o,r.entries());throw new Error(`Cannot serialize ${o}`)}serializeObjectEntries(o,r){const i=Array.from(r).sort((l,f)=>this.compare(l[0],f[0]));let a=`${o}{`;for(let l=0;l<i.length;l++){const[f,u]=i[l];a+=`${this.serialize(f,!0)}:${this.serialize(u)}`,l<i.length-1&&(a+=",")}return a+"}"}$object(o){let r=Re(this,t).get(o);return r===void 0&&(Re(this,t).set(o,`#${Re(this,t).size}`),r=this.serializeObject(o),Re(this,t).set(o,r)),r}$function(o){const r=Function.prototype.toString.call(o);return r.slice(-15)==="[native code] }"?`${o.name||""}()[native]`:`${o.name}(${o.length})${r.replace(/\s*\n\s*/g,"")}`}$Array(o){let r="[";for(let i=0;i<o.length;i++)r+=this.serialize(o[i]),i<o.length-1&&(r+=",");return r+"]"}$Date(o){try{return`Date(${o.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(o){return`ArrayBuffer[${new Uint8Array(o).join(",")}]`}$Set(o){return`Set${this.$Array(Array.from(o).sort((r,i)=>this.compare(r,i)))}`}$Map(o){return this.serializeObjectEntries("Map",o.entries())}}t=new WeakMap;for(const n of["Error","RegExp","URL"])e.prototype["$"+n]=function(o){return`${n}(${o})`};for(const n of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])e.prototype["$"+n]=function(o){return`${n}[${o.join(",")}]`};for(const n of["BigInt64Array","BigUint64Array"])e.prototype["$"+n]=function(o){return`${n}[${o.join("n,")}${o.length>0?"n":""}]`};return e}();function Uo(e,t){return e===t||Vt(e)===Vt(t)}function Lt(e){return e==null}function Ko(e){const t=Fn({dir:E("ltr")});return k(()=>{var n;return(e==null?void 0:e.value)||((n=t.dir)==null?void 0:n.value)||"ltr"})}function zt(){const e=E(),t=k(()=>{var n,o;return["#text","#comment"].includes((n=e.value)==null?void 0:n.$el.nodeName)?(o=e.value)==null?void 0:o.$el.nextElementSibling:Ue(e)});return{primitiveElement:e,currentElement:t}}function qo(e){return k(()=>{var t;return qn(e)?!!((t=Ue(e))!=null&&t.closest("form")):!0})}const Ft="data-reka-collection-item";function Le(e={}){const{key:t="",isProvider:n=!1}=e,o=`${t}CollectionProvider`;let r;if(n){const s=E(new Map);r={collectionRef:E(),itemMap:s},An(o,r)}else r=Sn(o);const i=(s=!1)=>{const d=r.collectionRef.value;if(!d)return[];const p=Array.from(d.querySelectorAll(`[${Ft}]`)),g=Array.from(r.itemMap.value.values()).sort((w,x)=>p.indexOf(w.ref)-p.indexOf(x.ref));return s?g:g.filter(w=>w.ref.dataset.disabled!=="")},a=M({name:"CollectionSlot",setup(s,{slots:d}){const{primitiveElement:p,currentElement:m}=zt();return Ce(m,()=>{r.collectionRef.value=m.value}),()=>_t(Ot,{ref:p},d)}}),l=M({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(s,{slots:d,attrs:p}){const{primitiveElement:m,currentElement:g}=zt();return je(w=>{if(g.value){const x=Pn(g.value);r.itemMap.value.set(x,{ref:g.value,value:s.value}),w(()=>r.itemMap.value.delete(x))}}),()=>_t(Ot,{...p,[Ft]:"",ref:m},d)}}),f=k(()=>Array.from(r.itemMap.value.values())),u=k(()=>r.itemMap.value.size);return{getItems:i,reactiveItems:f,itemMapSize:u,CollectionSlot:a,CollectionItem:l}}const[fn,Yo]=Oe("PopperRoot"),Go=M({inheritAttrs:!1,__name:"PopperRoot",setup(e){const t=E();return Yo({anchor:t,onAnchorChange:n=>t.value=n}),(n,o)=>I(n.$slots,"default")}});function pn(e){const t=Yn("",1e3);return{search:t,handleTypeaheadSearch:(r,i)=>{t.value=t.value+r;{const a=Zt(),l=i.map(p=>{var m,g;return{...p,textValue:((m=p.value)==null?void 0:m.textValue)??((g=p.ref.textContent)==null?void 0:g.trim())??""}}),f=l.find(p=>p.ref===a),u=l.map(p=>p.textValue),s=Jo(u,t.value,f==null?void 0:f.textValue),d=l.find(p=>p.textValue===s);return d&&d.ref.focus(),d==null?void 0:d.ref}},resetTypeahead:()=>{t.value=""}}}function Xo(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function Jo(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let a=Xo(e,Math.max(i,0));r.length===1&&(a=a.filter(u=>u!==n));const f=a.find(u=>u.toLowerCase().startsWith(r.toLowerCase()));return f!==n?f:void 0}const Zo=M({__name:"PopperAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,{forwardRef:n,currentElement:o}=$e(),r=fn();return Yt(()=>{r.onAnchorChange(t.reference??o.value)}),(i,a)=>(T(),N(c(he),{ref:c(n),as:i.as,"as-child":i.asChild},{default:b(()=>[I(i.$slots,"default")]),_:3},8,["as","as-child"]))}});function Qo(e){return e!==null}function er(e){return{name:"transformOrigin",options:e,fn(t){var x,A,y;const{placement:n,rects:o,middlewareData:r}=t,a=((x=r.arrow)==null?void 0:x.centerOffset)!==0,l=a?0:e.arrowWidth,f=a?0:e.arrowHeight,[u,s]=ut(n),d={start:"0%",center:"50%",end:"100%"}[s],p=(((A=r.arrow)==null?void 0:A.x)??0)+l/2,m=(((y=r.arrow)==null?void 0:y.y)??0)+f/2;let g="",w="";return u==="bottom"?(g=a?d:`${p}px`,w=`${-f}px`):u==="top"?(g=a?d:`${p}px`,w=`${o.floating.height+f}px`):u==="right"?(g=`${-f}px`,w=a?d:`${m}px`):u==="left"&&(g=`${o.floating.width+f}px`,w=a?d:`${m}px`),{data:{x:g,y:w}}}}}function ut(e){const[t,n="center"]=e.split("-");return[t,n]}function tr(e){const t=E(),n=k(()=>{var r;return((r=t.value)==null?void 0:r.width)??0}),o=k(()=>{var r;return((r=t.value)==null?void 0:r.height)??0});return ge(()=>{const r=Ue(e);if(r){t.value={width:r.offsetWidth,height:r.offsetHeight};const i=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const l=a[0];let f,u;if("borderBoxSize"in l){const s=l.borderBoxSize,d=Array.isArray(s)?s[0]:s;f=d.inlineSize,u=d.blockSize}else f=r.offsetWidth,u=r.offsetHeight;t.value={width:f,height:u}});return i.observe(r,{box:"border-box"}),()=>i.unobserve(r)}else t.value=void 0}),{width:n,height:o}}const nr={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[Zr,or]=Oe("PopperContent"),rr=M({inheritAttrs:!1,__name:"PopperContent",props:$n({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...nr}),emits:["placed"],setup(e,{emit:t}){const n=e,o=t,r=fn(),{forwardRef:i,currentElement:a}=$e(),l=E(),f=E(),{width:u,height:s}=tr(f),d=k(()=>n.side+(n.align!=="center"?`-${n.align}`:"")),p=k(()=>typeof n.collisionPadding=="number"?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),m=k(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),g=k(()=>({padding:p.value,boundary:m.value.filter(Qo),altBoundary:m.value.length>0})),w=Gn(()=>[ko({mainAxis:n.sideOffset+s.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&Mt({...g.value}),n.avoidCollisions&&Do({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky==="partial"?Lo():void 0,...g.value}),!n.prioritizePosition&&n.avoidCollisions&&Mt({...g.value}),Mo({...g.value,apply:({elements:O,rects:L,availableWidth:H,availableHeight:V})=>{const{width:U,height:K}=L.reference,W=O.floating.style;W.setProperty("--reka-popper-available-width",`${H}px`),W.setProperty("--reka-popper-available-height",`${V}px`),W.setProperty("--reka-popper-anchor-width",`${U}px`),W.setProperty("--reka-popper-anchor-height",`${K}px`)}}),f.value&&Wo({element:f.value,padding:n.arrowPadding}),er({arrowWidth:u.value,arrowHeight:s.value}),n.hideWhenDetached&&Io({strategy:"referenceHidden",...g.value})]),x=k(()=>n.reference??r.anchor.value),{floatingStyles:A,placement:y,isPositioned:h,middlewareData:v}=No(x,l,{strategy:n.positionStrategy,placement:d,whileElementsMounted:(...O)=>Bo(...O,{layoutShift:!n.disableUpdateOnLayoutShift,animationFrame:n.updatePositionStrategy==="always"}),middleware:w}),_=k(()=>ut(y.value)[0]),S=k(()=>ut(y.value)[1]);Yt(()=>{h.value&&o("placed")});const C=k(()=>{var O;return((O=v.value.arrow)==null?void 0:O.centerOffset)!==0}),$=E("");je(()=>{a.value&&($.value=window.getComputedStyle(a.value).zIndex)});const R=k(()=>{var O;return((O=v.value.arrow)==null?void 0:O.x)??0}),B=k(()=>{var O;return((O=v.value.arrow)==null?void 0:O.y)??0});return or({placedSide:_,onArrowChange:O=>f.value=O,arrowX:R,arrowY:B,shouldHideArrow:C}),(O,L)=>{var H,V,U;return T(),F("div",{ref_key:"floatingRef",ref:l,"data-reka-popper-content-wrapper":"",style:Gt({...c(A),transform:c(h)?c(A).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$.value,"--reka-popper-transform-origin":[(H=c(v).transformOrigin)==null?void 0:H.x,(V=c(v).transformOrigin)==null?void 0:V.y].join(" "),...((U=c(v).hide)==null?void 0:U.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[P(c(he),J({ref:c(i)},O.$attrs,{"as-child":n.asChild,as:O.as,"data-side":_.value,"data-align":S.value,style:{animation:c(h)?void 0:"none"}}),{default:b(()=>[I(O.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}});let tt=0;function ir(){je(e=>{if(!Xn)return;const t=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",t[0]??Wt()),document.body.insertAdjacentElement("beforeend",t[1]??Wt()),tt++,e(()=>{tt===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(n=>n.remove()),tt--})})}function Wt(){const e=document.createElement("span");return e.setAttribute("data-reka-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function Nt(e,t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY){return Math.min(n,Math.max(t,e))}const sr=M({__name:"BubbleSelect",props:{autocomplete:{},autofocus:{type:Boolean},disabled:{type:Boolean},form:{},multiple:{type:Boolean},name:{},required:{type:Boolean},size:{},value:{}},setup(e){const t=e,n=E();return Ce(()=>t.value,(o,r)=>{const i=window.HTMLSelectElement.prototype,l=Object.getOwnPropertyDescriptor(i,"value").set;if(o!==r&&l&&n.value){const f=new Event("change",{bubbles:!0});l.call(n.value,o),n.value.dispatchEvent(f)}}),(o,r)=>(T(),N(c(Ho),{"as-child":""},{default:b(()=>[z("select",J({ref_key:"selectElement",ref:n},t),[I(o.$slots,"default")],16)]),_:3}))}}),ar=[" ","Enter","ArrowUp","ArrowDown"],lr=[" ","Enter"],ee=10;function dt(e,t,n){return e===void 0?!1:Array.isArray(e)?e.some(o=>ft(o,t,n)):ft(e,t,n)}function ft(e,t,n){return e===void 0||t===void 0?!1:typeof e=="string"?e===t:typeof n=="function"?n(e,t):typeof n=="string"?(e==null?void 0:e[n])===(t==null?void 0:t[n]):Uo(e,t)}const cr={key:0,value:""},[Te,mn]=Oe("SelectRoot"),ur=M({inheritAttrs:!1,__name:"SelectRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{default:void 0},by:{},dir:{},multiple:{type:Boolean},autocomplete:{},disabled:{type:Boolean},name:{},required:{type:Boolean}},emits:["update:modelValue","update:open"],setup(e,{emit:t}){const n=e,o=t,{required:r,disabled:i,multiple:a,dir:l}=Xt(n),f=Pt(n,"modelValue",o,{defaultValue:n.defaultValue??(a.value?[]:void 0),passive:n.modelValue===void 0,deep:!0}),u=Pt(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0}),s=E(),d=E(),p=E({x:0,y:0}),m=k(()=>{var h;return a.value&&Array.isArray(f.value)?((h=f.value)==null?void 0:h.length)===0:Lt(f.value)});Le({isProvider:!0});const g=Ko(l),w=qo(s),x=E(new Set),A=k(()=>Array.from(x.value).map(h=>h.value).join(";"));function y(h){if(a.value){const v=Array.isArray(f.value)?[...f.value]:[],_=v.findIndex(S=>ft(S,h,n.by));_===-1?v.push(h):v.splice(_,1),f.value=[...v]}else f.value=h}return mn({triggerElement:s,onTriggerChange:h=>{s.value=h},valueElement:d,onValueElementChange:h=>{d.value=h},contentId:"",modelValue:f,onValueChange:y,by:n.by,open:u,multiple:a,required:r,onOpenChange:h=>{u.value=h},dir:g,triggerPointerDownPosRef:p,disabled:i,isEmptyModelValue:m,optionsSet:x,onOptionAdd:h=>x.value.add(h),onOptionRemove:h=>x.value.delete(h)}),(h,v)=>(T(),N(c(Go),null,{default:b(()=>[I(h.$slots,"default",{modelValue:c(f),open:c(u)}),c(w)?(T(),N(sr,{key:A.value,"aria-hidden":"true",tabindex:"-1",multiple:c(a),required:c(r),name:h.name,autocomplete:h.autocomplete,disabled:c(i),value:c(f)},{default:b(()=>[c(Lt)(c(f))?(T(),F("option",cr)):_e("",!0),(T(!0),F(Jt,null,On(Array.from(x.value),_=>(T(),F("option",J({key:_.value??"",ref_for:!0},_),null,16))),128))]),_:1},8,["multiple","required","name","autocomplete","disabled","value"])):_e("",!0)]),_:3}))}}),dr=M({__name:"SelectTrigger",props:{disabled:{type:Boolean},reference:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=Te(),{forwardRef:o,currentElement:r}=$e(),i=k(()=>{var p;return((p=n.disabled)==null?void 0:p.value)||t.disabled});n.contentId||(n.contentId=Qt(void 0,"reka-select-content")),ge(()=>{n.onTriggerChange(r.value)});const{getItems:a}=Le(),{search:l,handleTypeaheadSearch:f,resetTypeahead:u}=pn();function s(){i.value||(n.onOpenChange(!0),u())}function d(p){s(),n.triggerPointerDownPosRef.value={x:Math.round(p.pageX),y:Math.round(p.pageY)}}return(p,m)=>(T(),N(c(Zo),{"as-child":"",reference:p.reference},{default:b(()=>{var g,w,x,A;return[P(c(he),{ref:c(o),role:"combobox",type:p.as==="button"?"button":void 0,"aria-controls":c(n).contentId,"aria-expanded":c(n).open.value||!1,"aria-required":(g=c(n).required)==null?void 0:g.value,"aria-autocomplete":"none",disabled:i.value,dir:(w=c(n))==null?void 0:w.dir.value,"data-state":(x=c(n))!=null&&x.open.value?"open":"closed","data-disabled":i.value?"":void 0,"data-placeholder":(A=c(n).modelValue)!=null&&A.value?void 0:"","as-child":p.asChild,as:p.as,onClick:m[0]||(m[0]=y=>{var h;(h=y==null?void 0:y.currentTarget)==null||h.focus()}),onPointerdown:m[1]||(m[1]=y=>{if(y.pointerType==="touch")return y.preventDefault();const h=y.target;h.hasPointerCapture(y.pointerId)&&h.releasePointerCapture(y.pointerId),y.button===0&&y.ctrlKey===!1&&(d(y),y.preventDefault())}),onPointerup:m[2]||(m[2]=Ae(y=>{y.pointerType==="touch"&&d(y)},["prevent"])),onKeydown:m[3]||(m[3]=y=>{const h=c(l)!=="";!(y.ctrlKey||y.altKey||y.metaKey)&&y.key.length===1&&h&&y.key===" "||(c(f)(y.key,c(a)()),c(ar).includes(y.key)&&(s(),y.preventDefault()))})},{default:b(()=>[I(p.$slots,"default")]),_:3},8,["type","aria-controls","aria-expanded","aria-required","disabled","dir","data-state","data-disabled","data-placeholder","as-child","as"])]}),_:3},8,["reference"]))}}),[Qr,fr]=Oe("SelectItemAlignedPosition"),pr=M({inheritAttrs:!1,__name:"SelectItemAlignedPosition",props:{asChild:{type:Boolean},as:{}},emits:["placed"],setup(e,{emit:t}){const n=e,o=t,{getItems:r}=Le(),i=Te(),a=gn(),l=E(!1),f=E(!0),u=E(),{forwardRef:s,currentElement:d}=$e(),{viewport:p,selectedItem:m,selectedItemText:g,focusSelectedItem:w}=a;function x(){if(i.triggerElement.value&&i.valueElement.value&&u.value&&d.value&&(p!=null&&p.value)&&(m!=null&&m.value)&&(g!=null&&g.value)){const h=i.triggerElement.value.getBoundingClientRect(),v=d.value.getBoundingClientRect(),_=i.valueElement.value.getBoundingClientRect(),S=g.value.getBoundingClientRect();if(i.dir.value!=="rtl"){const re=S.left-v.left,le=_.left-re,fe=h.left-le,pe=h.width+fe,Je=Math.max(pe,v.width),Ze=window.innerWidth-ee,Qe=Nt(le,ee,Math.max(ee,Ze-Je));u.value.style.minWidth=`${pe}px`,u.value.style.left=`${Qe}px`}else{const re=v.right-S.right,le=window.innerWidth-_.right-re,fe=window.innerWidth-h.right-le,pe=h.width+fe,Je=Math.max(pe,v.width),Ze=window.innerWidth-ee,Qe=Nt(le,ee,Math.max(ee,Ze-Je));u.value.style.minWidth=`${pe}px`,u.value.style.right=`${Qe}px`}const C=r().map(re=>re.ref),$=window.innerHeight-ee*2,R=p.value.scrollHeight,B=window.getComputedStyle(d.value),O=Number.parseInt(B.borderTopWidth,10),L=Number.parseInt(B.paddingTop,10),H=Number.parseInt(B.borderBottomWidth,10),V=Number.parseInt(B.paddingBottom,10),U=O+L+R+V+H,K=Math.min(m.value.offsetHeight*5,U),W=window.getComputedStyle(p.value),Z=Number.parseInt(W.paddingTop,10),Q=Number.parseInt(W.paddingBottom,10),ye=h.top+h.height/2-ee,vn=$-ye,Xe=m.value.offsetHeight/2,yn=m.value.offsetTop+Xe,ze=O+L+yn,wn=U-ze;if(ze<=ye){const re=m.value===C[C.length-1];u.value.style.bottom="0px";const le=d.value.clientHeight-p.value.offsetTop-p.value.offsetHeight,fe=Math.max(vn,Xe+(re?Q:0)+le+H),pe=ze+fe;u.value.style.height=`${pe}px`}else{const re=m.value===C[0];u.value.style.top="0px";const fe=Math.max(ye,O+p.value.offsetTop+(re?Z:0)+Xe)+wn;u.value.style.height=`${fe}px`,p.value.scrollTop=ze-ye+p.value.offsetTop}u.value.style.margin=`${ee}px 0`,u.value.style.minHeight=`${K}px`,u.value.style.maxHeight=`${$}px`,o("placed"),requestAnimationFrame(()=>l.value=!0)}}const A=E("");ge(async()=>{await ke(),x(),d.value&&(A.value=window.getComputedStyle(d.value).zIndex)});function y(h){h&&f.value===!0&&(x(),w==null||w(),f.value=!1)}return Ln(i.triggerElement,()=>{x()}),fr({contentWrapper:u,shouldExpandOnScrollRef:l,onScrollButtonChange:y}),(h,v)=>(T(),F("div",{ref_key:"contentWrapperElement",ref:u,style:Gt({display:"flex",flexDirection:"column",position:"fixed",zIndex:A.value})},[P(c(he),J({ref:c(s),style:{boxSizing:"border-box",maxHeight:"100%"}},{...h.$attrs,...n}),{default:b(()=>[I(h.$slots,"default")]),_:3},16)],4))}}),mr=M({__name:"SelectPopperPosition",props:{side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{default:ee},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},setup(e){const n=en(e);return(o,r)=>(T(),N(c(rr),J(c(n),{style:{boxSizing:"border-box","--reka-select-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-select-content-available-width":"var(--reka-popper-available-width)","--reka-select-content-available-height":"var(--reka-popper-available-height)","--reka-select-trigger-width":"var(--reka-popper-anchor-width)","--reka-select-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:b(()=>[I(o.$slots,"default")]),_:3},16))}}),gr={onViewportChange:()=>{},itemTextRefCallback:()=>{},itemRefCallback:()=>{}},[gn,hn]=Oe("SelectContent"),hr=M({__name:"SelectContentImpl",props:{position:{default:"item-aligned"},bodyLock:{type:Boolean,default:!0},side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,o=t,r=Te();ir(),Wn(n.bodyLock);const{CollectionSlot:i,getItems:a}=Le(),l=E();Nn(l);const{search:f,handleTypeaheadSearch:u}=pn(),s=E(),d=E(),p=E(),m=E(!1),g=E(!1),w=E(!1);function x(){d.value&&l.value&&$t([d.value,l.value])}Ce(m,()=>{x()});const{onOpenChange:A,triggerPointerDownPosRef:y}=r;je(S=>{if(!l.value)return;let C={x:0,y:0};const $=B=>{var O,L;C={x:Math.abs(Math.round(B.pageX)-(((O=y.value)==null?void 0:O.x)??0)),y:Math.abs(Math.round(B.pageY)-(((L=y.value)==null?void 0:L.y)??0))}},R=B=>{var O;B.pointerType!=="touch"&&(C.x<=10&&C.y<=10?B.preventDefault():(O=l.value)!=null&&O.contains(B.target)||A(!1),document.removeEventListener("pointermove",$),y.value=null)};y.value!==null&&(document.addEventListener("pointermove",$),document.addEventListener("pointerup",R,{capture:!0,once:!0})),S(()=>{document.removeEventListener("pointermove",$),document.removeEventListener("pointerup",R,{capture:!0})})});function h(S){const C=S.ctrlKey||S.altKey||S.metaKey;if(S.key==="Tab"&&S.preventDefault(),!C&&S.key.length===1&&u(S.key,a()),["ArrowUp","ArrowDown","Home","End"].includes(S.key)){let R=[...a().map(B=>B.ref)];if(["ArrowUp","End"].includes(S.key)&&(R=R.slice().reverse()),["ArrowUp","ArrowDown"].includes(S.key)){const B=S.target,O=R.indexOf(B);R=R.slice(O+1)}setTimeout(()=>$t(R)),S.preventDefault()}}const v=k(()=>n.position==="popper"?n:{}),_=en(v.value);return hn({content:l,viewport:s,onViewportChange:S=>{s.value=S},itemRefCallback:(S,C,$)=>{const R=!g.value&&!$,B=dt(r.modelValue.value,C,r.by);if(r.multiple.value){if(w.value)return;(B||R)&&(d.value=S,B&&(w.value=!0))}else(B||R)&&(d.value=S);R&&(g.value=!0)},selectedItem:d,selectedItemText:p,onItemLeave:()=>{var S;(S=l.value)==null||S.focus()},itemTextRefCallback:(S,C,$)=>{const R=!g.value&&!$;(dt(r.modelValue.value,C,r.by)||R)&&(p.value=S)},focusSelectedItem:x,position:n.position,isPositioned:m,searchRef:f}),(S,C)=>(T(),N(c(i),null,{default:b(()=>[P(c(Hn),{"as-child":"",onMountAutoFocus:C[6]||(C[6]=Ae(()=>{},["prevent"])),onUnmountAutoFocus:C[7]||(C[7]=$=>{var R;o("closeAutoFocus",$),!$.defaultPrevented&&((R=c(r).triggerElement.value)==null||R.focus({preventScroll:!0}),$.preventDefault())})},{default:b(()=>[P(c(jn),{"as-child":"","disable-outside-pointer-events":"",onFocusOutside:C[2]||(C[2]=Ae(()=>{},["prevent"])),onDismiss:C[3]||(C[3]=$=>c(r).onOpenChange(!1)),onEscapeKeyDown:C[4]||(C[4]=$=>o("escapeKeyDown",$)),onPointerDownOutside:C[5]||(C[5]=$=>o("pointerDownOutside",$))},{default:b(()=>[(T(),N(En(S.position==="popper"?mr:pr),J({...S.$attrs,...c(_)},{id:c(r).contentId,ref:$=>{l.value=c(Ue)($)},role:"listbox","data-state":c(r).open.value?"open":"closed",dir:c(r).dir.value,style:{display:"flex",flexDirection:"column",outline:"none"},onContextmenu:C[0]||(C[0]=Ae(()=>{},["prevent"])),onPlaced:C[1]||(C[1]=$=>m.value=!0),onKeydown:h}),{default:b(()=>[I(S.$slots,"default")]),_:3},16,["id","data-state","dir","onKeydown"]))]),_:3})]),_:3})]),_:3}))}}),vr=M({inheritAttrs:!1,__name:"SelectProvider",props:{context:{}},setup(e){return mn(e.context),hn(gr),(n,o)=>I(n.$slots,"default")}}),yr={key:1},wr=M({inheritAttrs:!1,__name:"SelectContent",props:{forceMount:{type:Boolean},position:{},bodyLock:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=tn(n,t),i=Te(),a=E();ge(()=>{a.value=new DocumentFragment});const l=E(),f=k(()=>n.forceMount||i.open.value);return(u,s)=>{var d;return f.value?(T(),N(c(Un),{key:0,ref_key:"presenceRef",ref:l,present:!0},{default:b(()=>[P(hr,Tn(Rn({...c(r),...u.$attrs})),{default:b(()=>[I(u.$slots,"default")]),_:3},16)]),_:3},512)):!((d=l.value)!=null&&d.present)&&a.value?(T(),F("div",yr,[(T(),N(Bn,{to:a.value},[P(vr,{context:c(i)},{default:b(()=>[I(u.$slots,"default")]),_:3},8,["context"])],8,["to"]))])):_e("",!0)}}}),[ei,xr]=Oe("SelectItem"),br=M({__name:"SelectItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:t}){const n=e,o=t,{disabled:r}=Xt(n),i=Te(),a=gn(),{forwardRef:l,currentElement:f}=$e(),{CollectionItem:u}=Le(),s=k(()=>{var v;return dt((v=i.modelValue)==null?void 0:v.value,n.value,i.by)}),d=E(!1),p=E(n.textValue??""),m=Qt(void 0,"reka-select-item-text"),g="select.select";async function w(v){if(v.defaultPrevented)return;const _={originalEvent:v,value:n.value};Kn(g,x,_)}async function x(v){await ke(),o("select",v),!v.defaultPrevented&&(r.value||(i.onValueChange(n.value),i.multiple.value||i.onOpenChange(!1)))}async function A(v){var _;await ke(),!v.defaultPrevented&&(r.value?(_=a.onItemLeave)==null||_.call(a):v.currentTarget.focus({preventScroll:!0}))}async function y(v){var _;await ke(),!v.defaultPrevented&&v.currentTarget===Zt()&&((_=a.onItemLeave)==null||_.call(a))}async function h(v){var S;await ke(),!(v.defaultPrevented||((S=a.searchRef)==null?void 0:S.value)!==""&&v.key===" ")&&(lr.includes(v.key)&&w(v),v.key===" "&&v.preventDefault())}if(n.value==="")throw new Error("A <SelectItem /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return ge(()=>{f.value&&a.itemRefCallback(f.value,n.value,n.disabled)}),xr({value:n.value,disabled:r,textId:m,isSelected:s,onItemTextChange:v=>{p.value=((p.value||(v==null?void 0:v.textContent))??"").trim()}}),(v,_)=>(T(),N(c(u),{value:{textValue:p.value}},{default:b(()=>[P(c(he),{ref:c(l),role:"option","aria-labelledby":c(m),"data-highlighted":d.value?"":void 0,"aria-selected":s.value,"data-state":s.value?"checked":"unchecked","aria-disabled":c(r)||void 0,"data-disabled":c(r)?"":void 0,tabindex:c(r)?void 0:-1,as:v.as,"as-child":v.asChild,onFocus:_[0]||(_[0]=S=>d.value=!0),onBlur:_[1]||(_[1]=S=>d.value=!1),onPointerup:w,onPointerdown:_[2]||(_[2]=S=>{S.currentTarget.focus({preventScroll:!0})}),onTouchend:_[3]||(_[3]=Ae(()=>{},["prevent","stop"])),onPointermove:A,onPointerleave:y,onKeydown:h},{default:b(()=>[I(v.$slots,"default")]),_:3},8,["aria-labelledby","data-highlighted","aria-selected","data-state","aria-disabled","data-disabled","tabindex","as","as-child"])]),_:3},8,["value"]))}}),Cr=M({__name:"SelectValue",props:{placeholder:{default:""},asChild:{type:Boolean},as:{default:"span"}},setup(e){const t=e,{forwardRef:n,currentElement:o}=$e(),r=Te();ge(()=>{r.valueElement=o});const i=k(()=>{var s;let l=[];const f=Array.from(r.optionsSet.value),u=d=>f.find(p=>p.value===d);return Array.isArray(r.modelValue.value)?l=r.modelValue.value.map(d=>{var p;return((p=u(d))==null?void 0:p.textContent)??""}):l=[((s=u(r.modelValue.value))==null?void 0:s.textContent)??""],l.filter(Boolean)}),a=k(()=>i.value.length?i.value.join(", "):t.placeholder);return(l,f)=>(T(),N(c(he),{ref:c(n),as:l.as,"as-child":l.asChild,style:{pointerEvents:"none"},"data-placeholder":i.value.length?void 0:t.placeholder},{default:b(()=>[I(l.$slots,"default",{selectedLabel:i.value,modelValue:c(r).modelValue.value},()=>[D(xe(a.value),1)])]),_:3},8,["as","as-child","data-placeholder"]))}}),nt=M({__name:"Select",props:{open:{type:Boolean},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{},by:{type:[String,Function]},dir:{},multiple:{type:Boolean},autocomplete:{},disabled:{type:Boolean},name:{},required:{type:Boolean}},emits:["update:modelValue","update:open"],setup(e,{emit:t}){const r=tn(e,t);return(i,a)=>(T(),N(c(ur),J({"data-slot":"select"},c(r)),{default:b(()=>[I(i.$slots,"default")]),_:3},16))}}),ot=M({__name:"SelectContent",props:{forceMount:{type:Boolean},position:{default:"popper"},bodyLock:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=Ke(t,"class");return(o,r)=>(T(),N(c(wr),J({"data-slot":"select-content"},c(n),{class:c(ae)("relative z-50 max-h-96 min-w-32 overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",o.position==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t.class)}),{default:b(()=>[I(o.$slots,"default")]),_:3},16,["class"]))}}),_r={class:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center"},j=M({__name:"SelectItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=Ke(t,"class");return(o,r)=>(T(),N(c(br),J({"data-slot":"select-item"},c(n),{class:c(ae)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t.class)}),{default:b(()=>[z("span",_r,[I(o.$slots,"indicator")]),I(o.$slots,"default")]),_:3},16,["class"]))}}),rt=M({__name:"SelectTrigger",props:{disabled:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=Ke(t,"class");return(o,r)=>(T(),N(c(dr),J({"data-slot":"select-trigger"},c(n),{class:c(ae)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t.class)}),{default:b(()=>[I(o.$slots,"default")]),_:3},16,["class"]))}}),it=M({__name:"SelectValue",props:{placeholder:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=Ke(t,"class");return(o,r)=>(T(),N(c(Cr),J({"data-slot":"select-value"},c(n),{class:c(ae)("text-sm placeholder:text-muted-foreground",t.class)}),{default:b(()=>[I(o.$slots,"default")]),_:3},16,["class"]))}}),Ht=M({__name:"Card",props:{class:{}},setup(e){const t=e;return(n,o)=>(T(),F("div",{"data-slot":"card",class:Ie(c(ae)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t.class))},[I(n.$slots,"default")],2))}}),jt=M({__name:"CardContent",props:{class:{}},setup(e){const t=e;return(n,o)=>(T(),F("div",{"data-slot":"card-content",class:Ie(c(ae)("px-6",t.class))},[I(n.$slots,"default")],2))}}),Ut=M({__name:"CardDescription",props:{class:{}},setup(e){const t=e;return(n,o)=>(T(),F("p",{"data-slot":"card-description",class:Ie(c(ae)("text-muted-foreground text-sm",t.class))},[I(n.$slots,"default")],2))}}),Kt=M({__name:"CardHeader",props:{class:{}},setup(e){const t=e;return(n,o)=>(T(),F("div",{"data-slot":"card-header",class:Ie(c(ae)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t.class))},[I(n.$slots,"default")],2))}}),qt=M({__name:"CardTitle",props:{class:{}},setup(e){const t=e;return(n,o)=>(T(),F("h3",{"data-slot":"card-title",class:Ie(c(ae)("leading-none font-semibold",t.class))},[I(n.$slots,"default")],2))}}),Ar={class:"space-y-6"},Sr={key:0,class:"flex items-center justify-center py-8"},Pr={key:1,class:"space-y-6"},$r={key:0,class:"p-4 bg-green-50 border border-green-200 rounded-lg"},Or={class:"text-green-800"},Er={key:1,class:"p-4 bg-red-50 border border-red-200 rounded-lg"},Tr={class:"text-red-800"},Rr={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Br={class:"space-y-2"},kr={class:"space-y-2"},Dr={class:"space-y-2"},Mr={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ir={class:"space-y-2"},Vr={class:"space-y-2"},Lr={key:0,class:"p-3 bg-blue-50 rounded-lg"},zr={class:"text-sm text-blue-800"},Fr={class:"flex justify-end"},Wr={key:0,class:"fas fa-spinner fa-spin mr-2"},Nr={key:1,class:"fas fa-save mr-2"},ti=M({__name:"AppointmentPreferences",setup(e){const t=E(!1),n=E(!1),o=E(""),r=E(""),i=kn({preferred_location:"",preferred_gender:"",preferred_language:"",latitude:null,longitude:null,search_radius:"25"}),a=async()=>{t.value=!0;try{const s=(await At.get("/api/patient/appointment-preferences")).data.appointment_preferences;i.preferred_location=s.preferred_location||"",i.preferred_gender=s.preferred_gender||"",i.preferred_language=s.preferred_language||"",i.latitude=s.latitude||null,i.longitude=s.longitude||null,i.search_radius=s.search_radius||"25"}catch(u){console.error("Error loading preferences:",u),r.value="Failed to load appointment preferences"}finally{t.value=!1}},l=async()=>{var u,s;n.value=!0,r.value="",o.value="";try{await At.put("/api/patient/appointment-preferences",i.data()),o.value="Appointment preferences updated successfully!"}catch(d){console.error("Error saving preferences:",d),r.value=((s=(u=d.response)==null?void 0:u.data)==null?void 0:s.message)||"Failed to save preferences"}finally{n.value=!1}},f=()=>{navigator.geolocation?navigator.geolocation.getCurrentPosition(u=>{i.latitude=u.coords.latitude,i.longitude=u.coords.longitude,o.value="Location updated successfully!"},u=>{console.error("Error getting location:",u),r.value="Failed to get current location"}):r.value="Geolocation is not supported by this browser"};return ge(()=>{a()}),(u,s)=>(T(),F(Jt,null,[P(c(Dn),{title:"Appointment Preferences"}),P(Vn,null,{default:b(()=>[z("div",Ar,[z("div",null,[P(Mn,null,{default:b(()=>s[4]||(s[4]=[D("Appointment Preferences")])),_:1}),s[5]||(s[5]=z("p",{class:"text-muted-foreground"}," Set your preferences for booking appointments with healthcare providers. ",-1))]),P(c(In)),t.value?(T(),F("div",Sr,s[6]||(s[6]=[z("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(T(),F("div",Pr,[o.value?(T(),F("div",$r,[z("p",Or,xe(o.value),1)])):_e("",!0),r.value?(T(),F("div",Er,[z("p",Tr,xe(r.value),1)])):_e("",!0),z("form",{onSubmit:Ae(l,["prevent"]),class:"space-y-6"},[P(c(Ht),null,{default:b(()=>[P(c(Kt),null,{default:b(()=>[P(c(qt),null,{default:b(()=>s[7]||(s[7]=[D("Provider Preferences")])),_:1}),P(c(Ut),null,{default:b(()=>s[8]||(s[8]=[D(" Choose your preferred healthcare provider characteristics. ")])),_:1})]),_:1}),P(c(jt),{class:"space-y-4"},{default:b(()=>[z("div",Rr,[z("div",Br,[P(c(Be),{for:"preferred_gender"},{default:b(()=>s[9]||(s[9]=[D("Preferred Provider Gender")])),_:1}),P(c(nt),{modelValue:c(i).preferred_gender,"onUpdate:modelValue":s[0]||(s[0]=d=>c(i).preferred_gender=d)},{default:b(()=>[P(c(rt),null,{default:b(()=>[P(c(it),{placeholder:"Select gender preference"})]),_:1}),P(c(ot),null,{default:b(()=>[P(c(j),{value:"any"},{default:b(()=>s[10]||(s[10]=[D("No Preference")])),_:1}),P(c(j),{value:"male"},{default:b(()=>s[11]||(s[11]=[D("Male")])),_:1}),P(c(j),{value:"female"},{default:b(()=>s[12]||(s[12]=[D("Female")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),z("div",kr,[P(c(Be),{for:"preferred_language"},{default:b(()=>s[13]||(s[13]=[D("Preferred Language")])),_:1}),P(c(nt),{modelValue:c(i).preferred_language,"onUpdate:modelValue":s[1]||(s[1]=d=>c(i).preferred_language=d)},{default:b(()=>[P(c(rt),null,{default:b(()=>[P(c(it),{placeholder:"Select language"})]),_:1}),P(c(ot),null,{default:b(()=>[P(c(j),{value:"english"},{default:b(()=>s[14]||(s[14]=[D("English")])),_:1}),P(c(j),{value:"spanish"},{default:b(()=>s[15]||(s[15]=[D("Spanish")])),_:1}),P(c(j),{value:"french"},{default:b(()=>s[16]||(s[16]=[D("French")])),_:1}),P(c(j),{value:"german"},{default:b(()=>s[17]||(s[17]=[D("German")])),_:1}),P(c(j),{value:"italian"},{default:b(()=>s[18]||(s[18]=[D("Italian")])),_:1}),P(c(j),{value:"portuguese"},{default:b(()=>s[19]||(s[19]=[D("Portuguese")])),_:1}),P(c(j),{value:"chinese"},{default:b(()=>s[20]||(s[20]=[D("Chinese")])),_:1}),P(c(j),{value:"arabic"},{default:b(()=>s[21]||(s[21]=[D("Arabic")])),_:1})]),_:1})]),_:1},8,["modelValue"])])])]),_:1})]),_:1}),P(c(Ht),null,{default:b(()=>[P(c(Kt),null,{default:b(()=>[P(c(qt),null,{default:b(()=>s[22]||(s[22]=[D("Location Preferences")])),_:1}),P(c(Ut),null,{default:b(()=>s[23]||(s[23]=[D(" Set your preferred location and search radius for finding providers. ")])),_:1})]),_:1}),P(c(jt),{class:"space-y-4"},{default:b(()=>[z("div",Dr,[P(c(Be),{for:"preferred_location"},{default:b(()=>s[24]||(s[24]=[D("Preferred Location")])),_:1}),P(c(zn),{id:"preferred_location",modelValue:c(i).preferred_location,"onUpdate:modelValue":s[2]||(s[2]=d=>c(i).preferred_location=d),placeholder:"Enter city, state, or address"},null,8,["modelValue"])]),z("div",Mr,[z("div",Ir,[P(c(Be),{for:"search_radius"},{default:b(()=>s[25]||(s[25]=[D("Search Radius (miles)")])),_:1}),P(c(nt),{modelValue:c(i).search_radius,"onUpdate:modelValue":s[3]||(s[3]=d=>c(i).search_radius=d)},{default:b(()=>[P(c(rt),null,{default:b(()=>[P(c(it),{placeholder:"Select radius"})]),_:1}),P(c(ot),null,{default:b(()=>[P(c(j),{value:"5"},{default:b(()=>s[26]||(s[26]=[D("5 miles")])),_:1}),P(c(j),{value:"10"},{default:b(()=>s[27]||(s[27]=[D("10 miles")])),_:1}),P(c(j),{value:"25"},{default:b(()=>s[28]||(s[28]=[D("25 miles")])),_:1}),P(c(j),{value:"50"},{default:b(()=>s[29]||(s[29]=[D("50 miles")])),_:1}),P(c(j),{value:"100"},{default:b(()=>s[30]||(s[30]=[D("100 miles")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),z("div",Vr,[P(c(Be),null,{default:b(()=>s[31]||(s[31]=[D("Current Location")])),_:1}),P(c(St),{type:"button",variant:"outline",onClick:f,class:"w-full"},{default:b(()=>s[32]||(s[32]=[z("i",{class:"fas fa-location-arrow mr-2"},null,-1),D(" Use Current Location ")])),_:1})])]),c(i).latitude&&c(i).longitude?(T(),F("div",Lr,[z("p",zr,[s[33]||(s[33]=z("i",{class:"fas fa-map-marker-alt mr-2"},null,-1)),D(" Location set: "+xe(c(i).latitude.toFixed(4))+", "+xe(c(i).longitude.toFixed(4)),1)])])):_e("",!0)]),_:1})]),_:1}),z("div",Fr,[P(c(St),{type:"submit",disabled:n.value,class:"min-w-[120px]"},{default:b(()=>[n.value?(T(),F("i",Wr)):(T(),F("i",Nr)),D(" "+xe(n.value?"Saving...":"Save Preferences"),1)]),_:1},8,["disabled"])])],32)]))])]),_:1})],64))}});export{ti as default};
